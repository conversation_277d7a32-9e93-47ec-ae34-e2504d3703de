/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import React from 'react';
import DropdownIcon from '@/assests/images/dropdownIcon.svg';

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  register?: any;
  name: string;
  placeholder: string;
  options: Option[];
  onChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  register,
  name,
  placeholder,
  options,
  onChange,
}) => {
  return (
    <div className="w-full">
      <div className="relative">
        <select
          {...(register ? register(name) : {})}
          name={name}
          className="w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 pr-12 font-nunito text-sm text-customBlueSecondary ring-offset-white focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
          onChange={onChange}
        >
          <option value="">{placeholder}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="pointer-events-none absolute right-0 top-1/2 flex h-full w-10 -translate-y-1/2 transform items-center justify-center rounded-r-md border-b border-r border-t bg-customLight">
          <DropdownIcon />
        </div>
      </div>
    </div>
  );
};

export default CustomSelect;
