'use client';

import * as React from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export type DashboardPolicy = {
  id: number;
  policyId: string;
  name: string;
  department: string;
  categories: string[];
  version: string;
  classification: string;
  priorityScore: number;
  documentCode?: string;
};

const columnHelper = createColumnHelper<DashboardPolicy>();

export const columns = [
  // 1. Policy Name - Main identifier
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-gray-900">Policy Name</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="space-y-1">
        <div
          className="max-w-[200px] truncate font-medium text-gray-900"
          title={row.getValue('name')}
        >
          {row.getValue('name')}
        </div>
        <div className="text-sm text-gray-500">
          {row.original.documentCode || 'N/A'}
        </div>
      </div>
    ),
    enableHiding: false,
  }),

  // 2. Department
  columnHelper.accessor('department', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-gray-900">Department</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-gray-700">
        {row.getValue('department')}
      </div>
    ),
    enableHiding: false,
  }),

  // 3. Categories
  columnHelper.accessor('categories', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-gray-900">Categories</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const categories = row.getValue('categories') as string[];
      return (
        <div className="flex flex-wrap gap-1">
          {categories.slice(0, 2).map((category, idx) => (
            <span
              key={idx}
              className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
            >
              {category}
            </span>
          ))}
          {categories.length > 2 && (
            <span className="text-xs text-gray-500">
              +{categories.length - 2}
            </span>
          )}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 4. Version
  columnHelper.accessor('version', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-gray-900">Version</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="font-mono text-sm font-medium text-gray-700">
        {row.getValue('version')}
      </div>
    ),
    enableHiding: false,
  }),

  // 5. Classification
  columnHelper.accessor('classification', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-gray-900">Classification</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const classification = row.getValue('classification') as string;
      const getClassificationColor = (classification: string) => {
        switch (classification) {
          case 'Top Secret':
            return 'text-red-700 bg-red-100';
          case 'Restricted':
            return 'text-red-600 bg-red-50';
          case 'Confidential':
            return 'text-orange-600 bg-orange-50';
          case 'Internal':
            return 'text-blue-600 bg-blue-50';
          case 'Public':
            return 'text-green-600 bg-green-50';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <span
          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getClassificationColor(classification)}`}
        >
          {classification}
        </span>
      );
    },
    enableHiding: false,
  }),

  // 6. Priority Score
  columnHelper.accessor('priorityScore', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-gray-900">Priority</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-gray-600" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const score = row.getValue('priorityScore') as number;
      return (
        <div className="flex items-center">
          <span className="text-sm font-medium text-gray-900">
            {score || 5}
          </span>
          <span className="ml-1 text-xs text-gray-500">/10</span>
        </div>
      );
    },
    enableHiding: false,
  }),
];
