#!/bin/bash

# Quick fix for upload directory permissions
# Run this if you encounter EACCES errors when creating documents

echo "🔧 Fixing upload directory permissions..."

# Set host directory permissions
echo "Setting host directory permissions..."
chmod -R 777 backend/uploads 2>/dev/null || echo "Could not set host permissions"
mkdir -p backend/uploads/policies 2>/dev/null || true
chmod -R 777 backend/uploads/policies 2>/dev/null || true

# Get the backend container name
BACKEND_CONTAINER=$(docker ps --filter "name=grc-backend" --format "{{.Names}}" | head -1)

if [[ -z "$BACKEND_CONTAINER" ]]; then
    echo "❌ Backend container not found. Make sure the application is running."
    exit 1
fi

echo "Found backend container: $BACKEND_CONTAINER"

# Fix container permissions using multiple methods
echo "Setting container permissions..."

# Method 1: Regular user
if docker exec "$BACKEND_CONTAINER" chmod -R 777 /app/uploads 2>/dev/null; then
    echo "✅ Permissions set successfully"
else
    echo "Trying with root user..."
    # Method 2: Root user
    if docker exec -u root "$BACKEND_CONTAINER" chmod -R 777 /app/uploads 2>/dev/null; then
        echo "✅ Permissions set successfully (root method)"
    else
        echo "❌ Failed to set permissions"
        exit 1
    fi
fi

# Ensure policies directory exists
docker exec "$BACKEND_CONTAINER" mkdir -p /app/uploads/policies 2>/dev/null || true
docker exec "$BACKEND_CONTAINER" chmod -R 777 /app/uploads/policies 2>/dev/null || true

# Test file creation
echo "Testing file creation..."
if docker exec "$BACKEND_CONTAINER" touch /app/uploads/test-file 2>/dev/null; then
    echo "✅ File creation test passed"
    docker exec "$BACKEND_CONTAINER" rm /app/uploads/test-file 2>/dev/null || true
else
    echo "❌ File creation test failed"
    exit 1
fi

echo "✅ Upload directory permissions fixed successfully!"
echo "You can now try creating documents again."