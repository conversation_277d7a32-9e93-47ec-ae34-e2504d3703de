const fs = require('fs-extra');
const path = require('path');

// Get MIME type based on file extension
const getMimeType = (extension) => {
  const mimeTypes = {
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'pdf': 'application/pdf',
    'txt': 'text/plain'
  };
  return mimeTypes[extension] || 'application/octet-stream';
};

// Create document from template
const createDocumentFromTemplate = async (filePath, type, title) => {
  // Path to templates directory
  const templatesDir = path.resolve(__dirname, '../templates');
  
  // Ensure templates directory exists
  await fs.ensureDir(templatesDir);
  
  // Template file path
  const templatePath = path.join(templatesDir, `template.${type}`);
  
  // If template exists, copy it, otherwise create empty file
  if (await fs.pathExists(templatePath)) {
    await fs.copy(templatePath, filePath);
  } else {
    // Create empty file based on type
    if (type === 'txt') {
      await fs.writeFile(filePath, `# ${title}\n\nDocument created on ${new Date().toLocaleDateString()}`);
    } else {
      // For other types, create an empty file
      await fs.writeFile(filePath, '');
    }
  }
  
  return filePath;
};

module.exports = {
  getMimeType,
  createDocumentFromTemplate
};
