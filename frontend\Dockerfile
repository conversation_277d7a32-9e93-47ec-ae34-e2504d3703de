# Stage 1: Build the Next.js application
FROM node:18-alpine AS builder
 
WORKDIR /app

# Accept build arguments for environment variables
ARG NODE_ENV=production
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_SOCKET_URL
ARG NEXT_PUBLIC_ONLYOFFICE_URL
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_VERSION
ARG NEXT_PUBLIC_DOMAIN
ARG YARN_NETWORK_TIMEOUT
ARG YARN_NETWORK_CONCURRENCY

# Set environment variables for the build
ENV NODE_ENV=$NODE_ENV
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_SOCKET_URL=$NEXT_PUBLIC_SOCKET_URL
ENV NEXT_PUBLIC_ONLYOFFICE_URL=$NEXT_PUBLIC_ONLYOFFICE_URL
ENV NEXT_PUBLIC_APP_NAME=$NEXT_PUBLIC_APP_NAME
ENV NEXT_PUBLIC_APP_VERSION=$NEXT_PUBLIC_APP_VERSION
ENV NEXT_PUBLIC_DOMAIN=$NEXT_PUBLIC_DOMAIN

# Configure yarn for network issues
ENV YARN_NETWORK_TIMEOUT=$YARN_NETWORK_TIMEOUT
ENV YARN_NETWORK_CONCURRENCY=$YARN_NETWORK_CONCURRENCY
 
# Copy the package.json and yarn.lock to install dependencies
COPY package.json yarn.lock ./

# Install all dependencies including devDependencies for build
# Explicitly set NODE_ENV to development to ensure devDependencies are installed
ENV NODE_ENV=development
RUN yarn install --frozen-lockfile

# Verify tailwindcss is installed
RUN ls -la node_modules/.bin/ | grep tailwind || echo "TailwindCSS not found in .bin"
RUN yarn list tailwindcss || echo "TailwindCSS not in yarn list"

# Update browserslist database
RUN npx update-browserslist-db@latest

# Reset NODE_ENV to production for build
ENV NODE_ENV=production
 
# Copy the rest of the application files and build the Next.js app
COPY . .
RUN yarn build
 
# Stage 2: Serve the Next.js application
FROM node:18-alpine
 
WORKDIR /app

# Re-declare build arguments for the runtime stage
ARG NODE_ENV=production
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_SOCKET_URL
ARG NEXT_PUBLIC_ONLYOFFICE_URL
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_VERSION
ARG NEXT_PUBLIC_DOMAIN

# Set environment variables for runtime
ENV NODE_ENV=$NODE_ENV
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_SOCKET_URL=$NEXT_PUBLIC_SOCKET_URL
ENV NEXT_PUBLIC_ONLYOFFICE_URL=$NEXT_PUBLIC_ONLYOFFICE_URL
ENV NEXT_PUBLIC_APP_NAME=$NEXT_PUBLIC_APP_NAME
ENV NEXT_PUBLIC_APP_VERSION=$NEXT_PUBLIC_APP_VERSION
ENV NEXT_PUBLIC_DOMAIN=$NEXT_PUBLIC_DOMAIN

# Copy package files for production dependencies
COPY --from=builder /app/package.json /app/yarn.lock ./

# Install only production dependencies with network optimizations
ENV YARN_NETWORK_TIMEOUT=300000
ENV YARN_NETWORK_CONCURRENCY=1
RUN yarn install --production --frozen-lockfile --network-timeout 300000 && yarn cache clean
 
# Copy the built application from the builder stage
COPY --from=builder /app/.next ./.next

# Create empty public directory since it doesn't exist in the project
RUN mkdir -p ./public

# Copy next config if it exists
COPY --from=builder /app/next.config.mjs ./next.config.mjs

# Copy package.json for runtime
COPY --from=builder /app/package.json ./package.json
 
# Expose port 3000 to be used by Docker
EXPOSE 3000
 
# Start the Next.js application
CMD ["yarn", "start"]
 