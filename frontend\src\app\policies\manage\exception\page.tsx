'use client';

import { useState, useEffect } from 'react';
import { DataTable } from './datatable';
import { columns, ExceptionPrograms } from './column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyExceptionModal } from '@/components/PolicyExceptionModal';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';
import {
  FileWarning,
  Clock,
  CheckCircle,
  XCircle,
  BarChart3,
} from 'lucide-react';

const Exception = () => {
  const [isExceptionModalOpen, setIsExceptionModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch policies with exception-related statuses
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    requestException,
    exceptionGovernanceReview,
    approveException,
    rejectException,
  } = usePolicies({
    autoFetch: false,
    management: true,
    // Only fetch published policies since exceptions are tracked separately
    status: 'Published',
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Update selected policy when policies data changes (for real-time updates)
  useEffect(() => {
    if (selectedPolicy && policies.length > 0) {
      const updatedPolicy = policies.find((p) => p._id === selectedPolicy._id);
      if (updatedPolicy) {
        setSelectedPolicy(updatedPolicy);
      }
    }
  }, [policies, selectedPolicy]);

  // Filter policies for exception workflow (only policies with active exception statuses, excluding Published with no exceptions)
  const exceptionPolicies = policies.filter(
    (policy) =>
      policy.status === 'Published' &&
      policy.exceptionStatus &&
      policy.exceptionStatus !== 'None' &&
      [
        'Requested',
        'Under Review',
        'Pending Approval',
        'Approved',
        'Rejected',
        'Expired',
      ].includes(policy.exceptionStatus),
  );

  // Transform policies to match ExceptionPrograms interface
  const transformedData: ExceptionPrograms[] = exceptionPolicies.map(
    (policy, index) => ({
      id: index + 1,
      policyId: policy.policyId,
      documentCode: policy.documentCode || '',
      name: policy.name,
      documentName: policy.documentName || policy.name,
      documentType: policy.documentType || 'Policy',
      version: policy.version || '1.0',
      policyType: policy.policyType || '',
      categories: policy.categories?.join(', ') || '',
      department: policy.department,
      subDepartment: policy.subDepartment || '',
      policyOwner: policy.policyOwner?.name || '',
      priorityScore: policy.priorityScore || 5,
      status: policy.status,
      detailedStatus: `${policy.status}${policy.exceptionStatus && policy.exceptionStatus !== 'None' ? ` (Exception ${policy.exceptionStatus})` : ''}`,
      exceptionStatus: policy.exceptionStatus || '',
      classification: policy.classification || 'Internal',
      publishedDate: policy.publishedDate || '',
      exceptionRequestedDate: policy.exceptionRequestedDate || '',
      exceptionApprovedDate: policy.exceptionApprovedDate || '',
      exceptionJustification: policy.exceptionRequest?.justification || '',
      exceptionSpecificSection: policy.exceptionRequest?.specificSection || '',
      exceptionType: policy.exceptionRequest?.exceptionType || '',
      exceptionEffectiveDate: policy.exceptionRequest?.effectiveDate || '',
      exceptionExpiryDate: policy.exceptionRequest?.expiryDate || '',
      exceptionRequestedBy: policy.exceptionRequest?.requestedBy?.name || '',
    }),
  );

  // Handle policy exception request submission (from modal)
  const handleExceptionRequest = async (exceptionData: {
    justification: string;
    specificSection: string;
    exceptionType: 'Material Exception' | 'Immaterial Exception';
    effectiveDate?: string;
    expiryDate?: string;
    comments?: string;
  }) => {
    if (!selectedPolicy) {
      toast({
        title: 'Error',
        description: 'No policy selected for exception request',
        variant: 'destructive',
      });
      return;
    }

    try {
      const success = await requestException(
        selectedPolicy._id,
        exceptionData.justification,
        exceptionData.specificSection,
        exceptionData.exceptionType,
        exceptionData.effectiveDate,
        exceptionData.expiryDate,
        exceptionData.comments,
      );

      if (success) {
        toast({
          title: 'Success',
          description: 'Policy exception request submitted successfully',
        });
        // Refresh policies after exception request
        refetchPolicies();
        setIsExceptionModalOpen(false);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to submit exception request. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error requesting exception:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to submit exception request';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle exception submission from modal (with policy selection)
  const handleExceptionSubmit = async (data: {
    policyId: string;
    justification: string;
    specificSection: string;
    exceptionType: 'Material Exception' | 'Immaterial Exception';
    effectiveDate?: string;
    expiryDate?: string;
    comments?: string;
  }) => {
    try {
      const success = await requestException(
        data.policyId,
        data.justification,
        data.specificSection,
        data.exceptionType,
        data.effectiveDate,
        data.expiryDate,
        data.comments,
      );

      if (success) {
        toast({
          title: 'Success',
          description: 'Exception request submitted successfully',
        });
        setIsExceptionModalOpen(false);
        refetchPolicies();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to submit exception request',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An error occurred while submitting the exception request',
        variant: 'destructive',
      });
    }
  };

  // Handle policy row click to open details modal
  const handlePolicyClick = (row: ExceptionPrograms) => {
    // Find the original policy by policyId
    const policy = policies.find((p) => p.policyId === row.policyId);
    if (policy) {
      setSelectedPolicy(policy);
      setIsDetailsModalOpen(true);
    }
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle policy update (refresh policy data and update selected policy)
  const handlePolicyUpdate = () => {
    refetchPolicies();
  };

  // Handle exception governance review from details modal
  const handleExceptionGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await exceptionGovernanceReview(
      policyId,
      decision,
      comments,
    );
    refetchPolicies();
    return result;
  };

  // Handle approve exception from details modal
  const handleApproveException = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveException(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle reject exception from details modal
  const handleRejectException = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectException(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle request exception from details modal
  const handleRequestException = async (
    policyId: string,
    justification: string,
    specificSection: string,
    exceptionType: 'Material Exception' | 'Immaterial Exception',
    effectiveDate?: string,
    expiryDate?: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await requestException(
      policyId,
      justification,
      specificSection,
      exceptionType,
      effectiveDate,
      expiryDate,
      comments,
    );
    refetchPolicies();
    return result;
  };

  // Exception policies are already filtered, so we can use them directly for the table

  // Exception workflow status cards based on exceptionStatus (excluding Published with no exceptions)
  const exceptionStatusCards = [
    {
      title: 'Exception Requested',
      number: exceptionPolicies
        .filter((p) => p.exceptionStatus === 'Requested')
        .length.toString(),
      icon: FileWarning,
      borderColor: 'border-l-orange-500',
      numberColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Policies with pending exception requests',
    },
    {
      title: 'Exception Pending Approval',
      number: exceptionPolicies
        .filter((p) => p.exceptionStatus === 'Pending Approval')
        .length.toString(),
      icon: Clock,
      borderColor: 'border-l-yellow-500',
      numberColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: 'Exception requests awaiting final approval',
    },
    {
      title: 'Exception Approved',
      number: exceptionPolicies
        .filter((p) => p.exceptionStatus === 'Approved')
        .length.toString(),
      icon: CheckCircle,
      borderColor: 'border-l-blue-500',
      numberColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Successfully approved exceptions',
    },
    {
      title: 'Exception Rejected',
      number: exceptionPolicies
        .filter((p) => p.exceptionStatus === 'Rejected')
        .length.toString(),
      icon: XCircle,
      borderColor: 'border-l-red-500',
      numberColor: 'text-red-600',
      bgColor: 'bg-red-50',
      description: 'Rejected exception requests',
    },
  ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading policy exception data..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="w-full bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Policies"
          message="We couldn't load the policy exception data. Please try again or contact support if the problem persists."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="w-full bg-gray-100 py-2">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">
              Policy Exception
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage policy exception requests and track exception workflow
            </p>
          </div>
          {/* Only show Request Exception button for policy owners, admins, and super admins */}
          {user && ['Creator', 'Admin', 'Super Admin'].includes(user.role) && (
            <Button
              onClick={() => setIsExceptionModalOpen(true)}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Request Policy Exception
            </Button>
          )}
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-purple-500 bg-gradient-to-r from-purple-50 to-indigo-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-purple-700">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-6 w-6" />
                  <span>Policy Exception Overview</span>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-3xl font-bold text-purple-700">
                    {policiesLoading ? '...' : exceptionPolicies.length}
                  </div>
                  <div className="text-sm text-gray-500">
                    exception-related policies
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Policies currently in the exception workflow
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Exception Status Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Exception Workflow Overview
          </h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {exceptionStatusCards.map((card, index) => (
              <Card
                key={index}
                className={`${card.borderColor} cursor-pointer border-l-4 transition-shadow hover:shadow-md ${card.bgColor}`}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                    <div className="flex items-center space-x-2">
                      <card.icon className="h-4 w-4" />
                      <span className="truncate">{card.title}</span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-col space-y-2">
                    <p className={`text-2xl font-bold ${card.numberColor}`}>
                      {policiesLoading ? '...' : card.number}
                    </p>
                    <p className="text-xs leading-tight text-gray-500">
                      {card.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedData}
            loading={policiesLoading}
            onRowClick={handlePolicyClick}
          />
        </div>
      </div>

      <PolicyExceptionModal
        isOpen={isExceptionModalOpen}
        onClose={() => setIsExceptionModalOpen(false)}
        policies={policies}
        onSubmit={handleExceptionSubmit}
        isSubmitting={policiesLoading}
      />

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={() => Promise.resolve(false)} // Not used in exception workflow
        onGovernanceReview={() => Promise.resolve(false)} // Not used in exception workflow
        onGrcReview={() => Promise.resolve(false)} // Not used in exception workflow
        onRequestException={handleRequestException}
        onExceptionGovernanceReview={handleExceptionGovernanceReview}
        onApproveException={handleApproveException}
        onRejectException={handleRejectException}
        onPolicyUpdate={handlePolicyUpdate}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default Exception;
