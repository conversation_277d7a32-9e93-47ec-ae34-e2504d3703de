'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface CustomDateProps {
  placeholder: string;
  value?: Date | undefined;
  onChange?: (date: Date | undefined) => void;
}

const DateInput: React.FC<CustomDateProps> = ({
  placeholder,
  value,
  onChange,
}) => {
  // Use value directly, but initialize it with an empty state if undefined
  const [internalDate, setInternalDate] = React.useState<Date | undefined>(
    value,
  );

  // When the value changes externally, update internal state
  React.useEffect(() => {
    if (value !== internalDate) {
      setInternalDate(value);
    }
  }, [value, internalDate]);

  // Handler to set date and notify parent component
  const setDate = (newDate: Date | undefined) => {
    setInternalDate(newDate);
    onChange?.(newDate); // Pass the new value to parent
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'relative w-full justify-start text-left font-normal',
            !internalDate && 'text-muted-foreground',
          )}
        >
          <CalendarIcon />
          {internalDate ? (
            format(internalDate, 'PPP')
          ) : (
            <span>{placeholder || 'Pick a date'}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={internalDate}
          onSelect={setDate}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
};

export default DateInput;
