const Notification = require("../models/Notification");
const User = require("../models/User");

class NotificationService {
  constructor(io = null) {
    this.io = io; // Socket.IO instance for real-time notifications
  }

  // Set Socket.IO instance
  setSocketIO(io) {
    this.io = io;
  }

  // Create and send notification
  async createNotification(notificationData) {
    try {
      const notification =
        await Notification.createNotification(notificationData);

      // Send real-time notification if WebSocket is available
      if (this.io) {
        this.io
          .to(`user_${notification.recipient.id}`)
          .emit("new_notification", notification);
      }

      return notification;
    } catch (error) {
      console.error("Failed to create notification:", error);
      throw error;
    }
  }

  // Send notification to multiple users
  async createBulkNotifications(recipients, notificationTemplate) {
    try {
      const notifications = [];

      for (const recipient of recipients) {
        const notificationData = {
          ...notificationTemplate,
          recipient: {
            id: recipient._id,
            email: recipient.email,
            role: recipient.role,
          },
        };

        const notification = await this.createNotification(notificationData);
        notifications.push(notification);
      }

      return notifications;
    } catch (error) {
      console.error("Failed to create bulk notifications:", error);
      throw error;
    }
  }

  // Policy workflow notification handlers
  async notifyPolicyRequestInitiated(policy, initiator) {
    try {
      // Find governance approvers (Approvers from Governance department + Admins + Super Admins)
      const governanceApprovers = await User.find({
        $or: [
          { role: "Approver", department: "Governance", isActive: true },
          { role: { $in: ["Admin", "Super Admin"] }, isActive: true },
        ],
      });

      const notificationTemplate = {
        type: "policy_workflow",
        category: "approval_required",
        title: "New Policy Request for Review",
        message: `${policy.name} has been submitted for governance review`,
        priority: "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: true,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: initiator._id,
          name: initiator.name,
          email: initiator.email,
        },
      };

      return await this.createBulkNotifications(
        governanceApprovers,
        notificationTemplate
      );
    } catch (error) {
      console.error("Failed to notify policy request initiated:", error);
    }
  }

  async notifyGovernanceReview(policy, reviewer, decision, policyOwner) {
    try {
      const isApproved = decision === "Approved";

      const notificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: `Policy Request ${decision}`,
        message: `Your policy request "${policy.name}" has been ${decision.toLowerCase()} by governance review`,
        priority: isApproved ? "medium" : "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Request Initiated",
          newStatus: isApproved ? "Draft" : "Request Initiated",
          actionRequired: isApproved,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(notificationData);
    } catch (error) {
      console.error("Failed to notify governance review:", error);
    }
  }

  async notifyGRCReview(policy, reviewer, decision, policyOwner) {
    try {
      const isApproved = decision === "Approved";

      if (isApproved) {
        // Notify approvers (CEO/Board Committee based on policy category)
        const approvers = await this.getApproversForPolicy(policy);

        const approverNotificationTemplate = {
          type: "policy_workflow",
          category: "approval_required",
          title: "Policy Pending Final Approval",
          message: `${policy.name} has passed GRC review and requires final approval`,
          priority: "high",
          data: {
            policyId: policy._id,
            policyName: policy.name,
            actionRequired: true,
            actionUrl: `/policies/manage/${policy._id}`,
          },
          sender: {
            id: reviewer._id,
            name: reviewer.name,
            email: reviewer.email,
          },
        };

        await this.createBulkNotifications(
          approvers,
          approverNotificationTemplate
        );
      }

      // Notify policy owner
      const ownerNotificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: `GRC Review ${decision}`,
        message: `Your policy "${policy.name}" has been ${decision.toLowerCase()} by GRC review`,
        priority: isApproved ? "medium" : "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Under Review",
          newStatus: isApproved ? "Pending Approval" : "Draft",
          actionRequired: !isApproved,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(ownerNotificationData);
    } catch (error) {
      console.error("Failed to notify GRC review:", error);
    }
  }

  async notifyPolicyApproval(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      const ownerNotification = await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Approved",
        message: `Your policy "${policy.name}" has been approved and is ready for publication`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Pending Approval",
          newStatus: "Approved",
          actionRequired: false,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });

      // Notify publishers
      const publishers = await User.find({
        role: { $in: ["Publisher", "Admin", "Super Admin"] },
        isActive: true,
      });

      const publisherNotificationTemplate = {
        type: "policy_workflow",
        category: "assignment",
        title: "Policy Ready for Publication",
        message: `${policy.name} has been approved and is ready for publication`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: true,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      };

      await this.createBulkNotifications(
        publishers,
        publisherNotificationTemplate
      );

      return ownerNotification;
    } catch (error) {
      console.error("Failed to notify policy approval:", error);
    }
  }

  async notifyPolicyPublished(policy, publisher) {
    try {
      // Notify policy owner
      const policyOwner = await User.findById(policy.policyOwner.id);

      if (policyOwner) {
        await this.createNotification({
          recipient: {
            id: policyOwner._id,
            email: policyOwner.email,
            role: policyOwner.role,
          },
          type: "policy_workflow",
          category: "status_change",
          title: "Policy Published",
          message: `Your policy "${policy.name}" has been successfully published`,
          priority: "low",
          data: {
            policyId: policy._id,
            policyName: policy.name,
            previousStatus: "Approved",
            newStatus: "Published",
            actionRequired: false,
            actionUrl: `/policies/manage/${policy._id}`,
          },
          sender: {
            id: publisher._id,
            name: publisher.name,
            email: publisher.email,
          },
        });
      }

      // Notify relevant department users
      const departmentUsers = await User.find({
        department: policy.department,
        isActive: true,
        _id: { $ne: policy.policyOwner.id }, // Exclude policy owner (already notified)
      });

      const departmentNotificationTemplate = {
        type: "policy_workflow",
        category: "status_change",
        title: "New Policy Published",
        message: `${policy.name} has been published and is now effective`,
        priority: "low",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: false,
          actionUrl: `/policies/view/${policy._id}`,
        },
        sender: {
          id: publisher._id,
          name: publisher.name,
          email: publisher.email,
        },
      };

      return await this.createBulkNotifications(
        departmentUsers,
        departmentNotificationTemplate
      );
    } catch (error) {
      console.error("Failed to notify policy published:", error);
    }
  }

  // Policy retirement notification handlers
  async notifyPolicyRetirementRequested(policy, initiator) {
    try {
      // Find governance approvers (Approvers from Governance department + Admins + Super Admins)
      const governanceApprovers = await User.find({
        $or: [
          { role: "Approver", department: "Governance", isActive: true },
          { role: { $in: ["Admin", "Super Admin"] }, isActive: true },
        ],
      });

      const notificationTemplate = {
        type: "policy_workflow",
        category: "approval_required",
        title: "Policy Retirement Request for Review",
        message: `${policy.name} has been submitted for retirement review`,
        priority: "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: true,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: initiator._id,
          name: initiator.name,
          email: initiator.email,
        },
      };

      return await this.createBulkNotifications(
        governanceApprovers,
        notificationTemplate
      );
    } catch (error) {
      console.error("Failed to notify policy retirement requested:", error);
    }
  }

  async notifyRetirementGovernanceReview(
    policy,
    reviewer,
    decision,
    policyOwner
  ) {
    try {
      const isApproved = decision === "Approved";

      if (isApproved) {
        // Notify final approvers based on policy category
        const approvers = await this.getApproversForPolicy(policy);

        const approverNotificationTemplate = {
          type: "policy_workflow",
          category: "approval_required",
          title: "Policy Retirement Pending Final Approval",
          message: `${policy.name} retirement has passed governance review and requires final approval`,
          priority: "high",
          data: {
            policyId: policy._id,
            policyName: policy.name,
            actionRequired: true,
            actionUrl: `/policies/manage/${policy._id}`,
          },
          sender: {
            id: reviewer._id,
            name: reviewer.name,
            email: reviewer.email,
          },
        };

        await this.createBulkNotifications(
          approvers,
          approverNotificationTemplate
        );
      }

      // Notify policy owner
      const ownerNotificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: `Retirement Governance Review ${decision}`,
        message: `Your policy retirement request for "${policy.name}" has been ${decision.toLowerCase()} by governance review`,
        priority: isApproved ? "medium" : "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Retirement Requested",
          newStatus: isApproved ? "Retirement Pending Approval" : "Published",
          actionRequired: !isApproved,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(ownerNotificationData);
    } catch (error) {
      console.error("Failed to notify retirement governance review:", error);
    }
  }

  async notifyPolicyRetired(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      const ownerNotification = await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Retired",
        message: `Your policy "${policy.name}" has been successfully retired`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Retirement Pending Approval",
          newStatus: "Retired",
          actionRequired: false,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });

      // Notify relevant department users
      const departmentUsers = await User.find({
        department: policy.department,
        isActive: true,
        _id: { $ne: policy.policyOwner.id }, // Exclude policy owner (already notified)
      });

      const departmentNotificationTemplate = {
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Retired",
        message: `${policy.name} has been retired and is no longer active`,
        priority: "low",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: false,
          actionUrl: `/policies/view/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      };

      await this.createBulkNotifications(
        departmentUsers,
        departmentNotificationTemplate
      );

      return ownerNotification;
    } catch (error) {
      console.error("Failed to notify policy retired:", error);
    }
  }

  async notifyRetirementRejected(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      return await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Retirement Rejected",
        message: `Your retirement request for "${policy.name}" has been rejected - policy remains active`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Retirement Pending Approval",
          newStatus: "Published",
          actionRequired: false,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });
    } catch (error) {
      console.error("Failed to notify retirement rejected:", error);
    }
  }

  // Policy exception notification handlers
  async notifyPolicyExceptionRequested(policy, initiator) {
    try {
      // Find governance approvers (Approvers from Governance department + Admins + Super Admins)
      const governanceApprovers = await User.find({
        $or: [
          { role: "Approver", department: "Governance", isActive: true },
          { role: { $in: ["Admin", "Super Admin"] }, isActive: true },
        ],
      });

      const notificationTemplate = {
        type: "policy_workflow",
        category: "approval_required",
        title: "Policy Exception Request for Review",
        message: `${policy.name} has been submitted for exception review (${policy.exceptionRequest.exceptionType})`,
        priority: "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          exceptionType: policy.exceptionRequest.exceptionType,
          actionRequired: true,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: initiator._id,
          name: initiator.name,
          email: initiator.email,
        },
      };

      return await this.createBulkNotifications(
        governanceApprovers,
        notificationTemplate
      );
    } catch (error) {
      console.error("Failed to notify policy exception requested:", error);
    }
  }

  async notifyExceptionGovernanceReview(
    policy,
    reviewer,
    decision,
    policyOwner
  ) {
    try {
      const isApproved = decision === "Approved";

      if (isApproved) {
        // Notify final approvers based on exception type
        let approvers;
        if (policy.exceptionRequest?.exceptionType === "Material Exception") {
          // Board Committee level approval
          approvers = await User.find({
            role: { $in: ["Admin", "Super Admin"] },
            isActive: true,
          });
        } else if (
          policy.exceptionRequest?.exceptionType === "Immaterial Exception"
        ) {
          // CEO level approval
          approvers = await User.find({
            role: { $in: ["Approver", "Admin", "Super Admin"] },
            isActive: true,
          });
        }

        if (approvers && approvers.length > 0) {
          const approverNotificationTemplate = {
            type: "policy_workflow",
            category: "approval_required",
            title: "Policy Exception Pending Final Approval",
            message: `${policy.name} exception (${policy.exceptionRequest.exceptionType}) has passed governance review and requires final approval`,
            priority: "high",
            data: {
              policyId: policy._id,
              policyName: policy.name,
              exceptionType: policy.exceptionRequest.exceptionType,
              actionRequired: true,
              actionUrl: `/policies/manage/${policy._id}`,
            },
            sender: {
              id: reviewer._id,
              name: reviewer.name,
              email: reviewer.email,
            },
          };

          await this.createBulkNotifications(
            approvers,
            approverNotificationTemplate
          );
        }
      }

      // Notify policy owner
      const ownerNotificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: `Exception Governance Review ${decision}`,
        message: `Your policy exception request for "${policy.name}" has been ${decision.toLowerCase()} by governance review`,
        priority: isApproved ? "medium" : "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          exceptionType: policy.exceptionRequest?.exceptionType,
          previousStatus: "Exception Requested",
          newStatus: isApproved ? "Exception Pending Approval" : "Published",
          actionRequired: !isApproved,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(ownerNotificationData);
    } catch (error) {
      console.error("Failed to notify exception governance review:", error);
    }
  }

  async notifyPolicyExceptionApproved(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      const ownerNotification = await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Exception Approved",
        message: `Your policy exception request for "${policy.name}" (${policy.exceptionRequest?.exceptionType}) has been approved`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          exceptionType: policy.exceptionRequest?.exceptionType,
          previousStatus: "Exception Pending Approval",
          newStatus: "Exception Approved",
          actionRequired: false,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });

      // Notify relevant department users
      const departmentUsers = await User.find({
        department: policy.department,
        isActive: true,
        _id: { $ne: policy.policyOwner.id }, // Exclude policy owner (already notified)
      });

      const departmentNotificationTemplate = {
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Exception Approved",
        message: `${policy.name} exception (${policy.exceptionRequest?.exceptionType}) has been approved`,
        priority: "low",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          exceptionType: policy.exceptionRequest?.exceptionType,
          actionRequired: false,
          actionUrl: `/policies/view/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      };

      await this.createBulkNotifications(
        departmentUsers,
        departmentNotificationTemplate
      );

      return ownerNotification;
    } catch (error) {
      console.error("Failed to notify policy exception approved:", error);
    }
  }

  async notifyPolicyExceptionRejected(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      return await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Exception Rejected",
        message: `Your exception request for "${policy.name}" (${policy.exceptionRequest?.exceptionType}) has been rejected`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          exceptionType: policy.exceptionRequest?.exceptionType,
          previousStatus: "Exception Pending Approval",
          newStatus: "Exception Rejected",
          actionRequired: false,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });
    } catch (error) {
      console.error("Failed to notify exception rejected:", error);
    }
  }

  // Policy review notification handlers
  async notifyPolicyReviewInitiated(policy, initiator, policyOwner) {
    try {
      // Notify policy owner
      return await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "assignment",
        title: "Policy Review Required",
        message: `Your policy "${policy.name}" is due for review. Please complete the review process.`,
        priority: "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          reviewType: policy.reviewRequest?.reviewType,
          actionRequired: true,
          actionUrl: `/policies/review/${policy._id}`,
        },
        sender: {
          id: initiator._id,
          name: initiator.name,
          email: initiator.email,
        },
      });
    } catch (error) {
      console.error("Failed to notify policy review initiated:", error);
    }
  }

  async notifyPolicyOwnerReviewCompleted(policy, reviewer, decision) {
    try {
      // Find governance approvers (Approvers from Governance department + Admins + Super Admins)
      const governanceApprovers = await User.find({
        $or: [
          { role: "Approver", department: "Governance", isActive: true },
          { role: { $in: ["Admin", "Super Admin"] }, isActive: true },
        ],
      });

      const notificationTemplate = {
        type: "policy_workflow",
        category: "approval_required",
        title: "Policy Review - Governance Review Required",
        message: `${policy.name} owner review completed (${decision}) - requires governance review`,
        priority: "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          ownerDecision: decision,
          updatesRequired: policy.ownerReview?.updatesRequired,
          actionRequired: true,
          actionUrl: `/policies/review/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createBulkNotifications(
        governanceApprovers,
        notificationTemplate
      );
    } catch (error) {
      console.error("Failed to notify policy owner review completed:", error);
    }
  }

  async notifyReviewGovernanceReview(policy, reviewer, decision, policyOwner) {
    try {
      const isApproved = decision === "Approved";

      if (isApproved) {
        // Notify final approvers based on policy category
        const approvers = await this.getApproversForPolicy(policy);

        const approverNotificationTemplate = {
          type: "policy_workflow",
          category: "approval_required",
          title: "Policy Review - Final Approval Required",
          message: `${policy.name} review has passed governance review and requires final approval`,
          priority: "high",
          data: {
            policyId: policy._id,
            policyName: policy.name,
            actionRequired: true,
            actionUrl: `/policies/review/${policy._id}`,
          },
          sender: {
            id: reviewer._id,
            name: reviewer.name,
            email: reviewer.email,
          },
        };

        await this.createBulkNotifications(
          approvers,
          approverNotificationTemplate
        );
      }

      // Notify policy owner
      const ownerNotificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: `Review Governance ${decision}`,
        message: `Your policy review for "${policy.name}" has been ${decision.toLowerCase()} by governance`,
        priority: isApproved ? "medium" : "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Under Review",
          newStatus: isApproved ? "Pending Approval" : "In Progress",
          actionRequired: !isApproved,
          actionUrl: `/policies/review/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(ownerNotificationData);
    } catch (error) {
      console.error("Failed to notify review governance review:", error);
    }
  }

  async notifyPolicyReviewApproved(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      const ownerNotification = await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Review Completed",
        message: `Your policy review for "${policy.name}" has been completed and approved`,
        priority: "medium",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Pending Approval",
          newStatus: "Approved",
          actionRequired: false,
          actionUrl: `/policies/review/${policy._id}`,
          nextReviewDate: policy.nextReviewDate,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });

      // Notify relevant department users about completed review
      const departmentUsers = await User.find({
        department: policy.department,
        isActive: true,
        _id: { $ne: policy.policyOwner.id }, // Exclude policy owner (already notified)
      });

      const departmentNotificationTemplate = {
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Review Completed",
        message: `${policy.name} review has been completed - policy remains active`,
        priority: "low",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: false,
          actionUrl: `/policies/view/${policy._id}`,
          nextReviewDate: policy.nextReviewDate,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      };

      await this.createBulkNotifications(
        departmentUsers,
        departmentNotificationTemplate
      );

      return ownerNotification;
    } catch (error) {
      console.error("Failed to notify policy review approved:", error);
    }
  }

  async notifyPolicyReviewRejected(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      return await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: "policy_workflow",
        category: "status_change",
        title: "Policy Review Rejected",
        message: `Your policy review for "${policy.name}" has been rejected - action required`,
        priority: "high",
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: "Pending Approval",
          newStatus: "Rejected",
          actionRequired: true,
          actionUrl: `/policies/review/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });
    } catch (error) {
      console.error("Failed to notify policy review rejected:", error);
    }
  }

  // Helper method to get approvers based on policy category
  async getApproversForPolicy(policy) {
    let roleFilter;

    if (policy.policyCategory === "Corporate Policies") {
      // Board Committee level approval
      roleFilter = { role: { $in: ["Admin", "Super Admin"] } };
    } else if (policy.policyCategory === "Operational Policies") {
      // CEO level approval
      roleFilter = { role: { $in: ["Approver", "Admin", "Super Admin"] } };
    } else {
      // Department Head level approval
      roleFilter = {
        role: { $in: ["Approver", "Admin", "Super Admin"] },
        department: policy.department,
      };
    }

    return await User.find({ ...roleFilter, isActive: true });
  }

  // Due date reminder notifications with 15, 7, and 1 day intervals
  async sendDueDateReminders() {
    try {
      const Policy = require("../models/Policy");

      // Find overdue policies first
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const overduePolicy = await Policy.find({
        dueDate: { $lt: today },
        status: { $in: ["Draft", "Under Review", "Pending Approval"] },
      }).populate("policyOwner.id");

      for (const policy of overduePolicy) {
        if (policy.policyOwner?.id) {
          const daysOverdue = Math.ceil(
            (today - policy.dueDate) / (1000 * 60 * 60 * 24)
          );
          await this.createNotification({
            recipient: {
              id: policy.policyOwner.id._id,
              email: policy.policyOwner.id.email,
              role: policy.policyOwner.id.role,
            },
            type: "reminder",
            category: "due_date",
            title: "Policy Overdue - Immediate Action Required",
            message: `Policy "${policy.name}" is ${daysOverdue} days overdue. Please complete immediately.`,
            priority: "critical",
            data: {
              policyId: policy._id,
              policyName: policy.name,
              dueDate: policy.dueDate,
              daysOverdue: daysOverdue,
              actionRequired: true,
              actionUrl: `/policies/manage/${policy._id}`,
            },
          });
        }
      }

      // Send reminders at 15, 7, and 1 day intervals before due date
      const reminderIntervals = [
        { days: 15, priority: "low", title: "Policy Due in 15 Days" },
        { days: 7, priority: "medium", title: "Policy Due in 1 Week" },
        { days: 1, priority: "high", title: "Policy Due Tomorrow" },
      ];

      let totalReminders = 0;

      for (const interval of reminderIntervals) {
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() + interval.days);
        targetDate.setHours(0, 0, 0, 0);

        const nextDay = new Date(targetDate);
        nextDay.setDate(nextDay.getDate() + 1);
        nextDay.setHours(0, 0, 0, 0);

        const policiesDueSoon = await Policy.find({
          dueDate: { $gte: targetDate, $lt: nextDay },
          status: { $in: ["Draft", "Under Review", "Pending Approval"] },
        }).populate("policyOwner.id");

        for (const policy of policiesDueSoon) {
          if (policy.policyOwner?.id) {
            const message =
              interval.days === 1
                ? `Policy "${policy.name}" is due tomorrow. Please ensure completion by the due date.`
                : `Policy "${policy.name}" is due in ${interval.days} days. Please plan to complete on time.`;

            await this.createNotification({
              recipient: {
                id: policy.policyOwner.id._id,
                email: policy.policyOwner.id.email,
                role: policy.policyOwner.id.role,
              },
              type: "reminder",
              category: "due_date",
              title: interval.title,
              message: message,
              priority: interval.priority,
              data: {
                policyId: policy._id,
                policyName: policy.name,
                dueDate: policy.dueDate,
                daysUntilDue: interval.days,
                actionRequired: interval.days <= 1,
                actionUrl: `/policies/manage/${policy._id}`,
              },
            });
            totalReminders++;
          }
        }
      }

      console.log(
        `📅 Due date reminders processed: ${overduePolicy.length} overdue, ${totalReminders} scheduled reminders (15/7/1 day intervals)`
      );
    } catch (error) {
      console.error("Failed to send due date reminders:", error);
    }
  }

  // Policy review reminder notifications with 15, 7, and 1 day intervals
  async sendReviewReminders() {
    try {
      const Policy = require("../models/Policy");

      // Find policies that are due for review (nextReviewDate has passed)
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const policiesDueForReview = await Policy.find({
        nextReviewDate: { $lte: today },
        status: "Published",
        reviewStatus: { $in: ["None", null] }, // Only policies not already in review
      }).populate("policyOwner.id");

      for (const policy of policiesDueForReview) {
        // Automatically initiate review for overdue policies
        await policy.initiatePolicyReview("Scheduled Review", "System");

        if (policy.policyOwner?.id) {
          await this.createNotification({
            recipient: {
              id: policy.policyOwner.id._id,
              email: policy.policyOwner.id.email,
              role: policy.policyOwner.id.role,
            },
            type: "policy_workflow",
            category: "assignment",
            title: "Policy Review Required - Overdue",
            message: `Your policy "${policy.name}" is overdue for review. Please complete the review process immediately.`,
            priority: "high",
            data: {
              policyId: policy._id,
              policyName: policy.name,
              reviewType: "Scheduled Review",
              nextReviewDate: policy.nextReviewDate,
              actionRequired: true,
              actionUrl: `/policies/review/${policy._id}`,
            },
          });
        }
      }

      // Send reminders at 15, 7, and 1 day intervals
      const reminderIntervals = [
        { days: 15, priority: "low", title: "Policy Review Due in 15 Days" },
        { days: 7, priority: "medium", title: "Policy Review Due in 1 Week" },
        { days: 1, priority: "high", title: "Policy Review Due Tomorrow" },
      ];

      let totalReminders = 0;

      for (const interval of reminderIntervals) {
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() + interval.days);
        targetDate.setHours(0, 0, 0, 0);

        const nextDay = new Date(targetDate);
        nextDay.setDate(nextDay.getDate() + 1);
        nextDay.setHours(0, 0, 0, 0);

        const policiesForReminder = await Policy.find({
          nextReviewDate: { $gte: targetDate, $lt: nextDay },
          status: "Published",
          reviewStatus: { $in: ["None", null] }, // Only policies not already in review
        }).populate("policyOwner.id");

        for (const policy of policiesForReminder) {
          if (policy.policyOwner?.id) {
            const message =
              interval.days === 1
                ? `Policy "${policy.name}" is due for review tomorrow. Please prepare for the review process.`
                : `Policy "${policy.name}" is due for review in ${interval.days} days. Please plan accordingly.`;

            await this.createNotification({
              recipient: {
                id: policy.policyOwner.id._id,
                email: policy.policyOwner.id.email,
                role: policy.policyOwner.id.role,
              },
              type: "reminder",
              category: "review_due",
              title: interval.title,
              message: message,
              priority: interval.priority,
              data: {
                policyId: policy._id,
                policyName: policy.name,
                nextReviewDate: policy.nextReviewDate,
                daysUntilReview: interval.days,
                actionRequired: interval.days <= 1,
                actionUrl:
                  interval.days <= 1
                    ? `/policies/review/${policy._id}`
                    : `/policies/view/${policy._id}`,
              },
            });
            totalReminders++;
          }
        }
      }

      console.log(
        `📋 Review reminders processed: ${policiesDueForReview.length} overdue, ${totalReminders} scheduled reminders (15/7/1 day intervals)`
      );
    } catch (error) {
      console.error("Failed to send review reminders:", error);
    }
  }

  // Start scheduled tasks
  startScheduledTasks() {
    // Send due date and review reminders every day at 9 AM
    const scheduleReminders = () => {
      const now = new Date();
      const scheduledTime = new Date();
      scheduledTime.setHours(9, 0, 0, 0); // 9:00 AM

      // If it's already past 9 AM today, schedule for tomorrow
      if (now > scheduledTime) {
        scheduledTime.setDate(scheduledTime.getDate() + 1);
      }

      const timeUntilScheduled = scheduledTime.getTime() - now.getTime();

      setTimeout(() => {
        // Run both due date and review reminders
        this.sendDueDateReminders();
        this.sendReviewReminders();

        // Schedule the next reminder (24 hours later)
        setInterval(
          () => {
            this.sendDueDateReminders();
            this.sendReviewReminders();
          },
          24 * 60 * 60 * 1000
        ); // 24 hours
      }, timeUntilScheduled);
    };

    scheduleReminders();
    console.log("📅 Due date and review reminder scheduler started");
  }
}

module.exports = NotificationService;
