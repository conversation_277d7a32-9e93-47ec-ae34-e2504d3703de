'use client';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useRouter } from 'next/navigation';

import Link from 'next/link';
import Image from 'next/image';

import { useMutation } from 'react-query';
import mainimg from '@/assests/images/mainimg.png';
import autores from '@/assests/images/autores2.png';
import Logo from '@/assests/images/logo.svg';
import ascent from '@/assests/images/ascent.png';

import { useForm } from 'react-hook-form';
import { Toaster, toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';

interface UserCred {
  email: string;
  password: string;
}

const Login = () => {
  const router = useRouter();
  const { login } = useAuth();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<UserCred>();

  const { mutate, isLoading } = useMutation(
    async (data: UserCred) => {
      const success = await login(data.email, data.password);
      if (!success) {
        throw new Error('Login failed');
      }
      return data;
    },
    {
      onSuccess: () => {
        toast.success('Login successful!');
        router.push('/all-apps');
        reset();
      },
      onError: () => {
        toast.error('Login failed, please check your credentials.');
      },
    },
  );

  const onSubmit = (data: UserCred) => {
    mutate(data);
  };

  return (
    <div className={`flex flex-row items-center justify-center`}>
      <div className="flex h-screen w-1/2 items-center justify-center">
        <Image
          src={mainimg}
          priority={true}
          alt="Main"
          className="h-full object-cover"
        />
      </div>
      <div className="flex w-1/2 flex-col items-center justify-center">
        <Card className="flex w-4/6 flex-col justify-center border-0 shadow-none">
          <CardHeader className="flex items-center justify-between">
            <div className="flex">
              <Logo />
              <div className="flex flex-col">
                <Image
                  src={autores}
                  alt="autores"
                  className="h-12 w-40 shrink-0"
                />
                <p className="font-roboto text-xs text-customGray">
                  AI Powered Software Suite Enabling GRC
                </p>
              </div>
            </div>
            <CardTitle className="text-center text-4xl text-[#323232]">
              Sign in
              <p className="text-center text-sm font-bold text-customGray">
                to access Auto Resilience GRC Suite
              </p>
            </CardTitle>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="w-full items-center space-y-4 font-roboto">
                <div className="flex flex-col space-y-1.5">
                  <Label className="text-sm text-customLabel" htmlFor="email">
                    Email Address
                  </Label>

                  <Input
                    id="email"
                    placeholder="Enter your work email"
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        value:
                          /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
                        message: 'Enter a valid email address',
                      },
                    })}
                    className="font-roboto"
                  />

                  {errors.email && (
                    <p className="text-red-600">
                      {String(errors.email.message)}
                    </p>
                  )}
                </div>
                <div className="relative flex flex-col space-y-1.5">
                  <Label
                    className="text-sm text-customLabel"
                    htmlFor="password"
                  >
                    Password
                  </Label>

                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    {...register('password', {
                      required: 'Password is required',
                    })}
                    className="font-roboto"
                  />

                  <Label className="absolute right-0 top-[90%] text-xs text-customGray">
                    <Link href="/forgot-password">Forgot Password ?</Link>
                  </Label>

                  {errors.password && (
                    <p className="text-red-600">
                      {String(errors.password.message)}
                    </p>
                  )}
                </div>
              </div>
              <br></br>
              <div>
                <Button
                  disabled={isLoading}
                  type="submit"
                  className="flex h-[50px] w-full items-center justify-center rounded-[40px] bg-customBlue hover:bg-customBlueHover"
                >
                  {isLoading ? 'Signing In...' : 'Sign In'}
                </Button>
              </div>
              <div>
                <Toaster position="top-center" reverseOrder={false} />
              </div>
            </form>
          </CardContent>
          <CardFooter className="flex-col text-sm text-customGray">
            <p>
              New?{' '}
              <span className="text-customGreen">
                <Link onClick={(e) => e.preventDefault()} href="./signup">
                  Sign Up!
                </Link>
              </span>
            </p>
            <br></br>
          </CardFooter>
        </Card>
        <div className="absolute bottom-1 right-0 flex items-center pr-10 text-sm text-customGray">
          Powered by{' '}
          <Image
            src={ascent}
            alt="ascent"
            className="ml-1 h-[29px] w-[89px] shrink-0"
          />
        </div>
      </div>
    </div>
  );
};

export default Login;
