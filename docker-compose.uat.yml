version: '3.8'

networks:
  grc-network:
    driver: bridge

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
        - YARN_NETWORK_TIMEOUT=300000
        - YARN_NETWORK_CONCURRENCY=1
        - NEXT_PUBLIC_API_URL=https://grc-temp.autoresilience.com/app
        - NEXT_PUBLIC_SOCKET_URL=https://grc-temp-app.autoresilience.com
        - NEXT_PUBLIC_ONLYOFFICE_URL=https://onlyoffice-temp.autoresilience.com
        - NEXT_PUBLIC_APP_NAME=GRC Web App
        - NEXT_PUBLIC_APP_VERSION=1.0.0
        - NEXT_PUBLIC_DOMAIN=grc-temp.autoresilience.com
      network: host
    container_name: grc-frontend-uat
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env.uat
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://grc-temp.autoresilience.com/app
      - NEXT_PUBLIC_SOCKET_URL=https://grc-temp-app.autoresilience.com
      - NEXT_PUBLIC_ONLYOFFICE_URL=https://onlyoffice-temp.autoresilience.com
      - NEXT_PUBLIC_APP_NAME=GRC Web App
      - NEXT_PUBLIC_APP_VERSION=1.0.0
      - NEXT_PUBLIC_DOMAIN=grc-temp.autoresilience.com
    networks:
      - grc-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: grc-backend-uat
    restart: unless-stopped
    ports:
      - "5000:5000"
    env_file:
      - ./backend/.env.uat
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - FRONTEND_URL=https://grc-temp.autoresilience.com
      - ONLYOFFICE_JWT_SECRET=${ONLYOFFICE_JWT_SECRET}
      - BASE_URL_ONLYOFFICE=http://onlyoffice:80
    networks:
      - grc-network
    volumes:
      - ./backend/uploads:/app/uploads:rw
      - backend_logs:/app/logs
    user: "0:0"  # Run as root to ensure proper file permissions
    depends_on:
      - onlyoffice
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5000/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # OnlyOffice Document Server
  onlyoffice:
    image: onlyoffice/documentserver:latest
    container_name: grc-onlyoffice-uat
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - grc-network
    environment:
      # JWT Configuration
      - JWT_ENABLED=true
      - JWT_SECRET=${ONLYOFFICE_JWT_SECRET}
      - JWT_HEADER=Authorization

      # Storage and security settings
      - USE_UNAUTHORIZED_STORAGE=false
      - WOPI_ENABLED=false

      # Performance optimized timeout settings
      - DOC_SERV_MAX_FILE_SIZE=52428800
      - DOC_SERV_TIMEOUT=30000
      - DOC_SERV_CONVERTER_TIMEOUT=30000
      - DOC_SERV_BUILDER_TIMEOUT=30000
      
      # Connection and request timeouts
      - DOC_SERV_REQUEST_TIMEOUT=10000
      - DOC_SERV_SOCKET_TIMEOUT=10000
      - DOC_SERV_RESPONSE_TIMEOUT=10000

      # Network configuration for UAT
      - ALLOW_PRIVATE_IP_ADDRESS=true
      - ALLOW_META_IP_ADDRESS=true
      - ALLOW_HTTPS_INSECURE=true
      - ALLOW_HTTP_REQUEST_TO_HTTPS_RESOURCE=true

      # Additional security settings
      - REJECT_UNAUTHORIZED=false
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      
      # UAT specific settings
      - ONLYOFFICE_HTTPS_HSTS_ENABLED=false
      - ONLYOFFICE_HTTPS_HSTS_MAXAGE=31536000

      # Performance optimizations
      - DOC_SERV_PRELOADER_URL=
      - DOC_SERV_STATIC_HOST=
      - DOC_SERV_ALLOW_FORGOTTEN_FILES=false
      
    volumes:
      - onlyoffice_data_uat:/var/www/onlyoffice/Data
      - onlyoffice_logs_uat:/var/log/onlyoffice
      # Removed problematic volume mapping that was causing performance issues
    
    # Resource limits for better performance
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    
    # Memory limit (alternative syntax for older Docker Compose versions)
    mem_limit: 2g
    cpus: 1.0
    
    # Shared memory for better performance
    shm_size: 512m
    
    # Optimize container settings
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      memlock:
        soft: -1
        hard: -1
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  onlyoffice_data_uat:
    driver: local
  onlyoffice_logs_uat:
    driver: local
  backend_logs:
    driver: local