'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X, CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { useUsers } from '@/hooks/useUsers';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';

interface PolicyPublicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (publicationData: {
    effectiveDate: string;
    targetAudience: 'All Employees' | 'Selected Groups' | 'Department Only';
    selectedGroups?: string[];
    comments?: string;
  }) => Promise<void>;
  loading?: boolean;
  policyName: string;
  policyDepartment: string;
}

// Available departments for group selection
const AVAILABLE_DEPARTMENTS = [
  'PMO Advisory Investment Activation',
  'Development',
  'Legal',
  'Procurement',
  'Marketing',
  'Golf Excellence',
  'Risk Management',
  'HR',
  'Sales & Sponsorships',
  'Events',
  'Compliance',
  'Finance',
  'Federation Office',
  'Academies',
  'Governance',
  'Business Continuity Management',
  'Events Management',
  'Local Golf',
  'Tournaments',
  'Internal Audit',
  'IT',
  'Cybersecurity',
  'Admin & HSSE',
  'CEO Office',
  'Strategy',
  'Golf Operations',
];

const TARGET_AUDIENCE_OPTIONS = [
  'All Employees',
  'Selected Groups',
  'Department Only',
] as const;

export const PolicyPublicationModal: React.FC<PolicyPublicationModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false,
  policyName,
  policyDepartment,
}) => {
  const { users, fetchUsers } = useUsers();

  // Form state
  const [formData, setFormData] = useState({
    effectiveDate: undefined as Date | undefined,
    targetAudience: 'All Employees' as
      | 'All Employees'
      | 'Selected Groups'
      | 'Department Only',
    selectedGroups: [] as string[],
    comments: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [availableDepartments, setAvailableDepartments] = useState<string[]>(
    [],
  );

  // Fetch users and departments on modal open
  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen, fetchUsers]);

  // Update available departments from users
  useEffect(() => {
    if (users.length > 0) {
      const departments = [
        ...new Set(users.map((u) => u.department).filter(Boolean)),
      ];
      setAvailableDepartments(
        departments.length > 0 ? departments : AVAILABLE_DEPARTMENTS,
      );
    } else {
      setAvailableDepartments(AVAILABLE_DEPARTMENTS);
    }
  }, [users]);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        effectiveDate: new Date(), // Default to today
        targetAudience: 'All Employees',
        selectedGroups: [],
        comments: '',
      });
      setErrors({});
    }
  }, [isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleGroupChange = (department: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      selectedGroups: checked
        ? [...prev.selectedGroups, department]
        : prev.selectedGroups.filter((d) => d !== department),
    }));

    if (errors.selectedGroups) {
      setErrors((prev) => ({ ...prev, selectedGroups: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.effectiveDate) {
      newErrors.effectiveDate = 'Effective date is required';
    }

    if (
      formData.targetAudience === 'Selected Groups' &&
      formData.selectedGroups.length === 0
    ) {
      newErrors.selectedGroups = 'At least one group must be selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        effectiveDate: formData.effectiveDate!.toISOString(),
        targetAudience: formData.targetAudience,
        selectedGroups:
          formData.targetAudience === 'Selected Groups'
            ? formData.selectedGroups
            : undefined,
        comments: formData.comments.trim() || undefined,
      });

      // Reset form on successful submission
      setFormData({
        effectiveDate: new Date(),
        targetAudience: 'All Employees',
        selectedGroups: [],
        comments: '',
      });
      setErrors({});
      onClose();
    } catch (error) {
      console.error('Failed to publish policy:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      setErrors({ general: errorMessage });
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        effectiveDate: new Date(),
        targetAudience: 'All Employees',
        selectedGroups: [],
        comments: '',
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPrimitive.Portal>
        {/* Transparent overlay */}
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black opacity-40" />
        {/* Custom content without default overlay */}
        <DialogPrimitive.Content
          className={cn(
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid max-h-[90vh] w-full max-w-2xl translate-x-[-50%] translate-y-[-50%] gap-4 overflow-y-auto border bg-background p-6 shadow-lg duration-200 sm:rounded-lg',
          )}
        >
          <DialogHeader>
            <DialogTitle>Publish Policy</DialogTitle>
            <DialogDescription>
              Set the effective date and target audience for publishing
              {policyName}.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* General Error Display */}
            {errors.general && (
              <div className="rounded-md border border-red-200 bg-red-50 p-4">
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            )}

            {/* Effective Date */}
            <div className="space-y-2">
              <Label>Effective Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !formData.effectiveDate && 'text-muted-foreground',
                      errors.effectiveDate && 'border-red-500',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.effectiveDate ? (
                      format(formData.effectiveDate, 'PPP')
                    ) : (
                      <span>Pick an effective date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.effectiveDate}
                    onSelect={(date) =>
                      handleInputChange('effectiveDate', date)
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.effectiveDate && (
                <p className="text-sm text-red-500">{errors.effectiveDate}</p>
              )}
              <p className="text-xs text-gray-500">
                The date when this policy becomes effective and enforceable
              </p>
            </div>

            {/* Target Audience */}
            <div className="space-y-2">
              <Label>Target Audience *</Label>
              <Select
                value={formData.targetAudience}
                onValueChange={(value) =>
                  handleInputChange('targetAudience', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TARGET_AUDIENCE_OPTIONS.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="text-xs text-gray-500">
                {formData.targetAudience === 'All Employees' && (
                  <span>
                    Policy will be visible to all employees across the
                    organization
                  </span>
                )}
                {formData.targetAudience === 'Department Only' && (
                  <span>
                    Policy will be visible only to the {policyDepartment}{' '}
                    department
                  </span>
                )}
                {formData.targetAudience === 'Selected Groups' && (
                  <span>
                    Policy will be visible only to selected departments/groups
                  </span>
                )}
              </div>
            </div>

            {/* Selected Groups (only show if Selected Groups is chosen) */}
            {formData.targetAudience === 'Selected Groups' && (
              <div className="space-y-2">
                <Label>Select Departments/Groups *</Label>
                <div className="max-h-48 overflow-y-auto rounded-md border p-4">
                  <div className="grid grid-cols-2 gap-2">
                    {availableDepartments.map((department) => (
                      <div
                        key={department}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={department}
                          checked={formData.selectedGroups.includes(department)}
                          onCheckedChange={(checked) =>
                            handleGroupChange(department, checked as boolean)
                          }
                        />
                        <Label htmlFor={department} className="text-sm">
                          {department}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                {errors.selectedGroups && (
                  <p className="text-sm text-red-500">
                    {errors.selectedGroups}
                  </p>
                )}
                <p className="text-xs text-gray-500">
                  Selected: {formData.selectedGroups.length} department(s)
                </p>
              </div>
            )}

            {/* Comments */}
            <div className="space-y-2">
              <Label htmlFor="comments">Publication Comments</Label>
              <Textarea
                id="comments"
                value={formData.comments}
                onChange={(e) => handleInputChange('comments', e.target.value)}
                placeholder="Optional comments about this publication (e.g., key changes, implementation notes)"
                rows={3}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Publishing...' : 'Publish Policy'}
              </Button>
            </DialogFooter>
          </form>

          {/* Close button */}
          <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
  );
};
