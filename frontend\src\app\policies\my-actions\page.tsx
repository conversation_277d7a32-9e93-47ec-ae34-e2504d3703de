'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useMyActions } from '@/hooks/useMyActions';
import { Policy } from '@/lib/api';
import {
  CheckCircle,
  AlertTriangle,
  Eye,
  Edit,
  ThumbsUp,
  ThumbsDown,
  Send,
  Calendar,
  User,
  Building,
  Target,
  FileX,
  Archive,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { StatusBadge } from '@/components/StatusBadge';

const MyActions = () => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [actionFilter, setActionFilter] = useState<string>('all');

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch policies with user-specific filtering
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    updatePolicyStatus,
    governanceReview,
    grcReview,
    submitPolicyForReview,
    approvePolicy,
    rejectPolicy,
    requestRetirement,
    retirementGovernanceReview,
    approveRetirement,
    rejectRetirement,
    requestException,
    exceptionGovernanceReview,
    approveException,
    rejectException,
  } = usePolicies({
    autoFetch: false,
    myActions: true, // This will apply My Actions specific filtering
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Update selected policy when policies data changes
  useEffect(() => {
    if (selectedPolicy && policies.length > 0) {
      const updatedPolicy = policies.find((p) => p._id === selectedPolicy._id);
      if (updatedPolicy) {
        setSelectedPolicy(updatedPolicy);
      }
    }
  }, [policies, selectedPolicy]);

  // Use the custom hook for action categorization
  const {
    actionablePolicies,
    actionCategories,
    totalActionCount,
    hasUrgentActions,
  } = useMyActions(policies, user);

  // Handle policy click to open details modal
  const handlePolicyClick = (policy: Policy) => {
    setSelectedPolicy(policy);
    setIsDetailsModalOpen(true);
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle policy update
  const handlePolicyUpdate = () => {
    refetchPolicies();
  };

  // Handle status update from details modal
  const handleStatusUpdate = async (
    policyId: string,
    newStatus: string,
    comments?: string,
    effectiveDate?: string,
    selectedGroups?: string[],
  ): Promise<boolean> => {
    const result = await updatePolicyStatus(
      policyId,
      newStatus,
      comments,
      effectiveDate,
      selectedGroups,
    );
    refetchPolicies();
    return result !== null;
  };

  // Handle governance review from details modal
  const handleGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await governanceReview(policyId, decision, comments);
    refetchPolicies();
    return result;
  };

  // Handle GRC review from details modal
  const handleGrcReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await grcReview(policyId, decision, comments);
    refetchPolicies();
    return result;
  };

  // Handle submit for review from details modal
  const handleSubmitForReview = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await submitPolicyForReview(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle approve policy from details modal
  const handleApprovePolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approvePolicy(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle reject policy from details modal
  const handleRejectPolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectPolicy(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle retirement workflow actions
  const handleRequestRetirement = async (
    policyId: string,
    justification: string,
    effectiveDate?: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await requestRetirement(
      policyId,
      justification,
      effectiveDate,
      comments,
    );
    refetchPolicies();
    return result;
  };

  const handleRetirementGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await retirementGovernanceReview(
      policyId,
      decision,
      comments,
    );
    refetchPolicies();
    return result;
  };

  const handleApproveRetirement = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveRetirement(policyId, comments);
    refetchPolicies();
    return result;
  };

  const handleRejectRetirement = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectRetirement(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle exception workflow actions
  const handleRequestException = async (
    policyId: string,
    justification: string,
    specificSection: string,
    exceptionType: 'Material Exception' | 'Immaterial Exception',
    effectiveDate?: string,
    expiryDate?: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await requestException(
      policyId,
      justification,
      specificSection,
      exceptionType,
      effectiveDate,
      expiryDate,
      comments,
    );
    refetchPolicies();
    return result;
  };

  const handleExceptionGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await exceptionGovernanceReview(
      policyId,
      decision,
      comments,
    );
    refetchPolicies();
    return result;
  };

  const handleApproveException = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveException(policyId, comments);
    refetchPolicies();
    return result;
  };

  const handleRejectException = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectException(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading your action items..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="w-full bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Actions"
          message="We couldn't load your action items. Please try again."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="w-full bg-gray-100 py-2">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">My Actions</h1>
            <p className="mt-1 text-sm text-gray-600">
              Policies requiring your attention and action
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              <User className="mr-1 h-3 w-3" />
              {user?.role}
            </Badge>
            <Badge variant="outline" className="text-sm">
              <Building className="mr-1 h-3 w-3" />
              {user?.department}
            </Badge>
          </div>
        </div>

        {/* Action Summary Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Action Summary
          </h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6">
            {/* Urgent Actions */}
            <Card className="cursor-pointer border-l-4 border-l-red-500 bg-red-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <span>Urgent</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-red-600">
                    {policiesLoading ? '...' : actionCategories.urgent.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">Overdue</p>
                </div>
              </CardContent>
            </Card>

            {/* My Drafts */}
            <Card className="cursor-pointer border-l-4 border-l-blue-500 bg-blue-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <Edit className="h-4 w-4 text-blue-500" />
                    <span>My Drafts</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-blue-600">
                    {policiesLoading ? '...' : actionCategories.myDrafts.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Policies Im drafting
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Awaiting My Review */}
            <Card className="cursor-pointer border-l-4 border-l-orange-500 bg-orange-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-4 w-4 text-orange-500" />
                    <span>My Review</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-orange-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.awaitingMyReview.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Awaiting my review
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Awaiting My Approval */}
            <Card className="cursor-pointer border-l-4 border-l-yellow-500 bg-yellow-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-yellow-500" />
                    <span>My Approval</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-yellow-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.awaitingMyApproval.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Awaiting my approval
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Ready to Publish */}
            <Card className="cursor-pointer border-l-4 border-l-green-500 bg-green-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <Send className="h-4 w-4 text-green-500" />
                    <span>To Publish</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-green-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.readyToPublish.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Ready for publication
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Annual Review */}
            <Card className="cursor-pointer border-l-4 border-l-purple-500 bg-purple-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-purple-500" />
                    <span>Annual Review</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-purple-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.annualReview.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Under annual review
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Retirement Requests */}
            <Card className="cursor-pointer border-l-4 border-l-orange-500 bg-orange-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <FileX className="h-4 w-4 text-orange-500" />
                    <span>Retirement</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-orange-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.retirementRequests.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Can request retirement
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Retirement Review */}
            <Card className="cursor-pointer border-l-4 border-l-red-500 bg-red-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-4 w-4 text-red-500" />
                    <span>Retirement Review</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-red-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.retirementReview.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Retirement requests to review
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Retirement Approval */}
            <Card className="cursor-pointer border-l-4 border-l-gray-500 bg-gray-50 transition-shadow hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                  <div className="flex items-center space-x-2">
                    <Archive className="h-4 w-4 text-gray-500" />
                    <span>Retirement Approval</span>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-col space-y-2">
                  <p className="text-2xl font-bold text-gray-600">
                    {policiesLoading
                      ? '...'
                      : actionCategories.retirementApproval.length}
                  </p>
                  <p className="text-xs leading-tight text-gray-500">
                    Final retirement approvals
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Lists */}
        <div className="space-y-6">
          {/* Urgent Actions */}
          {actionCategories.urgent.length > 0 && (
            <Card className="border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <AlertTriangle className="h-5 w-5" />
                  Urgent Actions ({actionCategories.urgent.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.urgent.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-red-200 bg-red-50 p-3 transition-colors hover:bg-red-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          {policy.dueDate &&
                            new Date(policy.dueDate) < new Date() && (
                              <Badge variant="destructive" className="text-xs">
                                Overdue
                              </Badge>
                            )}
                          {(policy.priorityScore || 5) >= 8 && (
                            <Badge variant="destructive" className="text-xs">
                              High Priority
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          {policy.dueDate && (
                            <span>
                              Due:{' '}
                              {new Date(policy.dueDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="ml-4">
                        View Details
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* My Drafts */}
          {actionCategories.myDrafts.length > 0 && (
            <Card className="border-l-4 border-l-blue-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <Edit className="h-5 w-5" />
                  My Drafts ({actionCategories.myDrafts.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.myDrafts.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-3 transition-colors hover:bg-blue-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          {!policy.attachments ||
                            (policy.attachments.length === 0 && (
                              <Badge
                                variant="outline"
                                className="text-xs text-orange-600"
                              >
                                No Document
                              </Badge>
                            ))}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          {policy.dueDate && (
                            <span>
                              Due:{' '}
                              {new Date(policy.dueDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        {policy.attachments && policy.attachments.length > 0 ? (
                          <Button
                            size="sm"
                            variant="default"
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            Edit Document
                          </Button>
                        ) : (
                          <Button size="sm" variant="outline">
                            Add Document
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Awaiting My Review */}
          {actionCategories.awaitingMyReview.length > 0 && (
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-700">
                  <Eye className="h-5 w-5" />
                  Awaiting My Review ({actionCategories.awaitingMyReview.length}
                  )
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.awaitingMyReview.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-orange-200 bg-orange-50 p-3 transition-colors hover:bg-orange-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          {policy.dueDate && (
                            <span>
                              Due:{' '}
                              {new Date(policy.dueDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <ThumbsUp className="mr-1 h-3 w-3" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <ThumbsDown className="mr-1 h-3 w-3" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Awaiting My Approval */}
          {actionCategories.awaitingMyApproval.length > 0 && (
            <Card className="border-l-4 border-l-yellow-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-yellow-700">
                  <CheckCircle className="h-5 w-5" />
                  Awaiting My Approval (
                  {actionCategories.awaitingMyApproval.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.awaitingMyApproval.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-yellow-200 bg-yellow-50 p-3 transition-colors hover:bg-yellow-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          {policy.dueDate && (
                            <span>
                              Due:{' '}
                              {new Date(policy.dueDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Final Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <ThumbsDown className="mr-1 h-3 w-3" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Ready to Publish */}
          {actionCategories.readyToPublish.length > 0 && (
            <Card className="border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <Send className="h-5 w-5" />
                  Ready to Publish ({actionCategories.readyToPublish.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.readyToPublish.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-green-200 bg-green-50 p-3 transition-colors hover:bg-green-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          {policy.approvalDate && (
                            <span>
                              Approved:{' '}
                              {new Date(
                                policy.approvalDate,
                              ).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Send className="mr-1 h-3 w-3" />
                          Publish
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Retirement Requests */}
          {actionCategories.retirementRequests.length > 0 && (
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-700">
                  <FileX className="h-5 w-5" />
                  My Published Policies - Can Request Retirement (
                  {actionCategories.retirementRequests.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.retirementRequests.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-orange-200 bg-orange-50 p-3 transition-colors hover:bg-orange-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          {policy.publishedDate && (
                            <Badge
                              variant="outline"
                              className="text-xs text-green-600"
                            >
                              Published:{' '}
                              {new Date(
                                policy.publishedDate,
                              ).toLocaleDateString()}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Type: {policy.policyType}</span>
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-orange-600 hover:text-orange-700"
                        >
                          <FileX className="mr-1 h-3 w-3" />
                          Request Retirement
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Retirement Review */}
          {actionCategories.retirementReview.length > 0 && (
            <Card className="border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <Eye className="h-5 w-5" />
                  Retirement Requests - Awaiting My Review (
                  {actionCategories.retirementReview.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.retirementReview.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-red-200 bg-red-50 p-3 transition-colors hover:bg-red-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          {policy.retirementRequestedDate && (
                            <Badge
                              variant="outline"
                              className="text-xs text-red-600"
                            >
                              Requested:{' '}
                              {new Date(
                                policy.retirementRequestedDate,
                              ).toLocaleDateString()}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          <span>
                            Requested by:{' '}
                            {policy.retirementRequest?.requestedBy?.name}
                          </span>
                        </div>
                        {policy.retirementRequest?.justification && (
                          <div className="mt-2 text-sm text-gray-700">
                            <span className="font-medium">Justification:</span>{' '}
                            {policy.retirementRequest.justification}
                          </div>
                        )}
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <ThumbsUp className="mr-1 h-3 w-3" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <ThumbsDown className="mr-1 h-3 w-3" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Retirement Approval */}
          {actionCategories.retirementApproval.length > 0 && (
            <Card className="border-l-4 border-l-gray-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-700">
                  <Archive className="h-5 w-5" />
                  Retirement - Awaiting My Final Approval (
                  {actionCategories.retirementApproval.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.retirementApproval.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-3 transition-colors hover:bg-gray-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          <Badge
                            variant="outline"
                            className="text-xs text-blue-600"
                          >
                            {policy.policyCategory === 'Corporate Policies'
                              ? 'Board Approval'
                              : 'CEO Approval'}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          <span>
                            Requested by:{' '}
                            {policy.retirementRequest?.requestedBy?.name}
                          </span>
                        </div>
                        {policy.retirementRequest?.justification && (
                          <div className="mt-2 text-sm text-gray-700">
                            <span className="font-medium">Justification:</span>{' '}
                            {policy.retirementRequest.justification}
                          </div>
                        )}
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-red-600 hover:bg-red-700"
                        >
                          <Archive className="mr-1 h-3 w-3" />
                          Retire Policy
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-gray-600 hover:text-gray-700"
                        >
                          <ThumbsDown className="mr-1 h-3 w-3" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Exception Requests */}
          {actionCategories.exceptionRequests.length > 0 && (
            <Card className="border-l-4 border-l-purple-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-700">
                  <AlertTriangle className="h-5 w-5" />
                  My Published Policies - Can Request Exception (
                  {actionCategories.exceptionRequests.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.exceptionRequests.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-purple-200 bg-purple-50 p-3 transition-colors hover:bg-purple-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          {policy.publishedDate && (
                            <Badge
                              variant="outline"
                              className="text-xs text-green-600"
                            >
                              Published:{' '}
                              {new Date(
                                policy.publishedDate,
                              ).toLocaleDateString()}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Type: {policy.policyType}</span>
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-purple-600 hover:text-purple-700"
                        >
                          <AlertTriangle className="mr-1 h-3 w-3" />
                          Request Exception
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Exception Review */}
          {actionCategories.exceptionReview.length > 0 && (
            <Card className="border-l-4 border-l-indigo-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-indigo-700">
                  <Eye className="h-5 w-5" />
                  Exception Requests - Awaiting My Review (
                  {actionCategories.exceptionReview.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.exceptionReview.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-indigo-200 bg-indigo-50 p-3 transition-colors hover:bg-indigo-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          {policy.exceptionRequestedDate && (
                            <Badge
                              variant="outline"
                              className="text-xs text-purple-600"
                            >
                              Requested:{' '}
                              {new Date(
                                policy.exceptionRequestedDate,
                              ).toLocaleDateString()}
                            </Badge>
                          )}
                          {policy.exceptionRequest?.exceptionType && (
                            <Badge
                              variant="outline"
                              className="text-xs text-indigo-600"
                            >
                              {policy.exceptionRequest.exceptionType}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          <span>
                            Requested by:{' '}
                            {policy.exceptionRequest?.requestedBy?.name}
                          </span>
                        </div>
                        {policy.exceptionRequest?.justification && (
                          <div className="mt-2 text-sm text-gray-700">
                            <span className="font-medium">Justification:</span>{' '}
                            {policy.exceptionRequest.justification}
                          </div>
                        )}
                        {policy.exceptionRequest?.specificSection && (
                          <div className="mt-1 text-sm text-gray-700">
                            <span className="font-medium">Section:</span>{' '}
                            {policy.exceptionRequest.specificSection}
                          </div>
                        )}
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <ThumbsUp className="mr-1 h-3 w-3" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <ThumbsDown className="mr-1 h-3 w-3" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Exception Approval */}
          {actionCategories.exceptionApproval.length > 0 && (
            <Card className="border-l-4 border-l-pink-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-pink-700">
                  <CheckCircle className="h-5 w-5" />
                  Exception - Awaiting My Final Approval (
                  {actionCategories.exceptionApproval.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {actionCategories.exceptionApproval.map((policy) => (
                    <div
                      key={policy._id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-pink-200 bg-pink-50 p-3 transition-colors hover:bg-pink-100"
                      onClick={() => handlePolicyClick(policy)}
                    >
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {policy.name}
                          </h4>
                          <StatusBadge status={policy.status} />
                          <Badge
                            variant="outline"
                            className="text-xs text-blue-600"
                          >
                            {policy.exceptionRequest?.exceptionType ===
                            'Material Exception'
                              ? 'Board Approval'
                              : 'CEO Approval'}
                          </Badge>
                          {policy.exceptionRequest?.exceptionType && (
                            <Badge
                              variant="outline"
                              className="text-xs text-pink-600"
                            >
                              {policy.exceptionRequest.exceptionType}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>ID: {policy.policyId}</span>
                          <span>Dept: {policy.department}</span>
                          <span>Owner: {policy.policyOwner?.name}</span>
                          <span>
                            Requested by:{' '}
                            {policy.exceptionRequest?.requestedBy?.name}
                          </span>
                        </div>
                        {policy.exceptionRequest?.justification && (
                          <div className="mt-2 text-sm text-gray-700">
                            <span className="font-medium">Justification:</span>{' '}
                            {policy.exceptionRequest.justification}
                          </div>
                        )}
                        {policy.exceptionRequest?.specificSection && (
                          <div className="mt-1 text-sm text-gray-700">
                            <span className="font-medium">Section:</span>{' '}
                            {policy.exceptionRequest.specificSection}
                          </div>
                        )}
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-purple-600 hover:bg-purple-700"
                        >
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Approve Exception
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <ThumbsDown className="mr-1 h-3 w-3" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* No Actions Available */}
          {actionablePolicies.length === 0 && !policiesLoading && (
            <Card className="border-l-4 border-l-gray-300">
              <CardContent className="pt-6">
                <div className="py-8 text-center">
                  <Target className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    No Actions Required
                  </h3>
                  <p className="mb-4 text-gray-600">
                    You dont have any policies requiring action at this time.
                  </p>
                  <div className="flex justify-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/policies/manage')}
                    >
                      View All Policies
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => router.push('/policies/policyhub')}
                    >
                      Browse Policy Hub
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={handleStatusUpdate}
        onGovernanceReview={handleGovernanceReview}
        onGrcReview={handleGrcReview}
        onSubmitForReview={handleSubmitForReview}
        onApprovePolicy={handleApprovePolicy}
        onRejectPolicy={handleRejectPolicy}
        onRequestRetirement={handleRequestRetirement}
        onRetirementGovernanceReview={handleRetirementGovernanceReview}
        onApproveRetirement={handleApproveRetirement}
        onRejectRetirement={handleRejectRetirement}
        onRequestException={handleRequestException}
        onExceptionGovernanceReview={handleExceptionGovernanceReview}
        onApproveException={handleApproveException}
        onRejectException={handleRejectException}
        onPolicyUpdate={handlePolicyUpdate}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default MyActions;
