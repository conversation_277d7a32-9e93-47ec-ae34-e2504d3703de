version: '3.8'

services:
  onlyoffice-documentserver:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-documentserver-dev
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      # JWT Configuration - disabled for development
      - JWT_ENABLED=false
      - JWT_SECRET=
      - JWT_HEADER=Authorization

      # Storage and security settings
      - USE_UNAUTHORIZED_STORAGE=true
      - WOPI_ENABLED=false

      # File size and timeout limits
      - DOC_SERV_MAX_FILE_SIZE=50000000
      - DOC_SERV_TIMEOUT=120000

      # Network configuration for Docker Desktop
      - ALLOW_PRIVATE_IP_ADDRESS=true
      - ALLOW_META_IP_ADDRESS=true
      - ALLOW_HTTPS_INSECURE=true
      - ALLOW_HTTP_REQUEST_TO_HTTPS_RESOURCE=true

      # Additional security settings for development
      - REJECT_UNAUTHORIZED=false
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      
    volumes:
      - onlyoffice_data_dev:/var/www/onlyoffice/Data
      - onlyoffice_logs_dev:/var/log/onlyoffice
      - ../documents:/var/www/onlyoffice/documentserver/App_Data/cache/files
    networks:
      - onlyoffice-dev-network

volumes:
  onlyoffice_data_dev:
  onlyoffice_logs_dev:

networks:
  onlyoffice-dev-network:
    driver: bridge
