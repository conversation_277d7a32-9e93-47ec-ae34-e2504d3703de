'use client';

import { useState, useEffect } from 'react';
import { DataTable } from '@/app/policies/policyhub/components/datatable';
import { columns } from '@/app/policies/policyhub/components/column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';

const Policyhub = () => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [activeTab, setActiveTab] = useState<
    'Published' | 'Archived' | 'Retired'
  >('Published');

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch policies based on active tab
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    updatePolicyStatus,
    governanceReview,
    grcReview,
    submitPolicyForReview,
    approvePolicy,
    rejectPolicy,
  } = usePolicies({
    status: activeTab, // Filter based on active tab
    autoFetch: false,
  });

  // Trigger policies fetch when authentication is ready or tab changes
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies, activeTab]);

  // Handle policy row click to open details modal
  const handlePolicyClick = (policy: Policy) => {
    setSelectedPolicy(policy);
    setIsDetailsModalOpen(true);
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle status update from details modal
  const handleStatusUpdate = async (
    policyId: string,
    newStatus: string,
    comments?: string,
    effectiveDate?: string,
    selectedGroups?: string[],
  ): Promise<boolean> => {
    const result = await updatePolicyStatus(
      policyId,
      newStatus,
      comments,
      effectiveDate,
      selectedGroups,
    );
    // Refresh data after status update
    refetchPolicies();
    return result !== null;
  };

  // Handle governance review from details modal
  const handleGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await governanceReview(policyId, decision, comments);
    // Refresh data after governance review
    refetchPolicies();
    return result;
  };

  // Handle GRC review from details modal
  const handleGrcReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await grcReview(policyId, decision, comments);
    // Refresh data after GRC review
    refetchPolicies();
    return result;
  };

  // Handle submit for review from details modal
  const handleSubmitForReview = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await submitPolicyForReview(policyId, comments);
    // Refresh data after submission
    refetchPolicies();
    return result;
  };

  // Handle approve policy from details modal
  const handleApprovePolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approvePolicy(policyId, comments);
    // Refresh data after approval
    refetchPolicies();
    return result;
  };

  // Handle reject policy from details modal
  const handleRejectPolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectPolicy(policyId, comments);
    // Refresh data after rejection
    refetchPolicies();
    return result;
  };

  // Transform API data to match table structure
  const transformedPolicies = policies.map((policy, index) => ({
    id: index,
    policyId: policy.policyId,
    documentCode: policy.documentCode || '',
    name: policy.name,
    documentName: policy.documentName || policy.name,
    documentType: policy.documentType || 'Policy',
    version: policy.version || '1.0',
    versionNumber: policy.versionNumber || 1.0,
    policyType: policy.policyType || 'Corporate',
    categories: policy.categories.join(', '),
    department: policy.department,
    subDepartment: policy.subDepartment || '',
    policyOwner: policy.policyOwner.name,
    priorityScore: policy.priorityScore || 5,
    status: policy.status,
    detailedStatus: policy.detailedStatus || '',
    classification: policy.classification || 'Internal',
    startDate: policy.startDate || '',
    dueDate: policy.dueDate || '',
    completionDate: policy.completionDate || '',
    approvalDate: policy.approvalDate || '',
    authorizedApprover: policy.authorizedApprover?.name || '',
    lastReviewDate: policy.lastReviewDate || '',
    nextReviewDate: policy.nextReviewDate || '',
    initiatedBy: policy.initiatedBy?.name || '',
    reviewedBy: policy.reviewedBy?.map((r) => r.name).join(', ') || '',
    endorsedBy: policy.endorsedBy?.map((e) => e.name).join(', ') || '',
    publishedDate: policy.publishedDate || '',
    effectiveDate: policy.effectiveDate || '',
    retiredDate: policy.retiredDate || '',
    retirementJustification: policy.retirementRequest?.justification || '',
  }));

  // Calculate category counts from current policies
  const categoryCounts = policies.reduce(
    (acc, policy) => {
      policy.categories.forEach((category) => {
        acc[category] = (acc[category] || 0) + 1;
      });
      return acc;
    },
    {} as Record<string, number>,
  );

  // Create category cards based on active tab
  const cardData =
    activeTab === 'Published'
      ? [
          {
            title: 'All Published',
            number: policies.length.toString(),
            borderColor: 'border-l-[#1EE0AC]',
            numberColor: 'text-[#1EE0AC]',
            description: 'Total published policies',
          },
          {
            title: 'Corporate',
            number: (categoryCounts['Corporate'] || 0).toString(),
            borderColor: 'border-l-[#E85347]',
            numberColor: 'text-[#E85347]',
            description: 'Corporate policies',
          },
          {
            title: 'Compliance',
            number: (categoryCounts['Compliance'] || 0).toString(),
            borderColor: 'border-l-[#F4BD0E]',
            numberColor: 'text-[#F4BD0E]',
            description: 'Compliance policies',
          },
          {
            title: 'Organizational',
            number: (categoryCounts['Organizational'] || 0).toString(),
            borderColor: 'border-l-[#8B5CF6]',
            numberColor: 'text-[#8B5CF6]',
            description: 'Organizational policies',
          },
        ]
      : activeTab === 'Archived'
        ? [
            {
              title: 'All Archived',
              number: policies.length.toString(),
              borderColor: 'border-l-gray-500',
              numberColor: 'text-gray-600',
              description: 'Total archived policies',
            },
            {
              title: 'Corporate',
              number: (categoryCounts['Corporate'] || 0).toString(),
              borderColor: 'border-l-[#E85347]',
              numberColor: 'text-[#E85347]',
              description: 'Archived corporate policies',
            },
            {
              title: 'Compliance',
              number: (categoryCounts['Compliance'] || 0).toString(),
              borderColor: 'border-l-[#F4BD0E]',
              numberColor: 'text-[#F4BD0E]',
              description: 'Archived compliance policies',
            },
            {
              title: 'Organizational',
              number: (categoryCounts['Organizational'] || 0).toString(),
              borderColor: 'border-l-[#8B5CF6]',
              numberColor: 'text-[#8B5CF6]',
              description: 'Archived organizational policies',
            },
          ]
        : [
            {
              title: 'All Retired',
              number: policies.length.toString(),
              borderColor: 'border-l-gray-500',
              numberColor: 'text-gray-600',
              description: 'Total retired policies',
            },
            {
              title: 'Corporate',
              number: (categoryCounts['Corporate'] || 0).toString(),
              borderColor: 'border-l-[#E85347]',
              numberColor: 'text-[#E85347]',
              description: 'Retired corporate policies',
            },
            {
              title: 'Compliance',
              number: (categoryCounts['Compliance'] || 0).toString(),
              borderColor: 'border-l-[#F4BD0E]',
              numberColor: 'text-[#F4BD0E]',
              description: 'Retired compliance policies',
            },
            {
              title: 'Organizational',
              number: (categoryCounts['Organizational'] || 0).toString(),
              borderColor: 'border-l-[#8B5CF6]',
              numberColor: 'text-[#8B5CF6]',
              description: 'Retired organizational policies',
            },
          ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message={`Loading ${activeTab.toLowerCase()} policies...`}
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Policies"
          message={`We couldn't load the ${activeTab.toLowerCase()} policies. Please try again.`}
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="bg-gray-100 py-2 font-roboto">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">Policy Hub</h1>
            <p className="mt-1 text-sm text-gray-600">
              Browse and access published, archived, and retired policies
            </p>
          </div>
        </div>

        {/* Policy Type Toggle Cards */}
        <div className="mb-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {/* Published Policies Card */}
            <Card
              className={`cursor-pointer border-l-4 transition-all duration-200 ${
                activeTab === 'Published'
                  ? 'border-l-green-500 bg-gradient-to-r from-green-50 to-blue-50 shadow-md'
                  : 'border-l-gray-300 bg-white hover:shadow-md'
              }`}
              onClick={() => setActiveTab('Published')}
            >
              <CardHeader>
                <CardTitle
                  className={`flex items-center justify-between text-lg font-bold ${
                    activeTab === 'Published'
                      ? 'text-green-700'
                      : 'text-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`h-3 w-3 rounded-full ${
                        activeTab === 'Published'
                          ? 'bg-green-500'
                          : 'bg-gray-400'
                      }`}
                    ></div>
                    <span>Published Policies</span>
                  </div>
                  <div
                    className={`text-3xl font-bold ${
                      activeTab === 'Published'
                        ? 'text-green-700'
                        : 'text-gray-600'
                    }`}
                  >
                    {activeTab === 'Published'
                      ? policiesLoading
                        ? '...'
                        : policies.length
                      : '•'}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Access all published and active policies. These policies are
                  officially approved and in effect.
                </p>
              </CardContent>
            </Card>

            {/* Archived Policies Card */}
            <Card
              className={`cursor-pointer border-l-4 transition-all duration-200 ${
                activeTab === 'Archived'
                  ? 'border-l-gray-500 bg-gradient-to-r from-gray-50 to-gray-100 shadow-md'
                  : 'border-l-gray-300 bg-white hover:shadow-md'
              }`}
              onClick={() => setActiveTab('Archived')}
            >
              <CardHeader>
                <CardTitle
                  className={`flex items-center justify-between text-lg font-bold ${
                    activeTab === 'Archived' ? 'text-gray-700' : 'text-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`h-3 w-3 rounded-full ${
                        activeTab === 'Archived' ? 'bg-gray-500' : 'bg-gray-400'
                      }`}
                    ></div>
                    <span>Archived Policies</span>
                  </div>
                  <div
                    className={`text-3xl font-bold ${
                      activeTab === 'Archived'
                        ? 'text-gray-700'
                        : 'text-gray-600'
                    }`}
                  >
                    {activeTab === 'Archived'
                      ? policiesLoading
                        ? '...'
                        : policies.length
                      : '•'}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  View all archived policies. These policies are no longer
                  active but kept for reference and compliance.
                </p>
              </CardContent>
            </Card>

            {/* Retired Policies Card */}
            <Card
              className={`cursor-pointer border-l-4 transition-all duration-200 ${
                activeTab === 'Retired'
                  ? 'border-l-gray-500 bg-gradient-to-r from-gray-50 to-gray-100 shadow-md'
                  : 'border-l-gray-300 bg-white hover:shadow-md'
              }`}
              onClick={() => setActiveTab('Retired')}
            >
              <CardHeader>
                <CardTitle
                  className={`flex items-center justify-between text-lg font-bold ${
                    activeTab === 'Retired' ? 'text-gray-700' : 'text-gray-600'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`h-3 w-3 rounded-full ${
                        activeTab === 'Retired' ? 'bg-gray-500' : 'bg-gray-400'
                      }`}
                    ></div>
                    <span>Retired Policies</span>
                  </div>
                  <div
                    className={`text-3xl font-bold ${
                      activeTab === 'Retired'
                        ? 'text-gray-700'
                        : 'text-gray-600'
                    }`}
                  >
                    {activeTab === 'Retired'
                      ? policiesLoading
                        ? '...'
                        : policies.length
                      : '•'}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  View all retired policies. These policies are no longer active
                  but kept for reference and compliance.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedPolicies}
            loading={policiesLoading}
            onRowClick={(row) => {
              // Find the original policy from the transformed data
              const originalPolicy = policies.find(
                (p) => p._id === row.policyId || p.policyId === row.policyId,
              );
              if (originalPolicy) {
                handlePolicyClick(originalPolicy);
              }
            }}
          />
        </div>
      </div>

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={handleStatusUpdate}
        onGovernanceReview={handleGovernanceReview}
        onGrcReview={handleGrcReview}
        onSubmitForReview={handleSubmitForReview}
        onApprovePolicy={handleApprovePolicy}
        onRejectPolicy={handleRejectPolicy}
        onPolicyUpdate={refetchPolicies}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default Policyhub;
