'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, AlertTriangle, Info } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Policy } from '@/lib/api';

interface PolicyExceptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  policies: Policy[];
  onSubmit: (data: {
    policyId: string;
    justification: string;
    specificSection: string;
    exceptionType: 'Material Exception' | 'Immaterial Exception';
    effectiveDate?: string;
    expiryDate?: string;
    comments?: string;
  }) => Promise<void>;
  isSubmitting?: boolean;
}

export const PolicyExceptionModal: React.FC<PolicyExceptionModalProps> = ({
  isOpen,
  onClose,
  policies,
  onSubmit,
  isSubmitting = false,
}) => {
  const [formData, setFormData] = useState({
    policyId: '',
    justification: '',
    specificSection: '',
    exceptionType: '' as 'Material Exception' | 'Immaterial Exception' | '',
    effectiveDate: undefined as Date | undefined,
    expiryDate: undefined as Date | undefined,
    comments: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    const newErrors: Record<string, string> = {};

    if (!formData.policyId) {
      newErrors.policyId = 'Policy selection is required';
    }

    if (!formData.justification.trim()) {
      newErrors.justification = 'Justification is required';
    }

    if (!formData.specificSection.trim()) {
      newErrors.specificSection = 'Specific section is required';
    }

    if (!formData.exceptionType) {
      newErrors.exceptionType = 'Exception type is required';
    }

    if (
      formData.effectiveDate &&
      formData.expiryDate &&
      formData.effectiveDate >= formData.expiryDate
    ) {
      newErrors.expiryDate = 'Expiry date must be after effective date';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      return;
    }

    try {
      await onSubmit({
        policyId: formData.policyId,
        justification: formData.justification,
        specificSection: formData.specificSection,
        exceptionType: formData.exceptionType as
          | 'Material Exception'
          | 'Immaterial Exception',
        effectiveDate: formData.effectiveDate?.toISOString(),
        expiryDate: formData.expiryDate?.toISOString(),
        comments: formData.comments,
      });

      // Reset form
      setFormData({
        policyId: '',
        justification: '',
        specificSection: '',
        exceptionType: '',
        effectiveDate: undefined,
        expiryDate: undefined,
        comments: '',
      });
      setErrors({});
      onClose();
    } catch (error) {
      console.error('Error submitting exception request:', error);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        policyId: '',
        justification: '',
        specificSection: '',
        exceptionType: '',
        effectiveDate: undefined,
        expiryDate: undefined,
        comments: '',
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Request Policy Exception
          </DialogTitle>
          <DialogDescription>
            Submit a request for an exception to a published policy
          </DialogDescription>
        </DialogHeader>

        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-start space-x-2">
            <Info className="mt-0.5 h-4 w-4 text-blue-600" />
            <div className="text-sm text-blue-800">
              <p className="mb-1 font-medium">
                Exception Approval Requirements:
              </p>
              <p>
                • <strong>Material Exception:</strong> Requires Board Committee
                approval
              </p>
              <p>
                • <strong>Immaterial Exception:</strong> Requires CEO approval
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Policy Selection */}
          <div className="space-y-2">
            <Label htmlFor="policyId">
              Select Policy <span className="text-red-500">*</span>
            </Label>
            <Select
              value={formData.policyId}
              onValueChange={(value) =>
                setFormData({ ...formData, policyId: value })
              }
            >
              <SelectTrigger
                className={errors.policyId ? 'border-red-500' : ''}
              >
                <SelectValue placeholder="Choose a policy for exception request" />
              </SelectTrigger>
              <SelectContent>
                {policies
                  .filter(
                    (policy) =>
                      policy.status === 'Published' &&
                      (!policy.exceptionStatus ||
                        policy.exceptionStatus === 'None' ||
                        policy.exceptionStatus === 'Rejected' ||
                        policy.exceptionStatus === 'Expired'),
                  )
                  .map((policy) => (
                    <SelectItem key={policy._id} value={policy._id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{policy.name}</span>
                        <span className="text-xs text-gray-500">
                          {policy.documentCode} • {policy.department} • v
                          {policy.version}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            {errors.policyId && (
              <p className="text-sm text-red-600">{errors.policyId}</p>
            )}
            {policies.filter(
              (policy) =>
                policy.status === 'Published' &&
                (!policy.exceptionStatus ||
                  policy.exceptionStatus === 'None' ||
                  policy.exceptionStatus === 'Rejected' ||
                  policy.exceptionStatus === 'Expired'),
            ).length === 0 && (
              <p className="text-sm text-amber-600">
                No published policies available for exception requests.
              </p>
            )}
          </div>

          {/* Exception Type */}
          <div className="space-y-2">
            <Label htmlFor="exceptionType">
              Exception Type <span className="text-red-500">*</span>
            </Label>
            <Select
              value={formData.exceptionType}
              onValueChange={(
                value: 'Material Exception' | 'Immaterial Exception',
              ) => setFormData({ ...formData, exceptionType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select exception type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Material Exception">
                  <div className="flex flex-col">
                    <span>Material Exception</span>
                    <span className="text-xs text-gray-500">
                      Significant impact - Board approval required
                    </span>
                  </div>
                </SelectItem>
                <SelectItem value="Immaterial Exception">
                  <div className="flex flex-col">
                    <span>Immaterial Exception</span>
                    <span className="text-xs text-gray-500">
                      Minor impact - CEO approval required
                    </span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.exceptionType && (
              <p className="text-sm text-red-600">{errors.exceptionType}</p>
            )}
          </div>

          {/* Specific Section */}
          <div className="space-y-2">
            <Label htmlFor="specificSection">
              Specific Section or Clause <span className="text-red-500">*</span>
            </Label>
            <Input
              id="specificSection"
              placeholder="e.g., Section 3.2.1 - Data Retention Requirements"
              value={formData.specificSection}
              onChange={(e) =>
                setFormData({ ...formData, specificSection: e.target.value })
              }
              className={errors.specificSection ? 'border-red-500' : ''}
            />
            {errors.specificSection && (
              <p className="text-sm text-red-600">{errors.specificSection}</p>
            )}
          </div>

          {/* Justification */}
          <div className="space-y-2">
            <Label htmlFor="justification">
              Justification <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="justification"
              placeholder="Provide detailed justification for this exception request..."
              value={formData.justification}
              onChange={(e) =>
                setFormData({ ...formData, justification: e.target.value })
              }
              className={`min-h-[100px] ${errors.justification ? 'border-red-500' : ''}`}
            />
            {errors.justification && (
              <p className="text-sm text-red-600">{errors.justification}</p>
            )}
          </div>

          {/* Effective Date */}
          <div className="space-y-2">
            <Label>Effective Date (Optional)</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !formData.effectiveDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.effectiveDate ? (
                    format(formData.effectiveDate, 'PPP')
                  ) : (
                    <span>Pick effective date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.effectiveDate}
                  onSelect={(date) =>
                    setFormData({ ...formData, effectiveDate: date })
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Expiry Date */}
          <div className="space-y-2">
            <Label>Expiry Date (Optional)</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !formData.expiryDate && 'text-muted-foreground',
                    errors.expiryDate && 'border-red-500',
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.expiryDate ? (
                    format(formData.expiryDate, 'PPP')
                  ) : (
                    <span>Pick expiry date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.expiryDate}
                  onSelect={(date) =>
                    setFormData({ ...formData, expiryDate: date })
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {errors.expiryDate && (
              <p className="text-sm text-red-600">{errors.expiryDate}</p>
            )}
          </div>

          {/* Additional Comments */}
          <div className="space-y-2">
            <Label htmlFor="comments">Additional Comments (Optional)</Label>
            <Textarea
              id="comments"
              placeholder="Any additional information or context..."
              value={formData.comments}
              onChange={(e) =>
                setFormData({ ...formData, comments: e.target.value })
              }
              className="min-h-[80px]"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Submitting...' : 'Submit Exception Request'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
