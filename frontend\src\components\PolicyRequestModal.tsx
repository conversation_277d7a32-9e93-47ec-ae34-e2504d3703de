'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { useUsers } from '@/hooks/useUsers';
import { useAuth } from '@/contexts/AuthContext';

interface PolicyRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (policyData: {
    name: string;
    description?: string;
    documentName?: string;
    documentCode?: string;
    documentType?: string;
    version?: string;
    policyCategory: 'Corporate Policies' | 'Operational Policies';
    policyType?: string;
    categories: string[];
    department: string;
    subDepartment?: string;
    priorityScore?: number;
    classification?: string;
    priority?: string;
    dueDate?: string;
    startDate?: string;
    nextReviewDate?: string;
  }) => Promise<void>;
  loading?: boolean;
}

const CATEGORIES = [
  'Organizational',
  'Compliance',
  'Corporate',
  'Security',
  'HR',
  'IT',
  'Finance',
];

// Golf Saudi departments - will be replaced with real user data
const FALLBACK_DEPARTMENTS = [
  'PMO Advisory Investment Activation',
  'Development',
  'Legal',
  'Procurement',
  'Marketing',
  'Golf Excellence',
  'Risk Management',
  'HR',
  'Sales & Sponsorships',
  'Events',
  'Compliance',
  'Finance',
  'Federation Office',
  'Academies',
  'Governance',
  'Business Continuity Management',
  'Events Management',
  'Local Golf',
  'Tournaments',
  'Internal Audit',
  'IT',
  'Cybersecurity',
  'Admin & HSSE',
  'CEO Office',
  'Strategy',
  'Golf Operations',
];

const PRIORITIES = ['Low', 'Medium', 'High', 'Critical'];

const DOCUMENT_TYPES = [
  'Policy',
  'SoW',
  'Framework',
  'Procedure',
  'Guideline',
  'Standard',
];

const POLICY_CATEGORIES = ['Corporate Policies', 'Operational Policies'];

const POLICY_TYPES = [
  'Corporate',
  'Operational',
  'Technical',
  'Administrative',
  'Strategic',
];

const CLASSIFICATIONS = [
  'Public',
  'Internal',
  'Confidential',
  'Restricted',
  'Top Secret',
];

export const PolicyRequestModal: React.FC<PolicyRequestModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const { user } = useAuth();
  const { users, fetchUsers } = useUsers();

  // ✅ Move ALL useState hooks here, before any conditional logic
  const [availableDepartments, setAvailableDepartments] = useState<string[]>(
    [],
  );
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    documentName: '',
    documentCode: '',
    documentType: 'Policy',
    version: '1.0',
    policyCategory: 'Operational Policies' as
      | 'Corporate Policies'
      | 'Operational Policies',
    policyType: 'Corporate',
    categories: [] as string[],
    department: '',
    subDepartment: '',
    priorityScore: 5,
    classification: 'Internal',
    priority: 'Medium',
    dueDate: '',
    startDate: '',
    nextReviewDate: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // ✅ Move ALL useEffect hooks here too
  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen, fetchUsers]);

  useEffect(() => {
    if (users.length > 0) {
      const departments = [
        ...new Set(users.map((u) => u.department).filter(Boolean)),
      ];
      setAvailableDepartments(
        departments.length > 0 ? departments : FALLBACK_DEPARTMENTS,
      );
    } else {
      setAvailableDepartments(FALLBACK_DEPARTMENTS);
    }

    if (user?.department && formData.department === '') {
      setFormData((prev) => ({ ...prev, department: user.department }));
    }
  }, [users, user, formData.department]);

  // Check if user has permission to create policy requests
  const canCreatePolicyRequest = () => {
    if (!user) return false;
    return ['Creator', 'Admin', 'Super Admin'].includes(user.role);
  };

  // ✅ Now the conditional return is after all hooks
  if (!canCreatePolicyRequest()) {
    return null;
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const handleCategoryChange = (category: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      categories: checked
        ? [...prev.categories, category]
        : prev.categories.filter((c) => c !== category),
    }));

    if (errors.categories) {
      setErrors((prev) => ({ ...prev, categories: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Policy name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Policy name must be at least 3 characters';
    }

    if (formData.categories.length === 0) {
      newErrors.categories = 'At least one category is required';
    }

    if (!formData.department) {
      newErrors.department = 'Department is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        documentName: formData.documentName.trim() || undefined,
        documentCode: formData.documentCode.trim() || undefined,
        documentType: formData.documentType,
        version: formData.version,
        policyCategory: formData.policyCategory,
        policyType: formData.policyType,
        categories: formData.categories,
        department: formData.department,
        subDepartment: formData.subDepartment.trim() || undefined,
        priorityScore: formData.priorityScore,
        classification: formData.classification,
        priority: formData.priority,
        dueDate: formData.dueDate || undefined,
        startDate: formData.startDate || undefined,
        nextReviewDate: formData.nextReviewDate || undefined,
      });

      // Reset form on successful submission
      setFormData({
        name: '',
        description: '',
        documentName: '',
        documentCode: '',
        documentType: 'Policy',
        version: '1.0',
        policyCategory: 'Operational Policies',
        policyType: 'Corporate',
        categories: [],
        department: '',
        subDepartment: '',
        priorityScore: 5,
        classification: 'Internal',
        priority: 'Medium',
        dueDate: '',
        startDate: '',
        nextReviewDate: '',
      });
      setErrors({});
      onClose();
    } catch (error) {
      console.error('Failed to submit policy request:', error);

      // Handle specific error cases
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';

      // Check if it's a duplicate document code error
      if (
        errorMessage.includes('already exists') ||
        errorMessage.includes('duplicate')
      ) {
        setErrors({ documentCode: errorMessage });
      } else {
        // For other errors, show a general error message
        setErrors({ general: errorMessage });
      }
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        name: '',
        description: '',
        documentName: '',
        documentCode: '',
        documentType: 'Policy',
        version: '1.0',
        policyCategory: 'Operational Policies',
        policyType: 'Corporate',
        categories: [],
        department: '',
        subDepartment: '',
        priorityScore: 5,
        classification: 'Internal',
        priority: 'Medium',
        dueDate: '',
        startDate: '',
        nextReviewDate: '',
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPrimitive.Portal>
        {/* Transparent overlay */}
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black opacity-40" />
        {/* Custom content without default overlay */}
        <DialogPrimitive.Content
          className={cn(
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid max-h-[90vh] w-full max-w-4xl translate-x-[-50%] translate-y-[-50%] gap-4 overflow-y-auto border bg-background p-6 shadow-lg duration-200 sm:rounded-lg',
          )}
        >
          <DialogHeader>
            <DialogTitle>Request Policy Initiation</DialogTitle>
            <DialogDescription>
              Fill out the form below to request the initiation of a new policy
              with all required metadata.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* General Error Display */}
            {errors.general && (
              <div className="rounded-md border border-red-200 bg-red-50 p-4">
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            )}

            {/* Document Information Section */}
            <div className="space-y-4">
              <h3 className="border-b pb-2 text-lg font-semibold text-gray-900">
                Document Information
              </h3>

              <div className="grid grid-cols-2 gap-4">
                {/* Policy Name */}
                <div className="space-y-2">
                  <Label htmlFor="name">Policy Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter policy name"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name}</p>
                  )}
                </div>

                {/* Document Name */}
                <div className="space-y-2">
                  <Label htmlFor="documentName">Document Name</Label>
                  <Input
                    id="documentName"
                    value={formData.documentName}
                    onChange={(e) =>
                      handleInputChange('documentName', e.target.value)
                    }
                    placeholder="Enter document name (optional)"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* Document Code */}
                <div className="space-y-2">
                  <Label htmlFor="documentCode">Document Code</Label>
                  <Input
                    id="documentCode"
                    value={formData.documentCode}
                    onChange={(e) =>
                      handleInputChange('documentCode', e.target.value)
                    }
                    placeholder="Auto-generated if left empty"
                    className={errors.documentCode ? 'border-red-500' : ''}
                  />
                  {errors.documentCode && (
                    <p className="text-sm text-red-500">
                      {errors.documentCode}
                    </p>
                  )}
                </div>

                {/* Document Type */}
                <div className="space-y-2">
                  <Label>Document Type</Label>
                  <Select
                    value={formData.documentType}
                    onValueChange={(value) =>
                      handleInputChange('documentType', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {DOCUMENT_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* Version */}
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={formData.version}
                    onChange={(e) =>
                      handleInputChange('version', e.target.value)
                    }
                    placeholder="e.g., 1.0, 2.1"
                  />
                </div>

                {/* Policy Category */}
                <div className="space-y-2">
                  <Label>Policy Category *</Label>
                  <Select
                    value={formData.policyCategory}
                    onValueChange={(value) =>
                      handleInputChange('policyCategory', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {POLICY_CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    Corporate: Board approval | Operational: CEO approval
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4">
                {/* Policy Type */}
                <div className="space-y-2">
                  <Label>Policy Type</Label>
                  <Select
                    value={formData.policyType}
                    onValueChange={(value) =>
                      handleInputChange('policyType', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {POLICY_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder="Enter policy description (optional)"
                  rows={3}
                />
              </div>
            </div>

            {/* Department & Ownership Section */}
            <div className="space-y-4">
              <h3 className="border-b pb-2 text-lg font-semibold text-gray-900">
                Department & Ownership
              </h3>

              <div className="grid grid-cols-2 gap-4">
                {/* Department */}
                <div className="space-y-2">
                  <Label>Department *</Label>
                  <Select
                    value={formData.department}
                    onValueChange={(value) =>
                      handleInputChange('department', value)
                    }
                  >
                    <SelectTrigger
                      className={errors.department ? 'border-red-500' : ''}
                    >
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDepartments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.department && (
                    <p className="text-sm text-red-500">{errors.department}</p>
                  )}
                </div>

                {/* Sub-Department */}
                <div className="space-y-2">
                  <Label htmlFor="subDepartment">Sub-Department</Label>
                  <Input
                    id="subDepartment"
                    value={formData.subDepartment}
                    onChange={(e) =>
                      handleInputChange('subDepartment', e.target.value)
                    }
                    placeholder="Enter sub-department (optional)"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* Priority Score */}
                <div className="space-y-2">
                  <Label htmlFor="priorityScore">Priority Score (1-10)</Label>
                  <Input
                    id="priorityScore"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.priorityScore}
                    onChange={(e) =>
                      handleInputChange(
                        'priorityScore',
                        parseInt(e.target.value) || 5,
                      )
                    }
                    placeholder="5"
                  />
                  <p className="text-xs text-gray-500">
                    1 = Low, 10 = Critical
                  </p>
                </div>

                {/* Classification */}
                <div className="space-y-2">
                  <Label>Classification</Label>
                  <Select
                    value={formData.classification}
                    onValueChange={(value) =>
                      handleInputChange('classification', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CLASSIFICATIONS.map((classification) => (
                        <SelectItem key={classification} value={classification}>
                          {classification}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Categories */}
              <div className="space-y-2">
                <Label>Categories *</Label>
                <div className="grid grid-cols-2 gap-2">
                  {CATEGORIES.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={category}
                        checked={formData.categories.includes(category)}
                        onCheckedChange={(checked) =>
                          handleCategoryChange(category, checked as boolean)
                        }
                      />
                      <Label htmlFor={category} className="text-sm">
                        {category}
                      </Label>
                    </div>
                  ))}
                </div>
                {errors.categories && (
                  <p className="text-sm text-red-500">{errors.categories}</p>
                )}
              </div>
            </div>

            {/* Timeline & Dates Section */}
            <div className="space-y-4">
              <h3 className="border-b pb-2 text-lg font-semibold text-gray-900">
                Timeline & Dates
              </h3>

              <div className="grid grid-cols-3 gap-4">
                {/* Start Date */}
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) =>
                      handleInputChange('startDate', e.target.value)
                    }
                  />
                </div>

                {/* Due Date */}
                <div className="space-y-2">
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={formData.dueDate}
                    onChange={(e) =>
                      handleInputChange('dueDate', e.target.value)
                    }
                  />
                </div>

                {/* Next Review Date */}
                <div className="space-y-2">
                  <Label htmlFor="nextReviewDate">Next Review Date</Label>
                  <Input
                    id="nextReviewDate"
                    type="date"
                    value={formData.nextReviewDate}
                    onChange={(e) =>
                      handleInputChange('nextReviewDate', e.target.value)
                    }
                  />
                </div>
              </div>
            </div>

            {/* Legacy Priority for backward compatibility */}
            <div className="space-y-2">
              <Label>Legacy Priority</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => handleInputChange('priority', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITIES.map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                This field is maintained for backward compatibility
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Submitting...' : 'Submit Request'}
              </Button>
            </DialogFooter>
          </form>

          {/* Close button */}
          <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
  );
};
