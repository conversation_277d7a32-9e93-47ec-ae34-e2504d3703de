'use client';

import * as React from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export type Programs = {
  id: number;
  policyId: string;
  documentCode: string;
  name: string;
  documentName: string;
  documentType: string;
  version: string;
  versionNumber: number;
  policyType: string;
  categories: string;
  department: string;
  subDepartment: string;
  policyOwner: string;
  priorityScore: number;
  status: string;
  detailedStatus: string;
  classification: string;
  startDate: string;
  dueDate: string;
  completionDate: string;
  approvalDate: string;
  authorizedApprover: string;
  lastReviewDate: string;
  nextReviewDate: string;
  initiatedBy: string;
  reviewedBy: string;
  endorsedBy: string;
  publishedDate: string;
  effectiveDate: string;
};

const columnHelper = createColumnHelper<Programs>();

export const columns = [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }),
  // 1. Policy ID - Unique identifier
  columnHelper.accessor('policyId', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy ID
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2 text-sm font-medium text-customBlueSecondary">
          <span>{row.getValue('policyId')}</span>
        </div>
      );
    },
    enableHiding: false,
  }),
  // 2. Policy Name - Main identifier
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy Name
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[200px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('name')}
      >
        {row.getValue('name')}
      </div>
    ),
    enableHiding: false,
  }),

  // 3. Document Type - Critical for categorization
  columnHelper.accessor('documentType', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Type</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('documentType') || 'Policy'}
      </div>
    ),
    enableHiding: false,
  }),

  // 4. Version - Document version tracking
  columnHelper.accessor('version', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Version
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('version') || '1.0'}
      </div>
    ),
    enableHiding: false,
  }),

  // 5. Department - Ownership and responsibility
  columnHelper.accessor('department', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Department
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('department')}
      </div>
    ),
    enableHiding: false,
  }),

  // 6. Policy Owner - Key stakeholder
  columnHelper.accessor('policyOwner', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Owner</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[120px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('policyOwner')}
      >
        {row.getValue('policyOwner')}
      </div>
    ),
    enableHiding: false,
  }),

  // 7. Categories - Policy categorization
  columnHelper.accessor('categories', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Categories
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[150px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('categories')}
      >
        {row.getValue('categories')}
      </div>
    ),
    enableHiding: true,
  }),

  // 8. Published Date - When policy was published
  columnHelper.accessor('publishedDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Published Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const publishedDate = row.getValue('publishedDate') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {publishedDate ? new Date(publishedDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 9. Effective Date - When policy becomes effective
  columnHelper.accessor('effectiveDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Effective Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const effectiveDate = row.getValue('effectiveDate') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {effectiveDate ? new Date(effectiveDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 10. Next Review Date - When policy needs review
  columnHelper.accessor('nextReviewDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Next Review
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const nextReviewDate = row.getValue('nextReviewDate') as string;
      const isOverdue = nextReviewDate && new Date(nextReviewDate) < new Date();
      return (
        <div
          className={`text-sm font-medium ${isOverdue ? 'text-red-600' : 'text-customBlueSecondary'}`}
        >
          {nextReviewDate
            ? new Date(nextReviewDate).toLocaleDateString()
            : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),
];
