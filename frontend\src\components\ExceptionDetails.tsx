'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Policy } from '@/lib/api';
import { AlertTriangle, Calendar, Clock, User, FileText } from 'lucide-react';

interface ExceptionDetailsProps {
  policy: Policy;
}

export const ExceptionDetails: React.FC<ExceptionDetailsProps> = ({
  policy,
}) => {
  // Helper function to get exception status color
  const getExceptionStatusColor = (status: string) => {
    switch (status) {
      case 'Approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Requested':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Under Review':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Pending Approval':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Helper function to get exception type color
  const getExceptionTypeColor = (type: string) => {
    switch (type) {
      case 'Material Exception':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Immaterial Exception':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Check if policy has any exception information
  const hasExceptionInfo =
    policy.exceptionStatus && policy.exceptionStatus !== 'None';

  if (!hasExceptionInfo) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <AlertTriangle className="h-5 w-5 text-gray-400" />
            Exception Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center">
            <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-gray-300" />
            <p className="font-medium text-gray-500">No Exception Requested</p>
            <p className="mt-1 text-sm text-gray-400">
              This policy currently has no active or pending exceptions.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Exception Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Exception Status Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium text-gray-600">
                Current Status
              </Label>
              <div className="mt-1">
                <Badge
                  variant="outline"
                  className={`${getExceptionStatusColor(policy.exceptionStatus || 'None')} font-medium`}
                >
                  {policy.exceptionStatus}
                </Badge>
              </div>
            </div>
            {policy.exceptionRequest?.exceptionType && (
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Exception Type
                </Label>
                <div className="mt-1">
                  <Badge
                    variant="outline"
                    className={`${getExceptionTypeColor(policy.exceptionRequest.exceptionType)} font-medium`}
                  >
                    {policy.exceptionRequest.exceptionType}
                  </Badge>
                </div>
              </div>
            )}
          </div>

          {/* Timeline Information */}
          <div className="grid grid-cols-1 gap-4 border-t pt-4 md:grid-cols-3">
            {policy.exceptionRequestedDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-xs text-gray-600">Requested</p>
                  <p className="text-sm font-medium">
                    {new Date(
                      policy.exceptionRequestedDate,
                    ).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}
            {policy.exceptionApprovedDate && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-xs text-gray-600">Approved</p>
                  <p className="text-sm font-medium">
                    {new Date(
                      policy.exceptionApprovedDate,
                    ).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}
            {policy.exceptionRejectedDate && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-xs text-gray-600">Rejected</p>
                  <p className="text-sm font-medium">
                    {new Date(
                      policy.exceptionRejectedDate,
                    ).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Exception Request Details */}
      {policy.exceptionRequest && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <FileText className="h-5 w-5 text-blue-600" />
              Exception Request Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Requested By */}
            {policy.exceptionRequest.requestedBy && (
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Requested By
                </Label>
                <div className="mt-1 flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">
                    {policy.exceptionRequest.requestedBy.name} (
                    {policy.exceptionRequest.requestedBy.email})
                  </span>
                </div>
              </div>
            )}

            {/* Justification */}
            {policy.exceptionRequest.justification && (
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Justification
                </Label>
                <div className="mt-1 rounded-lg border bg-gray-50 p-3">
                  <p className="whitespace-pre-wrap text-sm text-gray-700">
                    {policy.exceptionRequest.justification}
                  </p>
                </div>
              </div>
            )}

            {/* Specific Section */}
            {policy.exceptionRequest.specificSection && (
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Specific Policy Section
                </Label>
                <div className="mt-1 rounded-lg border bg-blue-50 p-3">
                  <p className="text-sm font-medium text-blue-800">
                    {policy.exceptionRequest.specificSection}
                  </p>
                </div>
              </div>
            )}

            {/* Dates */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {policy.exceptionRequest.effectiveDate && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Effective Date
                  </Label>
                  <p className="mt-1 text-sm">
                    {new Date(
                      policy.exceptionRequest.effectiveDate,
                    ).toLocaleDateString()}
                  </p>
                </div>
              )}
              {policy.exceptionRequest.expiryDate && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Expiry Date
                  </Label>
                  <p className="mt-1 text-sm">
                    {new Date(
                      policy.exceptionRequest.expiryDate,
                    ).toLocaleDateString()}
                    {new Date(policy.exceptionRequest.expiryDate) <
                      new Date() && (
                      <Badge
                        variant="outline"
                        className="ml-2 border-red-200 bg-red-100 text-red-800"
                      >
                        Expired
                      </Badge>
                    )}
                  </p>
                </div>
              )}
            </div>

            {/* Additional Comments */}
            {policy.exceptionRequest.comments && (
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Additional Comments
                </Label>
                <div className="mt-1 rounded-lg border bg-gray-50 p-3">
                  <p className="whitespace-pre-wrap text-sm text-gray-700">
                    {policy.exceptionRequest.comments}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
