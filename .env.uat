# Server Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
MONGODB_URI=mongodb+srv://anurag98cs:<EMAIL>/grc-web-app?retryWrites=true&w=majority&appName=Cluster0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-uat-env
JWT_EXPIRE=7d

# Frontend Configuration
FRONTEND_URL=https://grc-v4.autoresilience.com

# OnlyOffice Configuration
ONLYOFFICE_JWT_SECRET=your-onlyoffice-jwt-secret-uat
# OnlyOffice will call back to backend service directly via container networking
BACKEND_INTERNAL_URL=http://backend:5000

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=52428800
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# UAT Specific Settings
ENVIRONMENT=uat
DOMAIN=grc-v4.autoresilience.com