'use client';

import * as React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';
import { cn } from '@/lib/utils';

interface CustomProgressProps {
  value: number;
}

const CustomProgress: React.FC<CustomProgressProps> = ({ value }) => {
  let progressBgColor = '#Eaeaea';

  if (value >= 65) {
    progressBgColor = '#1EE0AC';
  } else if (value >= 33) {
    progressBgColor = '#F4BD0E';
  } else {
    progressBgColor = '#E85347';
  }

  return (
    <ProgressPrimitive.Root
      className="relative h-2 w-full overflow-hidden rounded-full bg-gray-300"
      value={value}
    >
      <ProgressPrimitive.Indicator
        className={cn('h-full transition-all')}
        style={{
          transform: `translateX(-${100 - value}%)`,
          backgroundColor: progressBgColor,
        }}
      />
    </ProgressPrimitive.Root>
  );
};

export default CustomProgress;
