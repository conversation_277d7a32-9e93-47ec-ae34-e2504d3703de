'use client';

import React from 'react';

import { cva, type VariantProps } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        primary: 'bg-blue-700 text-white hover:bg-blue-800',
        primaryOutline:
          'border border-blue-700 text-blue-700 hover:bg-blue-700 hover:text-white',
        primaryDisabled: 'bg-gray-400 text-white cursor-not-allowed',
        secondary: 'bg-teal-500 text-white hover:bg-teal-600',
        secondaryOutline:
          'border border-teal-500 text-teal-500 hover:bg-teal-500 hover:text-white',
        secondaryDisabled: 'bg-gray-200 text-gray-400 cursor-not-allowed',
        success: 'bg-green-500 text-white hover:bg-green-600',
        danger: 'bg-red-500 text-white hover:bg-red-600',
        warning: 'bg-yellow-500 text-white hover:bg-yellow-600',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 px-3 py-1.5',
        lg: 'h-11 px-6 py-3',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'default',
    },
  },
);

interface CustomButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  label: string;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  label,
  variant,
  size,
  className,
  ...props
}) => {
  return (
    <button
      className={`${buttonVariants({ variant, size, className })}`}
      {...props}
    >
      {label}
    </button>
  );
};

export default CustomButton;
