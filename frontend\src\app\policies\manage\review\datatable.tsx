'use client';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons';

import * as React from 'react';
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Button } from '@/components/ui/button';

import { Input } from '@/components/ui/input';
import SearchIcon from '@/assests/images/searchIcon.svg';
import PdfIcon from '@/assests/images/pdf.svg';
import XlsIcon from '@/assests/images/xls.svg';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { columns, ReviewPrograms } from './column';

interface DataTableProps {
  columns: typeof columns;
  data: ReviewPrograms[];
  loading?: boolean;
  onRowClick?: (row: ReviewPrograms) => void;
}
type ColumnSort = {
  id: string;
  desc: boolean;
};
interface ColumnFilter {
  id: string;
  value: unknown;
}
type ColumnFiltersState = ColumnFilter[];

type SortingState = ColumnSort[];

export function DataTable({
  columns,
  data,
  loading = false,
  onRowClick,
}: DataTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] = React.useState({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="rounded-md border border-customBorder bg-white">
      <h1 className="px-5 py-5 font-roboto text-base font-semibold text-customBlue">
        Policy Review Management Hub
      </h1>
      <div className="flex w-full items-center justify-between px-5 pb-5">
        <div className="flex items-center space-x-8">
          <div className="flex space-x-4">
            <Select>
              <SelectTrigger className="border-customBorder font-bold text-customBlue">
                <SelectValue
                  className="text-customBlue"
                  placeholder="Bulk Action"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="export">Export Selected</SelectItem>
                <SelectItem value="initiate">Initiate Review</SelectItem>
                <SelectItem value="remind">Send Reminder</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="secondary" disabled className="text-base">
              Apply
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <p className="text-customBlueSecondary">Export</p>
            <div className="flex">
              <div className="rounded border border-customBorder p-1">
                <XlsIcon />
              </div>
              <div className="rounded border border-customBorder p-1">
                <PdfIcon />
              </div>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            Showing {table.getFilteredRowModel().rows.length} of {data.length}{' '}
            review policies
          </div>
        </div>
        <div className="flex items-center space-x-8">
          <div className="relative">
            <Input
              placeholder="Search review policies..."
              value={
                (table.getColumn('name')?.getFilterValue() as string) ?? ''
              }
              onChange={(event) =>
                table.getColumn('name')?.setFilterValue(event.target.value)
              }
              className="rounded-full border-customBorder px-8 font-roboto"
            />
            <div className="absolute left-2 top-1/2 -translate-y-1/2 transform">
              <SearchIcon className="h-5 w-4 text-gray-500" />
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-10 border-customBorder">
                <span className="material-symbols-outlined text-customBlueSecondary">
                  filter_list
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="p-4">
              <div className="space-y-2">
                <Select>
                  <SelectTrigger className="border-customBorder font-bold text-customBlue">
                    <SelectValue
                      className="text-customBlue"
                      placeholder="Fields"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="reviewStatus">Review Status</SelectItem>
                    <SelectItem value="reviewType">Review Type</SelectItem>
                    <SelectItem value="department">Department</SelectItem>
                    <SelectItem value="classification">
                      Classification
                    </SelectItem>
                    <SelectItem value="ownerReviewDecision">
                      Owner Decision
                    </SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="border-customBorder font-bold text-customBlue">
                    <SelectValue
                      className="text-customBlue"
                      placeholder="Condition"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals">Equals to</SelectItem>
                    <SelectItem value="not equals">Not Equals to</SelectItem>
                  </SelectContent>
                </Select>
                <Input placeholder="Filter value..." />
                <div className="space-x-2">
                  <Button className="bg-customBlue text-base">Apply</Button>
                  <Button variant="outline" className="text-base">
                    Cancel
                  </Button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <span className="material-symbols-outlined cursor-pointer text-customBlueSecondary">
                settings
              </span>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
          <div></div>
        </div>
      </div>
      <div className="mx-4 rounded-sm border border-customBorder">
        <Table>
          <TableHeader className="bg-customLight font-roboto">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Loading review policies...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  className="group cursor-pointer hover:bg-customHover"
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => onRowClick?.(row.original)}
                >
                  {row.getVisibleCells().map((cell, index) => (
                    <TableCell key={cell.id}>
                      {/* Render the cell content */}
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No review policies found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between px-4 py-4 text-customBlueSecondary">
        <div className="flex w-full items-center justify-between space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <DoubleArrowLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <DoubleArrowRightIcon className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => {
                  table.setPageSize(Number(value));
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue
                    placeholder={table.getState().pagination.pageSize}
                  />
                </SelectTrigger>
                <SelectContent side="top">
                  {[5, 10, 15, 20, 25].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
