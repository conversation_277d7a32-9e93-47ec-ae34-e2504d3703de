'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationProvider } from '@/contexts/NotificationContext';

import grclogo from '@/assests/images/grclogo.png';
import ascent from '@/assests/images/ascent.png';

import AppDrawer from '../all-apps/AppDrawer';
import UserPopover from '@/components/UserPopover';
import NotificationBell from '@/components/NotificationBell';
import { AppSidebar } from '@/components/app-sidebar';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, loading, user } = useAuth();

  // Check if we're on the editor route
  const isEditorRoute = pathname?.includes('/policies/editor/');

  // Handle authentication redirect - be more conservative
  useEffect(() => {
    console.log('Policies Layout Auth Check:', {
      loading,
      isAuthenticated,
      user: !!user,
      hasToken: !!localStorage.getItem('token'),
    });
    // Only redirect if we're sure there's no authentication
    if (!loading && !isAuthenticated && !localStorage.getItem('token')) {
      console.log('Redirecting to login from policies layout');
      router.push('/login');
    }
  }, [loading, isAuthenticated, router, user]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  // Show loading while redirecting if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Redirecting to login...</p>
      </div>
    );
  }
  // If we're on the editor route, render without navbar and sidebar
  if (isEditorRoute) {
    return <NotificationProvider>{children}</NotificationProvider>;
  }

  // Regular layout with navbar and sidebar for other routes
  return (
    <NotificationProvider>
      <main>
        <nav className="fixed left-0 top-0 z-30 flex h-16 w-full items-center justify-between border-b bg-white font-bold">
          <div className="ml-4 flex items-center justify-center space-x-6">
            <Image src={grclogo} alt="grclogo" className="h-10 w-10" />
            <p className="font-roboto text-xl font-semibold text-customBlue">
              GRC
            </p>
            {/* <p className="text-xl font-semibold text-customGray">
              Policy Management
            </p> */}
          </div>
          <div className="mr-4 flex items-center space-x-4">
            <NotificationBell />
            <AppDrawer />
            <UserPopover />
            <Image src={ascent} alt="ascent" className="mx-6 h-8 w-24" />
          </div>
        </nav>
        <SidebarProvider>
          <AppSidebar />
          <main className="mt-16 flex min-h-screen-minus-navbar w-full flex-col overflow-hidden bg-customCanvas">
            <div className="flex items-center gap-2 px-8 py-2">
              <SidebarTrigger />
            </div>
            <div className="mx-8 my-4 min-h-screen-minus-all">{children}</div>
          </main>
        </SidebarProvider>
      </main>
    </NotificationProvider>
  );
}
