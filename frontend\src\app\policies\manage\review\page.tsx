'use client';

import { useState, useEffect } from 'react';
import { DataTable } from './datatable';
import { columns, ReviewPrograms } from './column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';
import {
  Clock,
  FileSearch,
  CheckCircle,
  AlertTriangle,
  BarChart3,
} from 'lucide-react';

const Review = () => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch policies with review-related statuses
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    initiatePolicyReview,
    ownerReview,
    reviewGovernanceReview,
    approveReview,
    rejectReview,
  } = usePolicies({
    autoFetch: false,
    management: true,
    // Only fetch published policies since reviews are tracked separately
    status: 'Published',
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Update selected policy when policies data changes (for real-time updates)
  useEffect(() => {
    if (selectedPolicy && policies.length > 0) {
      const updatedPolicy = policies.find((p) => p._id === selectedPolicy._id);
      if (updatedPolicy) {
        setSelectedPolicy(updatedPolicy);
      }
    }
  }, [policies, selectedPolicy]);

  // Filter policies for review workflow (only policies with active review statuses or due for review)
  const reviewPolicies = policies.filter((policy) => {
    // Show policies that are due for review or have active review workflow
    const hasActiveReview =
      policy.reviewStatus &&
      policy.reviewStatus !== 'None' &&
      policy.reviewStatus !== 'Approved';

    // Show policies that are due for review (nextReviewDate has passed)
    const isDueForReview =
      policy.nextReviewDate &&
      new Date(policy.nextReviewDate) <= new Date() &&
      (!policy.reviewStatus || policy.reviewStatus === 'None');

    return hasActiveReview || isDueForReview;
  });

  // Transform policies to match ReviewPrograms interface
  const transformedData: ReviewPrograms[] = reviewPolicies.map(
    (policy, index) => ({
      id: index + 1,
      policyId: policy.policyId,
      documentCode: policy.documentCode || '',
      name: policy.name,
      documentName: policy.documentName || policy.name,
      documentType: policy.documentType || 'Policy',
      version: policy.version || '1.0',
      policyType: policy.policyType || '',
      categories: policy.categories?.join(', ') || '',
      department: policy.department,
      subDepartment: policy.subDepartment || '',
      policyOwner: policy.policyOwner?.name || '',
      priorityScore: policy.priorityScore || 5,
      status: policy.status,
      detailedStatus: `${policy.status}${policy.reviewStatus && policy.reviewStatus !== 'None' ? ` (Review ${policy.reviewStatus})` : ''}`,
      reviewStatus: policy.reviewStatus || 'Due',
      reviewType: policy.reviewRequest?.reviewType || 'Scheduled Review',
      classification: policy.classification || 'Internal',
      publishedDate: policy.publishedDate || '',
      lastReviewDate: policy.lastReviewDate || '',
      nextReviewDate: policy.nextReviewDate || '',
      reviewInitiatedDate: policy.reviewInitiatedDate || '',
      ownerReviewDecision: policy.ownerReview?.decision || '',
      ownerReviewDate: policy.ownerReview?.reviewDate || '',
      reviewCompletedDate: policy.reviewCompletedDate || '',
    }),
  );

  // Handle policy row click to open details modal
  const handlePolicyClick = (row: ReviewPrograms) => {
    // Find the original policy by policyId
    const policy = policies.find((p) => p.policyId === row.policyId);
    if (policy) {
      setSelectedPolicy(policy);
      setIsDetailsModalOpen(true);
    }
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle policy update (refresh policy data and update selected policy)
  const handlePolicyUpdate = () => {
    refetchPolicies();
  };

  // Handle initiate review
  const handleInitiateReview = async (
    policyId: string,
    reviewType: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await initiatePolicyReview(policyId, reviewType, comments);
    refetchPolicies();
    return result;
  };

  // Handle owner review
  const handleOwnerReview = async (
    policyId: string,
    decision: 'No Updates Required' | 'Updates Required',
    comments?: string,
    changesDescription?: string,
  ): Promise<boolean> => {
    const result = await ownerReview(
      policyId,
      decision,
      comments,
      changesDescription,
    );
    refetchPolicies();
    return result;
  };

  // Handle review governance review
  const handleReviewGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await reviewGovernanceReview(policyId, decision, comments);
    refetchPolicies();
    return result;
  };

  // Handle approve review
  const handleApproveReview = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveReview(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle reject review
  const handleRejectReview = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectReview(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Review workflow status cards based on reviewStatus
  const reviewStatusCards = [
    {
      title: 'Due for Review',
      number: reviewPolicies
        .filter(
          (p) =>
            !p.reviewStatus ||
            p.reviewStatus === 'None' ||
            p.reviewStatus === 'Due',
        )
        .length.toString(),
      icon: Clock,
      borderColor: 'border-l-orange-500',
      numberColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Policies that need review initiation',
    },
    {
      title: 'In Progress',
      number: reviewPolicies
        .filter((p) => p.reviewStatus === 'In Progress')
        .length.toString(),
      icon: FileSearch,
      borderColor: 'border-l-blue-500',
      numberColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Reviews currently being conducted by policy owners',
    },
    {
      title: 'Under Review',
      number: reviewPolicies
        .filter((p) => p.reviewStatus === 'Under Review')
        .length.toString(),
      icon: AlertTriangle,
      borderColor: 'border-l-yellow-500',
      numberColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: 'Reviews awaiting governance approval',
    },
    {
      title: 'Pending Approval',
      number: reviewPolicies
        .filter((p) => p.reviewStatus === 'Pending Approval')
        .length.toString(),
      icon: CheckCircle,
      borderColor: 'border-l-green-500',
      numberColor: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Reviews awaiting final approval',
    },
  ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading policy review data..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="w-full bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Policies"
          message="We couldn't load the policy review data. Please try again or contact support if the problem persists."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="w-full bg-gray-100 py-2">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">
              Policy Review Management
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage policy review workflows and track review progress
            </p>
          </div>
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-indigo-500 bg-gradient-to-r from-indigo-50 to-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-indigo-700">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-6 w-6" />
                  <span>Policy Review Overview</span>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-3xl font-bold text-indigo-700">
                    {policiesLoading ? '...' : reviewPolicies.length}
                  </div>
                  <div className="text-sm text-gray-500">
                    policies requiring attention
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Policies currently in the review workflow or due for review
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Review Status Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Review Workflow Overview
          </h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {reviewStatusCards.map((card, index) => (
              <Card
                key={index}
                className={`${card.borderColor} cursor-pointer border-l-4 transition-shadow hover:shadow-md ${card.bgColor}`}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                    <div className="flex items-center space-x-2">
                      <card.icon className="h-4 w-4" />
                      <span className="truncate">{card.title}</span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-col space-y-2">
                    <p className={`text-2xl font-bold ${card.numberColor}`}>
                      {policiesLoading ? '...' : card.number}
                    </p>
                    <p className="text-xs leading-tight text-gray-500">
                      {card.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedData}
            loading={policiesLoading}
            onRowClick={handlePolicyClick}
          />
        </div>
      </div>

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={() => Promise.resolve(false)} // Not used in review workflow
        onGovernanceReview={() => Promise.resolve(false)} // Not used in review workflow
        onGrcReview={() => Promise.resolve(false)} // Not used in review workflow
        onRequestException={() => Promise.resolve(false)} // Not used in review workflow
        onExceptionGovernanceReview={() => Promise.resolve(false)} // Not used in review workflow
        onApproveException={() => Promise.resolve(false)} // Not used in review workflow
        onRejectException={() => Promise.resolve(false)} // Not used in review workflow
        onPolicyUpdate={handlePolicyUpdate}
        userRole={user?.role || 'Viewer'}
        loading={false}
        // Review workflow handlers
        onInitiateReview={handleInitiateReview}
        onOwnerReview={handleOwnerReview}
        onReviewGovernanceReview={handleReviewGovernanceReview}
        onApproveReview={handleApproveReview}
        onRejectReview={handleRejectReview}
      />
    </>
  );
};

export default Review;
