<VirtualHost *:443>
    ServerName onlyoffice-temp.autoresilience.com
    ServerAlias autoresilience.com

    SSLEngine on
    SSLCertificateFile /etc/apache2/certificates/certificate.crt
    SSLCertificateKeyFile /etc/apache2/certificates/private.key
    SSLCACertificateFile /etc/apache2/certificates/ca_bundle.crt

    ProxyPreserveHost On
    ProxyRequests Off
    AllowEncodedSlashes NoDecode

    <Proxy *>
        Require all granted
    </Proxy>

    # --- WebSocket Support for OnlyOffice ---
    ProxyPass "/" "http://127.0.0.1:8080/"
    ProxyPassReverse "/" "http://127.0.0.1:8080/"

    ProxyPass "/" "ws://127.0.0.1:8080/"
    ProxyPassReverse "/" "ws://127.0.0.1:8080/"

    RequestHeader set Upgrade "websocket"
    RequestHeader set Connection "upgrade"

    # Suppress missing .map files to avoid noise in logs
    RedirectMatch 404 "\.map$"

    # Redirect HTTP traffic to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} !=on
    RewriteRule ^/?(.*) https://%{SERVER_NAME}/$1 [R=301,L]

    # Security header
    Header always set Content-Security-Policy "upgrade-insecure-requests;"
</VirtualHost>

