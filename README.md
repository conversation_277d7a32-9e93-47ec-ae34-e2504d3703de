# GRC Web Application

A comprehensive Governance, Risk, and Compliance (GRC) web application built with Next.js frontend and Node.js backend.

## 🚀 Features

### Policy Management
- **Policy Request Initiation**: Policy owners can request new policy creation
- **Status Tracking**: Real-time status updates from "Request Initiated" to "Active"
- **Approval Workflow**: Multi-step approval process with comments
- **Role-Based Access**: Different permissions for different user roles
- **Dashboard Analytics**: Visual statistics and policy metrics

### User Management
- **Authentication**: JWT-based secure authentication
- **Role-Based Access Control**: Super Admin, Admin, Approver, Creator, Reviewer, Publisher, Viewer roles
- **Department Management**: Organize users by departments

### Business Logic
- **Policy Lifecycle**: Complete workflow from initiation to approval
- **Status Transitions**: Controlled status changes with validation
- **Audit Trail**: Complete history of policy changes and approvals
- **Real-time Updates**: Live data synchronization between frontend and backend

## 🏗️ Architecture

```
grc-web-app/
├── frontend/          # Next.js React application
│   ├── src/
│   │   ├── app/       # Next.js app router pages
│   │   ├── components/ # Reusable UI components
│   │   ├── hooks/     # Custom React hooks
│   │   ├── lib/       # Utilities and API client
│   │   └── contexts/  # React contexts
│   └── package.json
├── backend/           # Node.js Express API
│   ├── src/
│   │   ├── controllers/ # Route handlers
│   │   ├── models/     # MongoDB schemas
│   │   ├── routes/     # API routes
│   │   ├── middleware/ # Custom middleware
│   │   ├── config/     # Configuration files
│   │   └── scripts/    # Utility scripts
│   └── package.json
└── README.md
```

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Radix UI** - Accessible UI components
- **React Hook Form** - Form management
- **TanStack Table** - Data tables
- **Lucide React** - Icons

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM
- **JWT** - Authentication
- **Joi** - Data validation
- **bcryptjs** - Password hashing

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd grc-web-app
```

2. **Set up the backend**
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration
npm run seed  # Seed demo data
npm run dev   # Start development server
```

3. **Set up the frontend**
```bash
cd frontend
npm install
cp .env.local.example .env.local
# Edit .env.local with your configuration
npm run dev   # Start development server
```

4. **Access the application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Health check: http://localhost:5000/health

### Demo Credentials

After running the seed script, you can use these demo accounts:

- **Super Admin**: <EMAIL> / password123
- **Creator**: <EMAIL> / password123
- **Approver**: <EMAIL> / password123

## 📋 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Policy Endpoints
- `GET /api/policies` - Get all policies (with filtering)
- `POST /api/policies/request` - Request policy initiation
- `POST /api/policies/:id/submit-for-review` - Submit policy for review
- `POST /api/policies/:id/governance-review` - Governance review
- `POST /api/policies/:id/grc-review` - GRC review
- `PUT /api/policies/:id/status` - Update policy status
- `POST /api/policies/:id/approve` - Approve policy
- `POST /api/policies/:id/reject` - Reject policy
- `GET /api/policies/stats` - Get policy statistics

## 🔄 Policy Workflow

1. **Request Initiated** - Creator submits request (Creator, Admin, or Super Admin only)
2. **Draft** - Policy content is being written
3. **Under Review** - Policy submitted for approval
4. **Approved/Rejected** - Approver makes decision
5. **Active** - Policy is live and in effect
6. **Archived** - Policy is no longer active

## 👥 User Roles

- **Super Admin**: Full system access
- **Admin**: Manage policies and users
- **Approver**: Approve/reject policies and policy requests (Governance department)
- **Creator**: Create and edit policies (can request new policies)
- **Reviewer**: Review policy documents for accuracy and compliance (GRC department)
- **Publisher**: Publish approved policies to repository
- **Viewer**: Read-only access

## 🔧 Development

### Backend Scripts
```bash
npm run dev      # Start development server
npm run seed     # Seed demo data
npm test         # Run tests
npm run lint     # Run linting
```

### Frontend Scripts
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run linting
```

## 🚀 Deployment

### Backend Deployment
1. Set environment variables
2. Install dependencies: `npm install`
3. Start server: `npm start`

### Frontend Deployment
1. Set environment variables
2. Build application: `npm run build`
3. Start server: `npm start`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔮 Future Enhancements

- [ ] Email notifications for policy updates
- [ ] Advanced reporting and analytics
- [ ] Document version control
- [ ] Integration with external systems
- [ ] Mobile application
- [ ] Advanced search and filtering
- [ ] Bulk operations
- [ ] Policy templates
