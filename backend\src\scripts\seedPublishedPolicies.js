const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Policy = require('../models/Policy');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grc-web-app');
    console.log('📦 MongoDB Connected for seeding published policies');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
};

const seedPublishedPolicies = async () => {
  try {
    console.log('🌱 Starting published policies seeding...');

    // Find existing users or create default ones
    let users = await User.find({});
    if (users.length === 0) {
      console.log('👥 No users found, creating default users...');
      const defaultUsers = [
        {
          name: '<PERSON>',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Creator',
          department: 'HR',
          permissions: ['create_policy', 'edit_policy', 'view_all_policies'],
        },
        {
          name: '<PERSON>',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Creator',
          department: 'IT',
          permissions: ['create_policy', 'edit_policy', 'view_all_policies'],
        },
        {
          name: 'Michael Brown',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Creator',
          department: 'Finance',
          permissions: ['create_policy', 'edit_policy', 'view_all_policies'],
        },
        {
          name: 'Emily Davis',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Creator',
          department: 'Legal',
          permissions: ['create_policy', 'edit_policy', 'view_all_policies'],
        },
        {
          name: 'David Wilson',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Creator',
          department: 'Operations',
          permissions: ['create_policy', 'edit_policy', 'view_all_policies'],
        },
      ];

      for (const userData of defaultUsers) {
        const user = new User(userData);
        await user.save();
        users.push(user);
      }
      console.log(`👥 Created ${users.length} default users`);
    }

    // Clear existing published policies
    await Policy.deleteMany({ status: 'Published' });
    console.log('🗑️  Cleared existing published policies');

    // Helper function to get random date in the past
    const getRandomPastDate = (daysAgo) => {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * daysAgo));
      return date;
    };

    // Helper function to get future date
    const getFutureDate = (daysFromNow) => {
      const date = new Date();
      date.setDate(date.getDate() + daysFromNow);
      return date;
    };

    // Create 10 comprehensive published policies
    const publishedPolicies = [
      {
        policyId: 'HR_POL_001',
        name: 'Employee Code of Conduct',
        description: 'Comprehensive guidelines for professional behavior, ethics, and workplace conduct for all employees.',
        documentName: 'Employee Code of Conduct Policy',
        documentCode: 'ECC-2024-001',
        documentType: 'Policy',
        version: '2.1',
        versionNumber: 2.1,
        department: 'HR',
        subDepartment: 'Employee Relations',
        policyCategory: 'Corporate Policies',
        policyType: 'Corporate',
        categories: ['HR', 'Corporate'],
        classification: 'Internal',
        approvalAuthority: 'Board Committee',
        priorityScore: 9,
        status: 'Published',
        detailedStatus: 'Active and enforced across all departments',
        policyOwner: {
          id: users[0]._id,
          name: users[0].name,
          email: users[0].email,
        },
        initiatedBy: {
          id: users[0]._id,
          name: users[0].name,
          email: users[0].email,
        },
        authorizedApprover: {
          id: users[0]._id,
          name: 'Board of Directors',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(180),
        approvalDate: getRandomPastDate(90),
        publishedDate: getRandomPastDate(60),
        effectiveDate: getRandomPastDate(60),
        lastReviewDate: getRandomPastDate(30),
        nextReviewDate: getFutureDate(365),
        publicationSettings: {
          targetAudience: 'All Employees',
          isPublished: true,
          publishedBy: {
            id: users[0]._id,
            name: users[0].name,
            email: users[0].email,
          },
        },
        metadata: {
          priority: 'Critical',
          tags: ['conduct', 'ethics', 'behavior', 'compliance'],
        },
      },
      {
        policyId: 'IT_SEC_002',
        name: 'Information Security and Data Protection Policy',
        description: 'Comprehensive policy covering cybersecurity measures, data protection protocols, and information handling procedures.',
        documentName: 'Information Security Policy',
        documentCode: 'ISP-2024-002',
        documentType: 'Policy',
        version: '3.0',
        versionNumber: 3.0,
        department: 'IT',
        subDepartment: 'Cybersecurity',
        policyCategory: 'Corporate Policies',
        policyType: 'Technical',
        categories: ['IT', 'Security'],
        classification: 'Confidential',
        approvalAuthority: 'CEO',
        priorityScore: 10,
        status: 'Published',
        detailedStatus: 'Critical security policy - mandatory compliance',
        policyOwner: {
          id: users[1]._id,
          name: users[1].name,
          email: users[1].email,
        },
        initiatedBy: {
          id: users[1]._id,
          name: users[1].name,
          email: users[1].email,
        },
        authorizedApprover: {
          id: users[1]._id,
          name: 'Chief Executive Officer',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(200),
        approvalDate: getRandomPastDate(120),
        publishedDate: getRandomPastDate(90),
        effectiveDate: getRandomPastDate(90),
        lastReviewDate: getRandomPastDate(45),
        nextReviewDate: getFutureDate(180),
        publicationSettings: {
          targetAudience: 'All Employees',
          isPublished: true,
          publishedBy: {
            id: users[1]._id,
            name: users[1].name,
            email: users[1].email,
          },
        },
        metadata: {
          priority: 'Critical',
          tags: ['security', 'data protection', 'cybersecurity', 'privacy'],
        },
      },
      {
        policyId: 'FIN_POL_003',
        name: 'Financial Management and Expense Policy',
        description: 'Guidelines for financial planning, budgeting, expense management, and financial reporting procedures.',
        documentName: 'Financial Management Policy',
        documentCode: 'FMP-2024-003',
        documentType: 'Policy',
        version: '1.5',
        versionNumber: 1.5,
        department: 'Finance',
        subDepartment: 'Financial Planning',
        policyCategory: 'Operational Policies',
        policyType: 'Administrative',
        categories: ['Finance', 'Corporate'],
        classification: 'Internal',
        approvalAuthority: 'CEO',
        priorityScore: 8,
        status: 'Published',
        detailedStatus: 'Active policy governing all financial operations',
        policyOwner: {
          id: users[2]._id,
          name: users[2].name,
          email: users[2].email,
        },
        initiatedBy: {
          id: users[2]._id,
          name: users[2].name,
          email: users[2].email,
        },
        authorizedApprover: {
          id: users[2]._id,
          name: 'Chief Financial Officer',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(150),
        approvalDate: getRandomPastDate(75),
        publishedDate: getRandomPastDate(45),
        effectiveDate: getRandomPastDate(45),
        lastReviewDate: getRandomPastDate(20),
        nextReviewDate: getFutureDate(270),
        publicationSettings: {
          targetAudience: 'Selected Groups',
          selectedGroups: ['Finance', 'Management', 'Department Heads'],
          isPublished: true,
          publishedBy: {
            id: users[2]._id,
            name: users[2].name,
            email: users[2].email,
          },
        },
        metadata: {
          priority: 'High',
          tags: ['finance', 'expenses', 'budgeting', 'reporting'],
        },
      },
      {
        policyId: 'LEG_COM_004',
        name: 'Legal Compliance and Regulatory Policy',
        description: 'Comprehensive framework for ensuring compliance with all applicable laws, regulations, and industry standards.',
        documentName: 'Legal Compliance Policy',
        documentCode: 'LCP-2024-004',
        documentType: 'Framework',
        version: '2.0',
        versionNumber: 2.0,
        department: 'Legal',
        subDepartment: 'Compliance',
        policyCategory: 'Corporate Policies',
        policyType: 'Strategic',
        categories: ['Compliance', 'Corporate'],
        classification: 'Confidential',
        approvalAuthority: 'Board Committee',
        priorityScore: 9,
        status: 'Published',
        detailedStatus: 'Mandatory compliance framework for all operations',
        policyOwner: {
          id: users[3]._id,
          name: users[3].name,
          email: users[3].email,
        },
        initiatedBy: {
          id: users[3]._id,
          name: users[3].name,
          email: users[3].email,
        },
        authorizedApprover: {
          id: users[3]._id,
          name: 'Chief Legal Officer',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(220),
        approvalDate: getRandomPastDate(100),
        publishedDate: getRandomPastDate(70),
        effectiveDate: getRandomPastDate(70),
        lastReviewDate: getRandomPastDate(35),
        nextReviewDate: getFutureDate(180),
        publicationSettings: {
          targetAudience: 'All Employees',
          isPublished: true,
          publishedBy: {
            id: users[3]._id,
            name: users[3].name,
            email: users[3].email,
          },
        },
        metadata: {
          priority: 'Critical',
          tags: ['compliance', 'legal', 'regulations', 'standards'],
        },
      },
      {
        policyId: 'OPS_PRO_005',
        name: 'Operational Procedures and Quality Standards',
        description: 'Standard operating procedures and quality management guidelines for all operational activities.',
        documentName: 'Operational Procedures Manual',
        documentCode: 'OPM-2024-005',
        documentType: 'Procedure',
        version: '1.8',
        versionNumber: 1.8,
        department: 'Operations',
        subDepartment: 'Quality Assurance',
        policyCategory: 'Operational Policies',
        policyType: 'Operational',
        categories: ['Organizational', 'Corporate'],
        classification: 'Internal',
        approvalAuthority: 'Department Head',
        priorityScore: 7,
        status: 'Published',
        detailedStatus: 'Active operational guidelines for all departments',
        policyOwner: {
          id: users[4]._id,
          name: users[4].name,
          email: users[4].email,
        },
        initiatedBy: {
          id: users[4]._id,
          name: users[4].name,
          email: users[4].email,
        },
        authorizedApprover: {
          id: users[4]._id,
          name: 'Operations Director',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(130),
        approvalDate: getRandomPastDate(65),
        publishedDate: getRandomPastDate(40),
        effectiveDate: getRandomPastDate(40),
        lastReviewDate: getRandomPastDate(15),
        nextReviewDate: getFutureDate(300),
        publicationSettings: {
          targetAudience: 'Selected Groups',
          selectedGroups: ['Operations', 'Quality', 'Production'],
          isPublished: true,
          publishedBy: {
            id: users[4]._id,
            name: users[4].name,
            email: users[4].email,
          },
        },
        metadata: {
          priority: 'High',
          tags: ['operations', 'procedures', 'quality', 'standards'],
        },
      },
      {
        policyId: 'HR_WFH_006',
        name: 'Remote Work and Flexible Arrangements Policy',
        description: 'Guidelines for remote work, flexible schedules, and hybrid work arrangements including technology and productivity requirements.',
        documentName: 'Remote Work Policy',
        documentCode: 'RWP-2024-006',
        documentType: 'Policy',
        version: '1.3',
        versionNumber: 1.3,
        department: 'HR',
        subDepartment: 'Workforce Management',
        policyCategory: 'Operational Policies',
        policyType: 'Administrative',
        categories: ['HR', 'Organizational'],
        classification: 'Internal',
        approvalAuthority: 'CEO',
        priorityScore: 6,
        status: 'Published',
        detailedStatus: 'Active policy supporting flexible work arrangements',
        policyOwner: {
          id: users[0]._id,
          name: users[0].name,
          email: users[0].email,
        },
        initiatedBy: {
          id: users[0]._id,
          name: users[0].name,
          email: users[0].email,
        },
        authorizedApprover: {
          id: users[0]._id,
          name: 'HR Director',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(90),
        approvalDate: getRandomPastDate(50),
        publishedDate: getRandomPastDate(30),
        effectiveDate: getRandomPastDate(30),
        lastReviewDate: getRandomPastDate(10),
        nextReviewDate: getFutureDate(240),
        publicationSettings: {
          targetAudience: 'All Employees',
          isPublished: true,
          publishedBy: {
            id: users[0]._id,
            name: users[0].name,
            email: users[0].email,
          },
        },
        metadata: {
          priority: 'Medium',
          tags: ['remote work', 'flexibility', 'hybrid', 'productivity'],
        },
      },
      {
        policyId: 'IT_AST_007',
        name: 'IT Asset Management and Security Policy',
        description: 'Comprehensive guidelines for managing IT assets, software licensing, hardware procurement, and technology security protocols.',
        documentName: 'IT Asset Management Policy',
        documentCode: 'ITAM-2024-007',
        documentType: 'Policy',
        version: '2.2',
        versionNumber: 2.2,
        department: 'IT',
        subDepartment: 'Asset Management',
        policyCategory: 'Operational Policies',
        policyType: 'Technical',
        categories: ['IT', 'Security'],
        classification: 'Internal',
        approvalAuthority: 'Department Head',
        priorityScore: 7,
        status: 'Published',
        detailedStatus: 'Active policy governing all IT asset lifecycle management',
        policyOwner: {
          id: users[1]._id,
          name: users[1].name,
          email: users[1].email,
        },
        initiatedBy: {
          id: users[1]._id,
          name: users[1].name,
          email: users[1].email,
        },
        authorizedApprover: {
          id: users[1]._id,
          name: 'IT Director',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(110),
        approvalDate: getRandomPastDate(55),
        publishedDate: getRandomPastDate(35),
        effectiveDate: getRandomPastDate(35),
        lastReviewDate: getRandomPastDate(12),
        nextReviewDate: getFutureDate(330),
        publicationSettings: {
          targetAudience: 'Selected Groups',
          selectedGroups: ['IT', 'Procurement', 'Finance'],
          isPublished: true,
          publishedBy: {
            id: users[1]._id,
            name: users[1].name,
            email: users[1].email,
          },
        },
        metadata: {
          priority: 'High',
          tags: ['IT assets', 'security', 'procurement', 'licensing'],
        },
      },
      {
        policyId: 'SEC_INC_008',
        name: 'Security Incident Response and Management Policy',
        description: 'Detailed procedures for identifying, responding to, and managing security incidents and data breaches.',
        documentName: 'Security Incident Response Policy',
        documentCode: 'SIRP-2024-008',
        documentType: 'Procedure',
        version: '1.6',
        versionNumber: 1.6,
        department: 'Security',
        subDepartment: 'Incident Response',
        policyCategory: 'Corporate Policies',
        policyType: 'Technical',
        categories: ['Security', 'Compliance'],
        classification: 'Restricted',
        approvalAuthority: 'CEO',
        priorityScore: 10,
        status: 'Published',
        detailedStatus: 'Critical security policy - immediate response required',
        policyOwner: {
          id: users[1]._id,
          name: users[1].name,
          email: users[1].email,
        },
        initiatedBy: {
          id: users[1]._id,
          name: users[1].name,
          email: users[1].email,
        },
        authorizedApprover: {
          id: users[1]._id,
          name: 'Chief Security Officer',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(160),
        approvalDate: getRandomPastDate(80),
        publishedDate: getRandomPastDate(50),
        effectiveDate: getRandomPastDate(50),
        lastReviewDate: getRandomPastDate(25),
        nextReviewDate: getFutureDate(120),
        publicationSettings: {
          targetAudience: 'Selected Groups',
          selectedGroups: ['Security', 'IT', 'Management', 'Legal'],
          isPublished: true,
          publishedBy: {
            id: users[1]._id,
            name: users[1].name,
            email: users[1].email,
          },
        },
        metadata: {
          priority: 'Critical',
          tags: ['security', 'incident response', 'breach', 'emergency'],
        },
      },
      {
        policyId: 'FIN_EXP_009',
        name: 'Travel and Expense Reimbursement Policy',
        description: 'Guidelines for business travel approval, expense reporting, reimbursement procedures, and spending limits.',
        documentName: 'Travel and Expense Policy',
        documentCode: 'TEP-2024-009',
        documentType: 'Policy',
        version: '1.4',
        versionNumber: 1.4,
        department: 'Finance',
        subDepartment: 'Expense Management',
        policyCategory: 'Operational Policies',
        policyType: 'Administrative',
        categories: ['Finance', 'HR'],
        classification: 'Internal',
        approvalAuthority: 'Department Head',
        priorityScore: 5,
        status: 'Published',
        detailedStatus: 'Active policy for all business travel and expenses',
        policyOwner: {
          id: users[2]._id,
          name: users[2].name,
          email: users[2].email,
        },
        initiatedBy: {
          id: users[2]._id,
          name: users[2].name,
          email: users[2].email,
        },
        authorizedApprover: {
          id: users[2]._id,
          name: 'Finance Manager',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(80),
        approvalDate: getRandomPastDate(40),
        publishedDate: getRandomPastDate(25),
        effectiveDate: getRandomPastDate(25),
        lastReviewDate: getRandomPastDate(8),
        nextReviewDate: getFutureDate(360),
        publicationSettings: {
          targetAudience: 'All Employees',
          isPublished: true,
          publishedBy: {
            id: users[2]._id,
            name: users[2].name,
            email: users[2].email,
          },
        },
        metadata: {
          priority: 'Medium',
          tags: ['travel', 'expenses', 'reimbursement', 'approval'],
        },
      },
      {
        policyId: 'HR_TRN_010',
        name: 'Employee Training and Development Policy',
        description: 'Comprehensive framework for employee skill development, training programs, certification requirements, and career advancement.',
        documentName: 'Training and Development Policy',
        documentCode: 'TDP-2024-010',
        documentType: 'Framework',
        version: '1.7',
        versionNumber: 1.7,
        department: 'HR',
        subDepartment: 'Learning & Development',
        policyCategory: 'Operational Policies',
        policyType: 'Strategic',
        categories: ['HR', 'Organizational'],
        classification: 'Internal',
        approvalAuthority: 'CEO',
        priorityScore: 6,
        status: 'Published',
        detailedStatus: 'Active policy supporting employee growth and development',
        policyOwner: {
          id: users[0]._id,
          name: users[0].name,
          email: users[0].email,
        },
        initiatedBy: {
          id: users[0]._id,
          name: users[0].name,
          email: users[0].email,
        },
        authorizedApprover: {
          id: users[0]._id,
          name: 'Learning & Development Director',
          email: '<EMAIL>',
        },
        startDate: getRandomPastDate(120),
        approvalDate: getRandomPastDate(60),
        publishedDate: getRandomPastDate(40),
        effectiveDate: getRandomPastDate(40),
        lastReviewDate: getRandomPastDate(18),
        nextReviewDate: getFutureDate(270),
        publicationSettings: {
          targetAudience: 'All Employees',
          isPublished: true,
          publishedBy: {
            id: users[0]._id,
            name: users[0].name,
            email: users[0].email,
          },
        },
        metadata: {
          priority: 'Medium',
          tags: ['training', 'development', 'skills', 'career'],
        },
      },
    ];

    // Insert all published policies
    const createdPolicies = await Policy.insertMany(publishedPolicies);
    console.log(`📋 Created ${createdPolicies.length} published policies`);

    return createdPolicies;
  } catch (error) {
    console.error('❌ Error seeding published policies:', error);
    throw error;
  }
};

module.exports = { seedPublishedPolicies };

// Run the seeding if this file is executed directly
if (require.main === module) {
  const runSeeding = async () => {
    try {
      await connectDB();
      await seedPublishedPolicies();
      console.log('✅ Published policies seeding completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('❌ Published policies seeding failed:', error);
      process.exit(1);
    }
  };
  
  runSeeding();
}
