'use client';
import React from 'react';
import Link from 'next/link';
import { icons } from '@/assests/images/homescreen/icons';

interface CardProps {
  title: string;
  description: string;
  tag: string;
  icon: string;
  buttonLabel: string;
  tagColor: string;
  onClick: () => void;
  isOutline: boolean;
  href: string;
}
const Card: React.FC<CardProps> = ({
  title,
  description,
  tag,
  icon,
  buttonLabel,
  href,
  tagColor,
  isOutline,
}) => {
  return (
    <div className="relative flex flex-col items-center rounded-md border bg-white p-10 text-center shadow-sm">
      <span
        className={`absolute right-2 top-2 mb-6 rounded-full px-4 py-1 text-xs font-bold ${tagColor}`}
      >
        {tag}
      </span>

      <div className="mb-2 mt-4">{icons[icon]}</div>

      <h3 className="mb-2 text-lg font-semibold text-customBlue">{title}</h3>

      <p className="mb-4 text-sm text-gray-500">{description}</p>

      <Link
        href={href || '#'}
        className={`mt-auto rounded-full px-8 py-1 ${
          isOutline
            ? 'border border-customBlue text-customBlue hover:bg-blue-100'
            : 'bg-customBlue text-white hover:bg-customBlueHover'
        }`}
      >
        {buttonLabel}
      </Link>
    </div>
  );
};

export default Card;
