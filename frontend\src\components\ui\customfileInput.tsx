import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function InputFile() {
  const [fileName, setFileName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setFileName(file ? file.name : null);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <div className="relative">
        <Input
          ref={fileInputRef}
          id="picture"
          type="file"
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          onChange={handleFileChange}
        />
        <div className="flex items-center rounded-md border focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
          <Input
            readOnly
            placeholder="Choose file"
            value={fileName || ''}
            className="border-0 placeholder-customBlueSecondary focus-visible:ring-0 focus-visible:ring-offset-0"
          />
          <Button
            type="button"
            variant="ghost"
            className="min-w-[4rem] bg-[#E5E9F2] px-6 font-normal text-customBlueSecondary transition-colors hover:bg-gray-200"
            onClick={handleBrowseClick}
          >
            Browse
          </Button>
        </div>
      </div>
      {fileName && (
        <p className="mt-1 text-sm text-muted-foreground">
          Selected: {fileName}
        </p>
      )}
    </div>
  );
}
