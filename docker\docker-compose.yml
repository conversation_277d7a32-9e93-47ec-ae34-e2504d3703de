version: '3.8'

services:
  onlyoffice-documentserver:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-documentserver
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      # JWT Configuration
      - JWT_ENABLED=true
      - JWT_SECRET=${JWT_SECRET}
      - JWT_HEADER=Authorization

      # Storage and security settings
      - USE_UNAUTHORIZED_STORAGE=true
      - WOPI_ENABLED=false

      # File size and timeout limits
      - DOC_SERV_MAX_FILE_SIZE=50000000
      - DOC_SERV_TIMEOUT=120000

      # Network configuration for Docker Desktop
      - ALLOW_PRIVATE_IP_ADDRESS=true
      - ALLOW_META_IP_ADDRESS=true
      - ALLOW_HTTPS_INSECURE=true
      - ALLOW_HTTP_REQUEST_TO_HTTPS_RESOURCE=true

      # Additional security settings
      - REJECT_UNAUTHORIZED=false
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      
    volumes:
      - onlyoffice_data:/var/www/onlyoffice/Data
      - onlyoffice_logs:/var/log/onlyoffice
      - ../backend/uploads/policies:/var/www/onlyoffice/documentserver/App_Data/cache/files
    networks:
      - onlyoffice-network

volumes:
  onlyoffice_data:
  onlyoffice_logs:

networks:
  onlyoffice-network:
    driver: bridge
