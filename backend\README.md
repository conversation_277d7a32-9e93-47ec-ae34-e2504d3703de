# GRC Web App Backend

Backend API for the GRC (Governance, Risk, and Compliance) web application.

## Features

- **Policy Management**: Create, update, delete, and manage policies
- **User Authentication**: JWT-based authentication and authorization
- **Role-Based Access Control**: Different permissions for different user roles
- **Policy Workflow**: Request initiation, approval workflow, status tracking
- **RESTful API**: Clean and consistent API design
- **Data Validation**: Input validation using Joi
- **Error Handling**: Comprehensive error handling and logging
- **Security**: Helmet, CORS, rate limiting, and other security measures

## Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication
- **Joi** - Data validation
- **bcryptjs** - Password hashing

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/grc-web-app
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d
FRONTEND_URL=http://localhost:3000
```

4. Start the development server
```bash
npm run dev
```

The server will start on `http://localhost:5000`

### Production

```bash
npm start
```

## API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile

### Policy Endpoints

- `GET /api/policies` - Get all policies (with filtering)
- `GET /api/policies/:id` - Get specific policy
- `POST /api/policies/request` - Request policy initiation (Creator, Admin, Super Admin only)
- `PUT /api/policies/:id` - Update policy
- `DELETE /api/policies/:id` - Delete policy
- `PUT /api/policies/:id/status` - Update policy status
- `POST /api/policies/:id/approve` - Approve policy
- `POST /api/policies/:id/reject` - Reject policy
- `GET /api/policies/:id/history` - Get policy history
- `GET /api/policies/stats` - Get policy statistics

### User Management Endpoints (Admin only)

- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get specific user
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Deactivate user

## User Roles

- **Super Admin**: Full access to all features
- **Admin**: Manage policies and users (except super admin functions)
- **Approver**: Approve/reject policies and policy requests, view reports (Governance department)
- **Creator**: Create and edit own policies (can request new policies)
- **Reviewer**: Review policy documents for accuracy and compliance (GRC department)
- **Publisher**: Publish approved policies to repository
- **Viewer**: Read-only access to reports

## Policy Lifecycle Status Flow

1. **Not Started** - Initial status when policy is requested but work hasn't begun
2. **Draft** - Policy is being written/edited by the policy owner
3. **Pending Approval** - Policy submitted for approval by designated approvers
4. **Approved** - Policy approved and ready for implementation
5. **Under Annual Review** - Policy is undergoing scheduled annual review process

### Status Transitions
- **Not Started** → **Draft**
- **Draft** → **Pending Approval**
- **Pending Approval** → **Approved** or **Draft** (if rejected)
- **Approved** → **Under Annual Review**
- **Under Annual Review** → **Approved** or **Draft** (if changes needed)

## Error Handling

The API uses consistent error response format:

```json
{
  "success": false,
  "error": "Error message",
  "stack": "Error stack (development only)"
}
```

## Security Features

- JWT token authentication
- Password hashing with bcrypt
- Input validation and sanitization
- Rate limiting
- CORS configuration
- Security headers with Helmet
- Role-based access control

## Development

### Scripts

- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

### Project Structure

```
src/
├── config/
│   └── database.js
├── controllers/
│   ├── authController.js
│   ├── policyController.js
│   └── userController.js
├── middleware/
│   ├── auth.js
│   ├── errorHandler.js
│   └── validation.js
├── models/
│   ├── Policy.js
│   └── User.js
├── routes/
│   ├── auth.js
│   ├── policies.js
│   └── users.js
└── server.js
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run linting and tests
6. Submit a pull request

## License

This project is licensed under the MIT License.
