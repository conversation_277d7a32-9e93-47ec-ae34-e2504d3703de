version: '3.8'

services:
  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=development
    container_name: grc-frontend-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:5000
      - NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
      - NEXT_PUBLIC_ONLYOFFICE_URL=http://localhost:8080
    depends_on:
      - backend
    networks:
      - grc-dev-network
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: ["yarn", "dev"]

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: grc-backend-dev
    restart: unless-stopped
    ports:
      - "5000:5000"
    env_file:
      - ./backend/.env
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - FRONTEND_URL=http://localhost:3000
      - ONLYOFFICE_JWT_SECRET=${ONLYOFFICE_JWT_SECRET}
      - BASE_URL_ONLYOFFICE=http://grc-backend-dev:5000
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
    networks:
      - grc-dev-network
    command: ["npm", "run", "dev"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OnlyOffice Document Server
  onlyoffice:
    image: onlyoffice/documentserver:latest
    container_name: grc-onlyoffice-dev
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      # JWT Configuration - disabled for development
      - JWT_ENABLED=false
      - JWT_SECRET=
      - JWT_HEADER=Authorization

      # Storage and security settings
      - USE_UNAUTHORIZED_STORAGE=true
      - WOPI_ENABLED=false

      # File size and timeout limits
      - DOC_SERV_MAX_FILE_SIZE=50000000
      - DOC_SERV_TIMEOUT=120000

      # Network configuration for Docker Desktop
      - ALLOW_PRIVATE_IP_ADDRESS=true
      - ALLOW_META_IP_ADDRESS=true
      - ALLOW_HTTPS_INSECURE=true
      - ALLOW_HTTP_REQUEST_TO_HTTPS_RESOURCE=true

      # Additional security settings for development
      - REJECT_UNAUTHORIZED=false
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      
    volumes:
      - onlyoffice_data_dev:/var/www/onlyoffice/Data
      - onlyoffice_logs_dev:/var/log/onlyoffice
      - ./backend/uploads/policies:/var/www/onlyoffice/documentserver/App_Data/cache/files
    networks:
      - grc-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  onlyoffice_data_dev:
    driver: local
  onlyoffice_logs_dev:
    driver: local

networks:
  grc-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16