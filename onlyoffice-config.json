{"services": {"CoAuthoring": {"server": {"port": 8000, "workerpercpu": 1, "limits_tempfile_upload": 104857600, "limits_image_size": 26214400, "limits_image_download_timeout": 120000, "callbackRequestTimeout": 30000, "callbackRetryDelay": 1000, "callbackRetryAttempts": 3, "editor": {"reconnection": {"attempts": 50, "delay": 2000}}}, "requestDefaults": {"timeout": 30000, "headers": {"User-Agent": "Node.js/OnlyOffice"}}}}, "storage": {"fs": {"secretString": "onlyoffice-secret"}}, "log": {"level": "WARN", "type": 4, "pattern": "[%d] [%p] %c - %m"}}