@echo off
REM GRC MERN Application Deployment Script for Windows
REM Supports both development and UAT environments

setlocal enabledelayedexpansion

REM Default values
set ENVIRONMENT=dev
set FORCE_REBUILD=false
set SKIP_TESTS=false
set VERBOSE=false

REM Function to print colored output (simplified for Windows)
:print_info
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Function to show usage
:show_usage
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -e, --environment ENV    Set environment (dev^|uat) [default: dev]
echo   -f, --force-rebuild      Force rebuild of Docker images
echo   -s, --skip-tests         Skip running tests
echo   -v, --verbose            Enable verbose output
echo   -h, --help               Show this help message
echo.
echo Examples:
echo   %0                       Deploy to development environment
echo   %0 -e uat                Deploy to UAT environment
echo   %0 -e uat -f             Deploy to UAT with force rebuild
goto :eof

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :main
if "%~1"=="-e" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--environment" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-f" (
    set FORCE_REBUILD=true
    shift
    goto :parse_args
)
if "%~1"=="--force-rebuild" (
    set FORCE_REBUILD=true
    shift
    goto :parse_args
)
if "%~1"=="-s" (
    set SKIP_TESTS=true
    shift
    goto :parse_args
)
if "%~1"=="--skip-tests" (
    set SKIP_TESTS=true
    shift
    goto :parse_args
)
if "%~1"=="-v" (
    set VERBOSE=true
    shift
    goto :parse_args
)
if "%~1"=="--verbose" (
    set VERBOSE=true
    shift
    goto :parse_args
)
if "%~1"=="-h" (
    call :show_usage
    exit /b 0
)
if "%~1"=="--help" (
    call :show_usage
    exit /b 0
)
call :print_error "Unknown option: %~1"
call :show_usage
exit /b 1

REM Function to validate environment
:validate_environment
if not "%ENVIRONMENT%"=="dev" if not "%ENVIRONMENT%"=="uat" (
    call :print_error "Invalid environment: %ENVIRONMENT%. Must be 'dev' or 'uat'"
    exit /b 1
)
goto :eof

REM Function to check prerequisites
:check_prerequisites
call :print_info "Checking prerequisites..."

REM Check if Docker is installed and running
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not installed or not in PATH"
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker daemon is not running"
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        call :print_error "Docker Compose is not installed"
        exit /b 1
    )
    set DOCKER_COMPOSE_CMD=docker compose
) else (
    set DOCKER_COMPOSE_CMD=docker-compose
)

call :print_success "Prerequisites check passed"
goto :eof

REM Function to setup environment
:setup_environment
call :print_info "Setting up environment for: %ENVIRONMENT%"

if "%ENVIRONMENT%"=="dev" (
    set COMPOSE_FILE=docker-compose.dev.yml
    set ENV_FILE=.env
    set BACKEND_ENV_FILE=backend\.env
    set FRONTEND_ENV_FILE=frontend\.env.local
) else if "%ENVIRONMENT%"=="uat" (
    set COMPOSE_FILE=docker-compose.uat.yml
    set ENV_FILE=.env.uat
    set BACKEND_ENV_FILE=backend\.env.uat
    set FRONTEND_ENV_FILE=frontend\.env.uat
)

REM Check if compose file exists
if not exist "%COMPOSE_FILE%" (
    call :print_error "Docker Compose file not found: %COMPOSE_FILE%"
    exit /b 1
)

REM Check if environment files exist
if not exist "%ENV_FILE%" (
    call :print_error "Environment file not found: %ENV_FILE%"
    exit /b 1
)

call :print_success "Environment setup completed"
goto :eof

REM Function to deploy application
:deploy_application
call :print_info "Deploying application to %ENVIRONMENT% environment..."

REM Build arguments
set BUILD_ARGS=
if "%FORCE_REBUILD%"=="true" (
    set BUILD_ARGS=--build --force-recreate
    call :print_info "Force rebuilding images..."
)

if "%VERBOSE%"=="true" (
    set BUILD_ARGS=%BUILD_ARGS% --verbose
)

REM Stop existing containers
call :print_info "Stopping existing containers..."
%DOCKER_COMPOSE_CMD% -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" down --remove-orphans

REM Also stop any containers that might be using the same ports
call :print_info "Checking for port conflicts..."
if "%ENVIRONMENT%"=="dev" (
    REM Stop any containers using port 8080
    for /f "tokens=*" %%i in ('docker ps --filter "publish=8080" --format "{{.Names}}" 2^>nul') do (
        if not "%%i"=="" (
            call :print_warning "Found containers using port 8080, stopping them..."
            docker stop %%i
        )
    )
) else if "%ENVIRONMENT%"=="uat" (
    REM Stop any containers using ports 80 or 443
    for /f "tokens=*" %%i in ('docker ps --filter "publish=80" --format "{{.Names}}" 2^>nul') do (
        if not "%%i"=="" (
            call :print_warning "Found containers using port 80, stopping them..."
            docker stop %%i
        )
    )
    for /f "tokens=*" %%i in ('docker ps --filter "publish=443" --format "{{.Names}}" 2^>nul') do (
        if not "%%i"=="" (
            call :print_warning "Found containers using port 443, stopping them..."
            docker stop %%i
        )
    )
)

REM Pull latest images (for production images)
if "%ENVIRONMENT%"=="uat" (
    call :print_info "Pulling latest images..."
    %DOCKER_COMPOSE_CMD% -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" pull --ignore-pull-failures
)

REM Build and start containers
call :print_info "Building and starting containers..."
%DOCKER_COMPOSE_CMD% -f "%COMPOSE_FILE%" --env-file "%ENV_FILE%" up -d %BUILD_ARGS%

REM Wait for services to be ready
call :print_info "Waiting for services to be ready..."
timeout /t 10 /nobreak >nul

call :print_success "Application deployed successfully!"
goto :eof

REM Function to show deployment info
:show_deployment_info
call :print_info "Deployment Information:"
echo ==========================
echo Environment: %ENVIRONMENT%
echo Compose File: %COMPOSE_FILE%
echo Environment File: %ENV_FILE%
echo.

if "%ENVIRONMENT%"=="dev" (
    echo Access URLs:
    echo   OnlyOffice: http://localhost:8080
    echo.
    echo Useful Commands:
    echo   View logs: %DOCKER_COMPOSE_CMD% -f %COMPOSE_FILE% logs -f
    echo   Stop services: %DOCKER_COMPOSE_CMD% -f %COMPOSE_FILE% down
) else if "%ENVIRONMENT%"=="uat" (
    echo Access URLs:
    echo   Application: https://grc-v4.autoresilience.com
    echo   OnlyOffice: https://grc-v4.autoresilience.com/onlyoffice
    echo   API: https://grc-v4.autoresilience.com/api
    echo.
    echo Useful Commands:
    echo   View logs: %DOCKER_COMPOSE_CMD% -f %COMPOSE_FILE% logs -f
    echo   Stop services: %DOCKER_COMPOSE_CMD% -f %COMPOSE_FILE% down
    echo   View NGINX logs: docker logs grc-nginx-uat
)
goto :eof

REM Main execution
:main
call :print_info "Starting GRC MERN Application Deployment"
call :print_info "Environment: %ENVIRONMENT%"

call :validate_environment
if errorlevel 1 exit /b 1

call :check_prerequisites
if errorlevel 1 exit /b 1

call :setup_environment
if errorlevel 1 exit /b 1

call :deploy_application
if errorlevel 1 exit /b 1

call :show_deployment_info

call :print_success "Deployment completed successfully!"
goto :eof

REM Start script execution
call :parse_args %*