import { useState, useEffect, useCallback } from 'react';
import {
  policyApi,
  Policy,
  PolicyStats,
  PaginationInfo,
  PolicyMetadata,
} from '@/lib/api';

interface UsePoliciesOptions {
  page?: number;
  limit?: number;
  status?: string;
  department?: string;
  search?: string;
  management?: boolean;
  myActions?: boolean;
  autoFetch?: boolean;
}

interface UsePoliciesReturn {
  policies: Policy[];
  pagination: PaginationInfo | null;
  metadata: PolicyMetadata | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fetchPolicies: (overrideOptions?: UsePoliciesOptions) => Promise<void>;
  requestPolicyInitiation: (policyData: {
    name: string;
    description?: string;
    documentName?: string;
    documentCode?: string;
    documentType?: string;
    version?: string;
    policyCategory: 'Corporate Policies' | 'Operational Policies';
    policyType?: string;
    categories: string[];
    department: string;
    subDepartment?: string;
    priorityScore?: number;
    classification?: string;
    priority?: string;
    dueDate?: string;
    startDate?: string;
    nextReviewDate?: string;
  }) => Promise<Policy | null>;
  updatePolicyStatus: (
    id: string,
    status: string,
    comments?: string,
    effectiveDate?: string,
    selectedGroups?: string[],
  ) => Promise<boolean>;
  governanceReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  grcReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  submitPolicyForReview: (id: string, comments?: string) => Promise<boolean>;
  approvePolicy: (id: string, comments?: string) => Promise<boolean>;
  rejectPolicy: (id: string, comments?: string) => Promise<boolean>;
  deletePolicy: (id: string) => Promise<boolean>;
  requestRetirement: (
    id: string,
    justification: string,
    effectiveDate?: string,
    comments?: string,
  ) => Promise<boolean>;
  retirementGovernanceReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  approveRetirement: (id: string, comments?: string) => Promise<boolean>;
  rejectRetirement: (id: string, comments?: string) => Promise<boolean>;
  requestException: (
    id: string,
    justification: string,
    specificSection: string,
    exceptionType: 'Material Exception' | 'Immaterial Exception',
    effectiveDate?: string,
    expiryDate?: string,
    comments?: string,
  ) => Promise<boolean>;
  exceptionGovernanceReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  approveException: (id: string, comments?: string) => Promise<boolean>;
  rejectException: (id: string, comments?: string) => Promise<boolean>;
  // Review workflow methods
  initiatePolicyReview: (
    id: string,
    reviewType: string,
    comments?: string,
  ) => Promise<boolean>;
  ownerReview: (
    id: string,
    decision: 'No Updates Required' | 'Updates Required',
    comments?: string,
    changesDescription?: string,
  ) => Promise<boolean>;
  reviewGovernanceReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  approveReview: (id: string, comments?: string) => Promise<boolean>;
  rejectReview: (id: string, comments?: string) => Promise<boolean>;
}

export const usePolicies = (
  options: UsePoliciesOptions = {},
): UsePoliciesReturn => {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [metadata, setMetadata] = useState<PolicyMetadata | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { autoFetch = true, ...fetchOptions } = options;

  const fetchPolicies = useCallback(
    async (overrideOptions?: UsePoliciesOptions) => {
      setLoading(true);
      setError(null);

      try {
        const finalOptions = overrideOptions || fetchOptions;
        const response = await policyApi.getPolicies(finalOptions);

        if (response.success && response.data) {
          setPolicies(response.data.policies);
          setPagination(response.data.pagination);
          setMetadata(response.data.metadata);
        } else {
          setError(response.error || 'Failed to fetch policies');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    },
    [JSON.stringify(fetchOptions)],
  );

  const requestPolicyInitiation = useCallback(
    async (policyData: {
      name: string;
      description?: string;
      documentName?: string;
      documentCode?: string;
      documentType?: string;
      version?: string;
      policyCategory: 'Corporate Policies' | 'Operational Policies';
      policyType?: string;
      categories: string[];
      department: string;
      subDepartment?: string;
      priorityScore?: number;
      classification?: string;
      priority?: string;
      dueDate?: string;
      startDate?: string;
      nextReviewDate?: string;
    }): Promise<Policy | null> => {
      setError(null);

      try {
        const response = await policyApi.requestPolicyInitiation(policyData);

        if (response.success && response.data) {
          // Add the new policy to the beginning of the list
          setPolicies((prev) => [response.data!, ...prev]);
          return response.data;
        } else {
          const errorMessage =
            response.error || 'Failed to request policy initiation';
          setError(errorMessage);

          // Throw error so it can be caught by the calling component
          throw new Error(errorMessage);
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        throw err; // Re-throw so the component can handle it
      }
    },
    [],
  );

  const updatePolicyStatus = useCallback(
    async (
      id: string,
      status: string,
      comments?: string,
      effectiveDate?: string,
      selectedGroups?: string[],
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.updatePolicyStatus(
          id,
          status,
          comments,
          effectiveDate,
          selectedGroups,
        );

        if (response.success && response.data) {
          // Update the policy in the list
          setPolicies((prev) =>
            prev.map((policy) => (policy._id === id ? response.data! : policy)),
          );
          return true;
        } else {
          setError(response.error || 'Failed to update policy status');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [],
  );

  const governanceReview = useCallback(
    async (
      id: string,
      decision: 'Approved' | 'Rejected',
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.governanceReview(
          id,
          decision,
          comments,
        );

        if (response.success) {
          // Refresh policies after review
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to submit governance review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const grcReview = useCallback(
    async (
      id: string,
      decision: 'Approved' | 'Rejected',
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.grcReview(id, decision, comments);

        if (response.success) {
          // Refresh policies after review
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to submit GRC review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const submitPolicyForReview = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.submitPolicyForReview(id, comments);

        if (response.success) {
          // Refresh policies after submission
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to submit policy for review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const approvePolicy = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.approvePolicy(id, comments);

        if (response.success) {
          // Refresh policies after approval
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to approve policy');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const rejectPolicy = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.rejectPolicy(id, comments);

        if (response.success) {
          // Refresh policies after rejection
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to reject policy');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const deletePolicy = useCallback(async (id: string): Promise<boolean> => {
    setError(null);

    try {
      const response = await policyApi.deletePolicy(id);

      if (response.success) {
        // Remove the policy from the list
        setPolicies((prev) => prev.filter((policy) => policy._id !== id));
        return true;
      } else {
        setError(response.error || 'Failed to delete policy');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return false;
    }
  }, []);

  const requestRetirement = useCallback(
    async (
      id: string,
      justification: string,
      effectiveDate?: string,
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.requestRetirement(
          id,
          justification,
          effectiveDate,
          comments,
        );

        if (response.success) {
          // Refresh policies after retirement request
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to request policy retirement');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const retirementGovernanceReview = useCallback(
    async (
      id: string,
      decision: 'Approved' | 'Rejected',
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.retirementGovernanceReview(
          id,
          decision,
          comments,
        );

        if (response.success) {
          // Refresh policies after retirement governance review
          await fetchPolicies();
          return true;
        } else {
          setError(
            response.error || 'Failed to submit retirement governance review',
          );
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const approveRetirement = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.approveRetirement(id, comments);

        if (response.success) {
          // Refresh policies after retirement approval
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to approve policy retirement');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const rejectRetirement = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.rejectRetirement(id, comments);

        if (response.success) {
          // Refresh policies after retirement rejection
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to reject policy retirement');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const requestException = useCallback(
    async (
      id: string,
      justification: string,
      specificSection: string,
      exceptionType: 'Material Exception' | 'Immaterial Exception',
      effectiveDate?: string,
      expiryDate?: string,
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.requestException(
          id,
          justification,
          specificSection,
          exceptionType,
          effectiveDate,
          expiryDate,
          comments,
        );

        if (response.success) {
          // Refresh policies after exception request
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to request policy exception');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const exceptionGovernanceReview = useCallback(
    async (
      id: string,
      decision: 'Approved' | 'Rejected',
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.exceptionGovernanceReview(
          id,
          decision,
          comments,
        );

        if (response.success) {
          // Refresh policies after exception governance review
          await fetchPolicies();
          return true;
        } else {
          setError(
            response.error || 'Failed to submit exception governance review',
          );
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const approveException = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.approveException(id, comments);

        if (response.success) {
          // Refresh policies after exception approval
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to approve policy exception');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const rejectException = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.rejectException(id, comments);

        if (response.success) {
          // Refresh policies after exception rejection
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to reject policy exception');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  // Review workflow methods
  const initiatePolicyReview = useCallback(
    async (
      id: string,
      reviewType: string,
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.initiateReview(
          id,
          reviewType,
          comments,
        );

        if (response.success) {
          // Refresh policies after review initiation
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to initiate policy review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const ownerReview = useCallback(
    async (
      id: string,
      decision: 'No Updates Required' | 'Updates Required',
      comments?: string,
      changesDescription?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.ownerReview(
          id,
          decision,
          comments,
          changesDescription,
        );

        if (response.success) {
          // Refresh policies after owner review
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to submit owner review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const reviewGovernanceReview = useCallback(
    async (
      id: string,
      decision: 'Approved' | 'Rejected',
      comments?: string,
    ): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.reviewGovernanceReview(
          id,
          decision,
          comments,
        );

        if (response.success) {
          // Refresh policies after review governance review
          await fetchPolicies();
          return true;
        } else {
          setError(
            response.error || 'Failed to submit review governance review',
          );
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const approveReview = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.approveReview(id, comments);

        if (response.success) {
          // Refresh policies after review approval
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to approve policy review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  const rejectReview = useCallback(
    async (id: string, comments?: string): Promise<boolean> => {
      setError(null);

      try {
        const response = await policyApi.rejectReview(id, comments);

        if (response.success) {
          // Refresh policies after review rejection
          await fetchPolicies();
          return true;
        } else {
          setError(response.error || 'Failed to reject policy review');
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [fetchPolicies],
  );

  useEffect(() => {
    if (autoFetch) {
      fetchPolicies();
    }
  }, [fetchPolicies, autoFetch]);

  return {
    policies,
    pagination,
    metadata,
    loading,
    error,
    refetch: fetchPolicies,
    fetchPolicies,
    requestPolicyInitiation,
    updatePolicyStatus,
    governanceReview,
    grcReview,
    submitPolicyForReview,
    approvePolicy,
    rejectPolicy,
    deletePolicy,
    requestRetirement,
    retirementGovernanceReview,
    approveRetirement,
    rejectRetirement,
    requestException,
    exceptionGovernanceReview,
    approveException,
    rejectException,
    // Review workflow methods
    initiatePolicyReview,
    ownerReview,
    reviewGovernanceReview,
    approveReview,
    rejectReview,
  };
};

// Hook for policy statistics
interface UsePolicyStatsReturn {
  stats: PolicyStats | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UsePolicyStatsOptions {
  autoFetch?: boolean;
}

export const usePolicyStats = (
  options: UsePolicyStatsOptions = {},
): UsePolicyStatsReturn => {
  const { autoFetch = true } = options;
  const [stats, setStats] = useState<PolicyStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await policyApi.getPolicyStats();

      if (response.success && response.data) {
        setStats(response.data);
      } else {
        setError(response.error || 'Failed to fetch policy statistics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoFetch) {
      fetchStats();
    }
  }, [fetchStats, autoFetch]);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
};

// Hook for single policy
interface UsePolicyReturn {
  policy: Policy | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updatePolicy: (policyData: Partial<Policy>) => Promise<Policy | null>;
}

export const usePolicy = (id: string): UsePolicyReturn => {
  const [policy, setPolicy] = useState<Policy | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPolicy = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await policyApi.getPolicyById(id);

      if (response.success && response.data) {
        setPolicy(response.data);
      } else {
        setError(response.error || 'Failed to fetch policy');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const updatePolicy = useCallback(
    async (policyData: Partial<Policy>): Promise<Policy | null> => {
      if (!id) return null;

      setError(null);

      try {
        const response = await policyApi.updatePolicy(id, policyData);

        if (response.success && response.data) {
          setPolicy(response.data);
          return response.data;
        } else {
          setError(response.error || 'Failed to update policy');
          return null;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [id],
  );

  useEffect(() => {
    fetchPolicy();
  }, [fetchPolicy]);

  return {
    policy,
    loading,
    error,
    refetch: fetchPolicy,
    updatePolicy,
  };
};
