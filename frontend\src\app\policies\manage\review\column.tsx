'use client';

import * as React from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export type ReviewPrograms = {
  id: number;
  policyId: string;
  documentCode: string;
  name: string;
  documentName: string;
  documentType: string;
  version: string;
  policyType: string;
  categories: string;
  department: string;
  subDepartment: string;
  policyOwner: string;
  priorityScore: number;
  status: string;
  detailedStatus: string;
  reviewStatus: string;
  reviewType: string;
  classification: string;
  publishedDate: string;
  lastReviewDate: string;
  nextReviewDate: string;
  reviewInitiatedDate: string;
  ownerReviewDecision: string;
  ownerReviewDate: string;
  reviewCompletedDate: string;
};

const columnHelper = createColumnHelper<ReviewPrograms>();

export const columns = [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }),
  // 1. Policy ID - Unique identifier
  columnHelper.accessor('policyId', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy ID
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2 text-sm font-medium text-customBlueSecondary">
          <span>{row.getValue('policyId')}</span>
        </div>
      );
    },
    enableHiding: false,
  }),
  // 2. Policy Name - Main identifier
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy Name
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[200px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('name')}
      >
        {row.getValue('name')}
      </div>
    ),
    enableHiding: false,
  }),

  // 3. Document Type - Critical for categorization
  columnHelper.accessor('documentType', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Type</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('documentType') || 'Policy'}
      </div>
    ),
    enableHiding: false,
  }),
  // 4. Department - Ownership and responsibility
  columnHelper.accessor('department', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Department
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('department')}
      </div>
    ),
    enableHiding: false,
  }),

  // 5. Policy Owner - Key stakeholder
  columnHelper.accessor('policyOwner', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Owner</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[120px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('policyOwner')}
      >
        {row.getValue('policyOwner')}
      </div>
    ),
    enableHiding: false,
  }),

  // 6. Review Status - Current review workflow stage
  columnHelper.accessor('reviewStatus', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Review Status
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const reviewStatus = row.getValue('reviewStatus') as string;
      const getReviewStatusColor = (status: string) => {
        switch (status) {
          case 'Due':
            return 'text-orange-700 bg-orange-100';
          case 'In Progress':
            return 'text-blue-700 bg-blue-100';
          case 'Under Review':
            return 'text-yellow-700 bg-yellow-100';
          case 'Pending Approval':
            return 'text-green-700 bg-green-100';
          case 'Approved':
            return 'text-emerald-700 bg-emerald-100';
          case 'Rejected':
            return 'text-red-700 bg-red-100';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getReviewStatusColor(reviewStatus || 'Due')}`}
        >
          {reviewStatus || 'Due'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 7. Review Type - Scheduled, Ad-hoc, Compliance
  columnHelper.accessor('reviewType', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Review Type
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const reviewType = row.getValue('reviewType') as string;
      const getReviewTypeColor = (type: string) => {
        switch (type) {
          case 'Scheduled Review':
            return 'text-blue-700 bg-blue-100';
          case 'Ad-hoc Review':
            return 'text-purple-700 bg-purple-100';
          case 'Compliance Review':
            return 'text-red-700 bg-red-100';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getReviewTypeColor(reviewType || 'Scheduled Review')}`}
        >
          {reviewType || 'Scheduled Review'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 8. Next Review Date - When review is due
  columnHelper.accessor('nextReviewDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Next Review Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const nextReviewDate = row.getValue('nextReviewDate') as string;
      const isOverdue = nextReviewDate && new Date(nextReviewDate) < new Date();
      return (
        <div
          className={`text-sm font-medium ${isOverdue ? 'text-red-600' : 'text-customBlueSecondary'}`}
        >
          {nextReviewDate
            ? new Date(nextReviewDate).toLocaleDateString()
            : 'N/A'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 9. Last Review Date - When policy was last reviewed
  columnHelper.accessor('lastReviewDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Last Review Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const lastReviewDate = row.getValue('lastReviewDate') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {lastReviewDate
            ? new Date(lastReviewDate).toLocaleDateString()
            : 'Never'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 10. Review Initiated Date - When current review was started
  columnHelper.accessor('reviewInitiatedDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Review Initiated
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const reviewInitiatedDate = row.getValue('reviewInitiatedDate') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {reviewInitiatedDate
            ? new Date(reviewInitiatedDate).toLocaleDateString()
            : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 11. Owner Review Decision - Policy owner's decision
  columnHelper.accessor('ownerReviewDecision', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Owner Decision
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const decision = row.getValue('ownerReviewDecision') as string;
      const getDecisionColor = (decision: string) => {
        switch (decision) {
          case 'No Updates Required':
            return 'text-green-700 bg-green-100';
          case 'Updates Required':
            return 'text-orange-700 bg-orange-100';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getDecisionColor(decision)}`}
        >
          {decision || 'Pending'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 12. Published Date - When policy was published
  columnHelper.accessor('publishedDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Published Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const publishedDate = row.getValue('publishedDate') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {publishedDate ? new Date(publishedDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 13. Classification - Security level
  columnHelper.accessor('classification', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Classification
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const classification = row.getValue('classification') as string;
      const getClassificationColor = (classification: string) => {
        switch (classification) {
          case 'Top Secret':
            return 'text-red-700 bg-red-100';
          case 'Restricted':
            return 'text-red-600 bg-red-50';
          case 'Confidential':
            return 'text-orange-600 bg-orange-50';
          case 'Internal':
            return 'text-blue-600 bg-blue-50';
          case 'Public':
            return 'text-green-600 bg-green-50';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getClassificationColor(classification || 'Internal')}`}
        >
          {classification || 'Internal'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 14. Version - Document version tracking
  columnHelper.accessor('version', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Version
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('version') || '1.0'}
      </div>
    ),
    enableHiding: true,
  }),
];
