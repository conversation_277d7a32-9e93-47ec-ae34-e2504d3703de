import React from 'react';
import * as Popover from '@radix-ui/react-popover';
import { User, LogOut, UserCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function UserPopover() {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="flex items-center justify-center">
      <Popover.Root>
        <Popover.Trigger asChild>
          <button className="rounded-full bg-gray-100 p-2 transition-colors hover:bg-gray-200">
            <UserCircle className="h-6 w-6 text-gray-700" />
          </button>
        </Popover.Trigger>
        <Popover.Content
          align="end"
          className="mt-2 w-[280px] rounded-lg border border-gray-200 bg-white p-4 shadow-lg"
        >
          {/* User Info Section */}
          <div className="flex items-center space-x-3 border-b border-gray-200 pb-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">
                {user?.name || 'User'}
              </h3>
              <p className="text-sm text-gray-500">
                {user?.email || '<EMAIL>'}
              </p>
              <p className="mt-1 inline-block rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-400">
                {user?.role || 'User'}
              </p>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            <button
              className="group flex w-full items-center space-x-3 rounded-lg p-2 text-left transition-colors hover:bg-red-50"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-600 group-hover:text-red-700">
                Logout
              </span>
            </button>
          </div>
        </Popover.Content>
      </Popover.Root>
    </div>
  );
}
