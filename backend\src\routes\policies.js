const express = require('express');
const router = express.Router();
const {
  getAllPolicies,
  getPolicyById,
  requestPolicyInitiation,
  updatePolicy,
  deletePolicy,
  updatePolicyStatus,
  getPolicyStats,
  approvePolicy,
  rejectPolicy,
  getPolicyHistory,
  governanceReview,
  grcReview,
  submitPolicyForReview,
  uploadPolicyAttachment,
  requestPolicyRetirement,
  retirementGovernanceReview,
  approveRetirement,
  rejectRetirement,
  requestPolicyException,
  exceptionGovernanceReview,
  approveException,
  rejectException,
  // Review workflow controllers
  initiatePolicyReview,
  ownerReview,
  reviewGovernanceReview,
  approveReview,
  rejectReview,
} = require('../controllers/policyController');

const { protect, authorize, checkPermission, checkOwnership } = require('../middleware/auth');
const { validatePolicyRequest, validatePolicyUpdate } = require('../middleware/validation');

// Public routes (with optional auth)
router.get('/stats', getPolicyStats);

// Protected routes - require authentication
router.use(protect);

// GET /api/policies - Get all policies with filtering and pagination
router.get('/', getAllPolicies);

// GET /api/policies/:id - Get specific policy by ID
router.get('/:id', getPolicyById);

// POST /api/policies/request - Request policy initiation (Creator, Admin, Super Admin only)
router.post('/request', authorize('Creator', 'Admin', 'Super Admin'), validatePolicyRequest, requestPolicyInitiation);

// PUT /api/policies/:id - Update policy (only owner or admin)
router.put('/:id', validatePolicyUpdate, updatePolicy);

// DELETE /api/policies/:id - Delete policy (only owner or admin)
router.delete('/:id', deletePolicy);

// PUT /api/policies/:id/status - Update policy status
router.put('/:id/status', updatePolicyStatus);

// POST /api/policies/:id/approve - Approve policy
router.post('/:id/approve', checkPermission('approve_policy'), approvePolicy);

// POST /api/policies/:id/reject - Reject policy
router.post('/:id/reject', checkPermission('approve_policy'), rejectPolicy);

// GET /api/policies/:id/history - Get policy history/audit trail
router.get('/:id/history', getPolicyHistory);

// POST /api/policies/:id/governance-review - Governance review (Approver from Governance dept, Admin, Super Admin)
router.post('/:id/governance-review', authorize('Approver', 'Admin', 'Super Admin'), governanceReview);

// POST /api/policies/:id/grc-review - GRC review (Reviewer from GRC dept, Admin, Super Admin)
router.post('/:id/grc-review', authorize('Reviewer', 'Admin', 'Super Admin'), grcReview);

// POST /api/policies/:id/submit-for-review - Submit policy for review (Draft → Under Review)
router.post('/:id/submit-for-review', submitPolicyForReview);

// POST /api/policies/:id/request-retirement - Request policy retirement (Policy Owner, Admin, Super Admin)
router.post('/:id/request-retirement', requestPolicyRetirement);

// POST /api/policies/:id/retirement-governance-review - Retirement governance review (Approver from Governance dept, Admin, Super Admin)
router.post('/:id/retirement-governance-review', authorize('Approver', 'Admin', 'Super Admin'), retirementGovernanceReview);

// POST /api/policies/:id/approve-retirement - Approve policy retirement (Final Approvers)
router.post('/:id/approve-retirement', authorize('Approver', 'Admin', 'Super Admin'), approveRetirement);

// POST /api/policies/:id/reject-retirement - Reject policy retirement (Final Approvers)
router.post('/:id/reject-retirement', authorize('Approver', 'Admin', 'Super Admin'), rejectRetirement);

// POST /api/policies/:id/request-exception - Request policy exception (Policy Owner, Admin, Super Admin)
router.post('/:id/request-exception', requestPolicyException);

// POST /api/policies/:id/exception-governance-review - Exception governance review (Approver from Governance dept, Admin, Super Admin)
router.post('/:id/exception-governance-review', authorize('Approver', 'Admin', 'Super Admin'), exceptionGovernanceReview);

// POST /api/policies/:id/approve-exception - Approve policy exception (Final Approvers)
router.post('/:id/approve-exception', authorize('Approver', 'Admin', 'Super Admin'), approveException);

// POST /api/policies/:id/reject-exception - Reject policy exception (Final Approvers)
router.post('/:id/reject-exception', authorize('Approver', 'Admin', 'Super Admin'), rejectException);

// Review Workflow Routes

// POST /api/policies/:id/initiate-review - Initiate policy review (Policy Owner, Admin, Super Admin)
router.post('/:id/initiate-review', initiatePolicyReview);

// POST /api/policies/:id/owner-review - Policy owner review (Policy Owner, Admin, Super Admin)
router.post('/:id/owner-review', ownerReview);

// POST /api/policies/:id/review-governance-review - Review governance review (Approver from Governance dept, Admin, Super Admin)
router.post('/:id/review-governance-review', authorize('Approver', 'Admin', 'Super Admin'), reviewGovernanceReview);

// POST /api/policies/:id/approve-review - Approve policy review (Final Approvers)
router.post('/:id/approve-review', authorize('Approver', 'Admin', 'Super Admin'), approveReview);

// POST /api/policies/:id/reject-review - Reject policy review (Final Approvers)
router.post('/:id/reject-review', authorize('Approver', 'Admin', 'Super Admin'), rejectReview);

// Note: File upload routes moved to /api/uploads/policies/:id/attachments

module.exports = router;
