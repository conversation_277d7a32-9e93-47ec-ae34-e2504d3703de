'use client';

import React, { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X, Upload, FileText, Download, Trash2, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { StatusBadge } from '@/components/StatusBadge';
import { CommentsAndHistory } from '@/components/CommentsAndHistory';
import { ExceptionDetails } from '@/components/ExceptionDetails';
import { PolicyPublicationModal } from '@/components/PolicyPublicationModal';
import { Policy, policyApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface PolicyDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  policy: Policy | null;
  onStatusUpdate: (
    policyId: string,
    newStatus: string,
    comments?: string,
    effectiveDate?: string,
    selectedGroups?: string[],
  ) => Promise<boolean>;
  onGovernanceReview: (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  onGrcReview: (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  onSubmitForReview?: (policyId: string, comments?: string) => Promise<boolean>;
  onApprovePolicy?: (policyId: string, comments?: string) => Promise<boolean>;
  onRejectPolicy?: (policyId: string, comments?: string) => Promise<boolean>;
  onRequestRetirement?: (
    policyId: string,
    justification: string,
    effectiveDate?: string,
    comments?: string,
  ) => Promise<boolean>;
  onRetirementGovernanceReview?: (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  onApproveRetirement?: (
    policyId: string,
    comments?: string,
  ) => Promise<boolean>;
  onRejectRetirement?: (
    policyId: string,
    comments?: string,
  ) => Promise<boolean>;
  onRequestException?: (
    policyId: string,
    justification: string,
    specificSection: string,
    exceptionType: 'Material Exception' | 'Immaterial Exception',
    effectiveDate?: string,
    expiryDate?: string,
    comments?: string,
  ) => Promise<boolean>;
  onExceptionGovernanceReview?: (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  onApproveException?: (
    policyId: string,
    comments?: string,
  ) => Promise<boolean>;
  onRejectException?: (policyId: string, comments?: string) => Promise<boolean>;
  // Review workflow handlers
  onInitiateReview?: (
    policyId: string,
    reviewType: string,
    comments?: string,
  ) => Promise<boolean>;
  onOwnerReview?: (
    policyId: string,
    decision: 'No Updates Required' | 'Updates Required',
    comments?: string,
    changesDescription?: string,
  ) => Promise<boolean>;
  onReviewGovernanceReview?: (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => Promise<boolean>;
  onApproveReview?: (policyId: string, comments?: string) => Promise<boolean>;
  onRejectReview?: (policyId: string, comments?: string) => Promise<boolean>;
  onPolicyUpdate?: () => void; // Callback to refresh policy data
  userRole?: string;
  loading?: boolean;
}

export const PolicyDetailsModal: React.FC<PolicyDetailsModalProps> = ({
  isOpen,
  onClose,
  policy,
  onStatusUpdate,
  onGovernanceReview,
  onGrcReview,
  onSubmitForReview,
  onApprovePolicy,
  onRejectPolicy,
  onRequestRetirement,
  onRetirementGovernanceReview,
  onApproveRetirement,
  onRejectRetirement,
  onRequestException,
  onExceptionGovernanceReview,
  onApproveException,
  onRejectException,
  onInitiateReview,
  onOwnerReview,
  onReviewGovernanceReview,
  onApproveReview,
  onRejectReview,
  onPolicyUpdate,
  userRole = 'Creator',
  loading = false,
}) => {
  const [comments, setComments] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [retirementJustification, setRetirementJustification] = useState('');
  const [retirementEffectiveDate, setRetirementEffectiveDate] = useState('');
  const [exceptionJustification, setExceptionJustification] = useState('');
  const [exceptionSpecificSection, setExceptionSpecificSection] = useState('');
  const [exceptionType, setExceptionType] = useState<
    'Material Exception' | 'Immaterial Exception'
  >('Immaterial Exception');
  const [exceptionEffectiveDate, setExceptionEffectiveDate] = useState('');
  const [exceptionExpiryDate, setExceptionExpiryDate] = useState('');
  // Review workflow state
  const [reviewType, setReviewType] = useState<string>('Scheduled Review');
  const [ownerReviewDecision, setOwnerReviewDecision] = useState<
    'No Updates Required' | 'Updates Required'
  >('No Updates Required');
  const [changesDescription, setChangesDescription] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showReplaceUpload, setShowReplaceUpload] = useState(false);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [showPublicationModal, setShowPublicationModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useAuth();

  if (!policy) return null;

  // Permission helper functions
  const canEditPolicy = () => {
    if (!user) return false;

    // Check if policy can be edited based on status
    // Only Draft status allows editing - Request Initiated is just a request waiting for approval
    const editableStatuses = ['Draft'];
    if (!editableStatuses.includes(policy.status)) return false;

    // Check user permissions
    const userRole = user.role;
    if (['Super Admin', 'Admin'].includes(userRole)) return true;
    if (
      userRole === 'Creator' &&
      String((policy?.policyOwner?.id as any)?._id) === String(user?._id)
    )
      return true;

    return false;
  };

  const canViewDocument = () => {
    return !!policy.attachments && policy.attachments.length > 0;
  };

  const canCreateDocument = () => {
    if (!user) return false;

    // Can only create documents for policies in Draft status
    // Request Initiated is just a request - no documents until approved for drafting
    const creatableStatuses = ['Draft'];
    if (!creatableStatuses.includes(policy.status)) return false;

    // Check user permissions
    const userRole = user.role;
    if (['Super Admin', 'Admin'].includes(userRole)) return true;
    if (
      userRole === 'Creator' &&
      String((policy?.policyOwner?.id as any)?._id) === String(user?._id)
    )
      return true;

    return false;
  };

  const handleCreateDocument = async () => {
    if (!policy) return;

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/onlyoffice/documents/${policy._id}/create`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            title: policy.name,
            type: 'docx',
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create document');
      }

      toast({
        title: 'Success',
        description: 'Document created successfully. Redirecting to editor...',
      });

      // Close the modal
      onClose();

      // Redirect to editor page
      router.push(`/policies/editor/${policy._id}`);
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create document',
        variant: 'destructive',
      });
    }
  };

  const handleEditDocument = () => {
    if (!policy || !policy.attachments || policy.attachments.length === 0)
      return;

    // Close the modal
    onClose();

    // Redirect to editor page
    router.push(`/policies/editor/${policy._id}`);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file type - only allow Word documents
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/msword', // .doc
        'application/pdf', // .pdf (also commonly used for policies)
      ];

      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'Invalid File Type',
          description:
            'Please select a Word document (.doc, .docx) or PDF file for the policy document.',
          variant: 'destructive',
        });
        return;
      }

      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast({
          title: 'File Too Large',
          description: 'File size must be less than 10MB.',
          variant: 'destructive',
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile || !policy) return;

    setUploadLoading(true);
    try {
      const response = await policyApi.uploadAttachment(
        policy._id,
        selectedFile,
      );

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Policy document uploaded successfully',
        });
        setSelectedFile(null);
        setShowReplaceUpload(false);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        // Refresh the policy data to show the new attachment
        if (onPolicyUpdate) {
          onPolicyUpdate();
        }
      } else {
        throw new Error(response.message || 'Upload failed');
      }
    } catch (error) {
      toast({
        title: 'Upload Failed',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to upload policy document',
        variant: 'destructive',
      });
    } finally {
      setUploadLoading(false);
    }
  };

  const handleRemoveDocument = () => {
    // Clear any selected file and show upload interface
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    // Show the replace upload interface
    setShowReplaceUpload(true);
    toast({
      title: 'Replace Document',
      description: 'Select a new document to replace the current one',
    });
  };

  const handleCancelReplace = () => {
    setShowReplaceUpload(false);
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleViewDocument = () => {
    if (!policy?.attachments?.[0]) return;

    const attachment = policy.attachments[0];
    const fileName = attachment.originalName || attachment.filename || '';
    const fileExtension = fileName.toLowerCase().split('.').pop();

    // Check if it's a PDF (can be viewed in iframe)
    if (fileExtension === 'pdf') {
      setShowDocumentViewer(true);
    } else {
      // For Word documents and other non-PDF files, open in new tab
      const url = attachment.url.startsWith('http')
        ? attachment.url
        : `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}${attachment.url}`;

      // Show a message about Word document viewing
      toast({
        title: 'Opening Document',
        description:
          'Word documents will open in a new tab or download to your device for viewing.',
      });

      window.open(url, '_blank');
    }
  };

  const handleCloseDocumentViewer = () => {
    setShowDocumentViewer(false);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleAction = async (
    action: string,
    decision?: 'Approved' | 'Rejected',
  ) => {
    if (!policy) return;

    setActionLoading(true);
    try {
      let success = false;
      let errorMessage = 'Failed to update policy status';

      switch (action) {
        case 'governance-approve':
          success = await onGovernanceReview(policy._id, 'Approved', comments);
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy approved for drafting',
            });
          } else {
            errorMessage = 'Failed to approve governance review';
          }
          break;
        case 'governance-reject':
          success = await onGovernanceReview(policy._id, 'Rejected', comments);
          if (success) {
            toast({
              title: 'Policy Rejected',
              description: 'Policy request has been rejected',
            });
          } else {
            errorMessage = 'Failed to reject governance review';
          }
          break;
        case 'submit-for-review':
          // Check if policy document is attached
          if (!policy.attachments || policy.attachments.length === 0) {
            toast({
              title: 'Policy Document Required',
              description:
                'A policy document must be attached before submitting for review',
              variant: 'destructive',
            });
            return;
          }

          if (onSubmitForReview) {
            success = await onSubmitForReview(policy._id, comments);
          } else {
            success = await onStatusUpdate(
              policy._id,
              'Under Review',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy submitted for review',
            });
          } else {
            errorMessage = 'Failed to submit policy for review';
          }
          break;
        case 'grc-approve':
          success = await onGrcReview(policy._id, 'Approved', comments);
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy approved by GRC team',
            });
          } else {
            errorMessage = 'Failed to approve GRC review';
          }
          break;
        case 'grc-reject':
          success = await onGrcReview(policy._id, 'Rejected', comments);
          if (success) {
            toast({
              title: 'Policy Rejected',
              description: 'Policy rejected by GRC team',
            });
          } else {
            errorMessage = 'Failed to reject GRC review';
          }
          break;
        case 'final-approve':
          // Use the dedicated approve policy handler if available
          if (onApprovePolicy) {
            success = await onApprovePolicy(policy._id, comments);
          } else {
            // Fallback to status update
            success = await onStatusUpdate(policy._id, 'Approved', comments);
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy approved and ready for publication',
            });
          } else {
            errorMessage = 'Failed to approve policy';
          }
          break;
        case 'final-reject':
          // Use the dedicated reject policy handler if available
          if (onRejectPolicy) {
            success = await onRejectPolicy(policy._id, comments);
          } else {
            // Fallback to status update
            success = await onStatusUpdate(policy._id, 'Draft', comments);
          }
          if (success) {
            toast({
              title: 'Policy Rejected',
              description:
                'Policy has been rejected and sent back for revision',
            });
          } else {
            errorMessage = 'Failed to reject policy';
          }
          break;
        case 'publish':
          // Show publication modal instead of directly publishing
          setShowPublicationModal(true);
          setActionLoading(false); // Reset loading since we're showing modal
          return; // Don't continue with the rest of the function
        case 'archive':
          success = await onStatusUpdate(policy._id, 'Archived', comments);
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy has been archived',
            });
          } else {
            errorMessage = 'Failed to archive policy';
          }
          break;
        case 'request-retirement':
          if (onRequestRetirement) {
            success = await onRequestRetirement(
              policy._id,
              retirementJustification,
              retirementEffectiveDate,
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy retirement request submitted',
            });
          } else {
            errorMessage = 'Failed to request policy retirement';
          }
          break;
        case 'retirement-governance-approve':
          if (onRetirementGovernanceReview) {
            success = await onRetirementGovernanceReview(
              policy._id,
              'Approved',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Retirement request approved by governance',
            });
          } else {
            errorMessage = 'Failed to approve retirement governance review';
          }
          break;
        case 'retirement-governance-reject':
          if (onRetirementGovernanceReview) {
            success = await onRetirementGovernanceReview(
              policy._id,
              'Rejected',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Retirement Request Rejected',
              description: 'Retirement request rejected by governance',
            });
          } else {
            errorMessage = 'Failed to reject retirement governance review';
          }
          break;
        case 'approve-retirement':
          if (onApproveRetirement) {
            success = await onApproveRetirement(policy._id, comments);
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy has been retired',
            });
          } else {
            errorMessage = 'Failed to approve policy retirement';
          }
          break;
        case 'reject-retirement':
          if (onRejectRetirement) {
            success = await onRejectRetirement(policy._id, comments);
          }
          if (success) {
            toast({
              title: 'Retirement Rejected',
              description: 'Policy retirement has been rejected',
            });
          } else {
            errorMessage = 'Failed to reject policy retirement';
          }
          break;
        case 'request-exception':
          if (onRequestException) {
            success = await onRequestException(
              policy._id,
              exceptionJustification,
              exceptionSpecificSection,
              exceptionType,
              exceptionEffectiveDate,
              exceptionExpiryDate,
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy exception request submitted',
            });
          } else {
            errorMessage = 'Failed to request policy exception';
          }
          break;
        case 'exception-governance-approve':
          if (onExceptionGovernanceReview) {
            success = await onExceptionGovernanceReview(
              policy._id,
              'Approved',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Exception request approved by governance',
            });
          } else {
            errorMessage = 'Failed to approve exception governance review';
          }
          break;
        case 'exception-governance-reject':
          if (onExceptionGovernanceReview) {
            success = await onExceptionGovernanceReview(
              policy._id,
              'Rejected',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Exception Request Rejected',
              description: 'Exception request rejected by governance',
            });
          } else {
            errorMessage = 'Failed to reject exception governance review';
          }
          break;
        case 'approve-exception':
          if (onApproveException) {
            success = await onApproveException(policy._id, comments);
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy exception has been approved',
            });
          } else {
            errorMessage = 'Failed to approve policy exception';
          }
          break;
        case 'reject-exception':
          if (onRejectException) {
            success = await onRejectException(policy._id, comments);
          }
          if (success) {
            toast({
              title: 'Exception Rejected',
              description: 'Policy exception has been rejected',
            });
          } else {
            errorMessage = 'Failed to reject policy exception';
          }
          break;
        case 'initiate-review':
          if (onInitiateReview) {
            success = await onInitiateReview(policy._id, reviewType, comments);
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy review has been initiated',
            });
          } else {
            errorMessage = 'Failed to initiate policy review';
          }
          break;
        case 'owner-review':
          if (onOwnerReview) {
            success = await onOwnerReview(
              policy._id,
              ownerReviewDecision,
              comments,
              changesDescription,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy owner review completed',
            });
          } else {
            errorMessage = 'Failed to complete owner review';
          }
          break;
        case 'review-governance-approve':
          if (onReviewGovernanceReview) {
            success = await onReviewGovernanceReview(
              policy._id,
              'Approved',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Review approved by governance',
            });
          } else {
            errorMessage = 'Failed to approve review governance';
          }
          break;
        case 'review-governance-reject':
          if (onReviewGovernanceReview) {
            success = await onReviewGovernanceReview(
              policy._id,
              'Rejected',
              comments,
            );
          }
          if (success) {
            toast({
              title: 'Review Rejected',
              description: 'Review rejected by governance',
            });
          } else {
            errorMessage = 'Failed to reject review governance';
          }
          break;
        case 'approve-review':
          if (onApproveReview) {
            success = await onApproveReview(policy._id, comments);
          }
          if (success) {
            toast({
              title: 'Success',
              description: 'Policy review has been approved',
            });
          } else {
            errorMessage = 'Failed to approve policy review';
          }
          break;
        case 'reject-review':
          if (onRejectReview) {
            success = await onRejectReview(policy._id, comments);
          }
          if (success) {
            toast({
              title: 'Review Rejected',
              description: 'Policy review has been rejected',
            });
          } else {
            errorMessage = 'Failed to reject policy review';
          }
          break;
      }

      // If any action failed, show error toast
      if (!success) {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
        return; // Don't close modal or clear comments on failure
      }

      setComments('');
      setRetirementJustification('');
      setRetirementEffectiveDate('');
      setExceptionJustification('');
      setExceptionSpecificSection('');
      setExceptionType('Immaterial Exception');
      setExceptionEffectiveDate('');
      setExceptionExpiryDate('');
      setReviewType('Scheduled Review');
      setOwnerReviewDecision('No Updates Required');
      setChangesDescription('');
      onClose();
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to update policy status',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handlePublicationSubmit = async (publicationData: {
    effectiveDate: string;
    targetAudience: 'All Employees' | 'Selected Groups' | 'Department Only';
    selectedGroups?: string[];
    comments?: string;
  }) => {
    if (!policy) return;

    try {
      const success = await onStatusUpdate(
        policy._id,
        'Published',
        publicationData.comments || comments,
        publicationData.effectiveDate,
        publicationData.selectedGroups || [],
      );
      if (success) {
        toast({
          title: 'Success',
          description:
            'Policy has been published with effective date and target audience',
        });
        setComments('');
        setShowPublicationModal(false);
        onClose();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to publish policy',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to publish policy',
        variant: 'destructive',
      });
    }
  };

  const getAvailableActions = () => {
    const actions = [];
    const currentRole = user?.role || userRole;
    const hasAttachment = policy.attachments && policy.attachments.length > 0;

    console.log(
      'my check',
      String((policy?.policyOwner?.id as any)?._id),
      String(user?._id),
    );

    switch (policy.status) {
      case 'Request Initiated':
        // Check if user can perform governance review
        const canPerformGovernanceReview =
          (currentRole === 'Approver' && user?.department === 'Governance') ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin';

        if (canPerformGovernanceReview) {
          actions.push(
            {
              id: 'governance-approve',
              label: 'Approve for Drafting',
              variant: 'default' as const,
              color: 'green',
            },
            {
              id: 'governance-reject',
              label: 'Reject Request',
              variant: 'destructive' as const,
              color: 'red',
            },
          );
        }
        break;

      case 'Draft':
        if (
          currentRole === 'Creator' ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin'
        ) {
          actions.push({
            id: 'submit-for-review',
            label: 'Submit for Review',
            variant: 'default' as const,
            color: 'blue',
            disabled: !hasAttachment,
            tooltip: !hasAttachment
              ? 'A policy document must be attached before submitting for review'
              : undefined,
          });
        }
        break;

      case 'Under Review':
        // Check if user can perform GRC review
        const canPerformGrcReview =
          (currentRole === 'Reviewer' && user?.department === 'GRC') ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin';

        if (canPerformGrcReview) {
          actions.push(
            {
              id: 'grc-approve',
              label: 'Approve Policy',
              variant: 'default' as const,
              color: 'green',
            },
            {
              id: 'grc-reject',
              label: 'Reject & Send Back',
              variant: 'destructive' as const,
              color: 'red',
            },
          );
        }
        break;

      case 'Pending Approval':
        if (
          currentRole === 'Approver' ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin'
        ) {
          actions.push(
            {
              id: 'final-approve',
              label: 'Final Approval',
              variant: 'default' as const,
              color: 'green',
            },
            {
              id: 'final-reject',
              label: 'Reject & Send Back',
              variant: 'destructive' as const,
              color: 'red',
            },
          );
        }
        break;

      case 'Approved':
        if (
          currentRole === 'Publisher' ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin'
        ) {
          actions.push(
            {
              id: 'publish',
              label: 'Publish Policy',
              variant: 'default' as const,
              color: 'blue',
            },
            {
              id: 'archive',
              label: 'Archive Policy',
              variant: 'outline' as const,
              color: 'gray',
            },
          );
        }
        break;

      case 'Archived':
        if (
          currentRole === 'Publisher' ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin'
        ) {
          actions.push({
            id: 'publish',
            label: 'Publish Policy',
            variant: 'default' as const,
            color: 'blue',
          });
        }
        break;

      case 'Published':
        // Check if policy is due for review or in review workflow
        const isDueForReview =
          policy.nextReviewDate &&
          new Date(policy.nextReviewDate) <= new Date() &&
          (!policy.reviewStatus || policy.reviewStatus === 'None');

        const canInitiateReview =
          currentRole === 'Admin' ||
          currentRole === 'Super Admin' ||
          (currentRole === 'Creator' &&
            String((policy?.policyOwner?.id as any)?._id) ===
              String(user?._id));

        if (isDueForReview && canInitiateReview) {
          actions.push({
            id: 'initiate-review',
            label: 'Initiate Review',
            variant: 'default' as const,
            color: 'blue',
          });
        }

        // Handle review workflow based on reviewStatus
        if (policy.reviewStatus) {
          switch (policy.reviewStatus) {
            case 'Due':
            case 'In Progress':
              // Policy owner can complete their review
              const canCompleteOwnerReview =
                (currentRole === 'Creator' &&
                  String((policy?.policyOwner?.id as any)?._id) ===
                    String(user?._id)) ||
                currentRole === 'Admin' ||
                currentRole === 'Super Admin';

              if (canCompleteOwnerReview) {
                actions.push({
                  id: 'owner-review',
                  label: 'Complete Review',
                  variant: 'default' as const,
                  color: 'green',
                });
              }
              break;

            case 'Under Review':
              // Governance team can review
              const canPerformReviewGovernanceReview =
                (currentRole === 'Approver' &&
                  user?.department === 'Governance') ||
                currentRole === 'Admin' ||
                currentRole === 'Super Admin';

              if (canPerformReviewGovernanceReview) {
                actions.push(
                  {
                    id: 'review-governance-approve',
                    label: 'Approve Review',
                    variant: 'default' as const,
                    color: 'green',
                  },
                  {
                    id: 'review-governance-reject',
                    label: 'Reject Review',
                    variant: 'destructive' as const,
                    color: 'red',
                  },
                );
              }
              break;

            case 'Pending Approval':
              // Final approvers based on policy category
              let hasReviewApprovalAuthority = false;
              if (policy.policyCategory === 'Corporate Policies') {
                // Board Committee approval - Admin/Super Admin
                hasReviewApprovalAuthority = ['Admin', 'Super Admin'].includes(
                  currentRole,
                );
              } else if (policy.policyCategory === 'Operational Policies') {
                // CEO approval - Approver/Admin/Super Admin
                hasReviewApprovalAuthority = [
                  'Approver',
                  'Admin',
                  'Super Admin',
                ].includes(currentRole);
              }

              if (hasReviewApprovalAuthority) {
                actions.push(
                  {
                    id: 'approve-review',
                    label: 'Approve Review',
                    variant: 'default' as const,
                    color: 'green',
                  },
                  {
                    id: 'reject-review',
                    label: 'Reject Review',
                    variant: 'destructive' as const,
                    color: 'red',
                  },
                );
              }
              break;
          }
        }
        break;

      case 'Under Annual Review':
        if (
          currentRole === 'Reviewer' ||
          currentRole === 'Creator' ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin'
        ) {
          actions.push(
            {
              id: 'submit-for-grc',
              label: 'Submit Updates',
              variant: 'default' as const,
              color: 'blue',
            },
            {
              id: 'final-approve',
              label: 'No Changes Needed',
              variant: 'outline' as const,
              color: 'green',
            },
          );
        }
        break;

      case 'Retirement Requested':
        // Check if user can perform retirement governance review
        const canPerformRetirementGovernanceReview =
          (currentRole === 'Approver' && user?.department === 'Governance') ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin';

        if (canPerformRetirementGovernanceReview) {
          actions.push(
            {
              id: 'retirement-governance-approve',
              label: 'Approve Retirement',
              variant: 'default' as const,
              color: 'green',
            },
            {
              id: 'retirement-governance-reject',
              label: 'Reject Retirement',
              variant: 'destructive' as const,
              color: 'red',
            },
          );
        }
        break;

      case 'Retirement Pending Approval':
        // Check approval authority based on policy category
        let hasRetirementApprovalAuthority = false;
        if (policy.policyCategory === 'Corporate Policies') {
          // Board Committee approval - Admin/Super Admin
          hasRetirementApprovalAuthority = ['Admin', 'Super Admin'].includes(
            currentRole,
          );
        } else if (policy.policyCategory === 'Operational Policies') {
          // CEO approval - Approver/Admin/Super Admin
          hasRetirementApprovalAuthority = [
            'Approver',
            'Admin',
            'Super Admin',
          ].includes(currentRole);
        }

        if (hasRetirementApprovalAuthority) {
          actions.push(
            {
              id: 'approve-retirement',
              label: 'Approve Retirement',
              variant: 'default' as const,
              color: 'green',
            },
            {
              id: 'reject-retirement',
              label: 'Reject Retirement',
              variant: 'destructive' as const,
              color: 'red',
            },
          );
        }
        break;

      case 'Published':
        // Add retirement request option for policy owners
        const canRequestRetirement =
          (currentRole === 'Creator' &&
            String((policy?.policyOwner?.id as any)?._id) ===
              String(user?._id)) ||
          currentRole === 'Admin' ||
          currentRole === 'Super Admin';

        if (canRequestRetirement) {
          actions.push({
            id: 'request-retirement',
            label: 'Request Retirement',
            variant: 'outline' as const,
            color: 'orange',
          });
        }

        // Add exception request option for policy owners (only if no active exception)
        const canRequestException =
          ((currentRole === 'Creator' &&
            String((policy?.policyOwner?.id as any)?._id) ===
              String(user?._id)) ||
            currentRole === 'Admin' ||
            currentRole === 'Super Admin') &&
          (!policy.exceptionStatus || policy.exceptionStatus === 'None');

        if (canRequestException) {
          actions.push({
            id: 'request-exception',
            label: 'Request Exception',
            variant: 'outline' as const,
            color: 'purple',
          });
        }

        // Removed archive action for published policies
        // Published policies should use retirement workflow instead
        break;
    }

    // Handle exception workflow actions based on exceptionStatus
    if (policy.status === 'Published' && policy.exceptionStatus) {
      switch (policy.exceptionStatus) {
        case 'Requested':
          // Check if user can perform exception governance review
          const canPerformExceptionGovernanceReview =
            (currentRole === 'Approver' && user?.department === 'Governance') ||
            currentRole === 'Admin' ||
            currentRole === 'Super Admin';

          if (canPerformExceptionGovernanceReview) {
            actions.push(
              {
                id: 'exception-governance-approve',
                label: 'Approve Exception',
                variant: 'default' as const,
                color: 'green',
              },
              {
                id: 'exception-governance-reject',
                label: 'Reject Exception',
                variant: 'destructive' as const,
                color: 'red',
              },
            );
          }
          break;

        case 'Pending Approval':
          // Check approval authority based on exception type
          let hasExceptionApprovalAuthority = false;
          if (policy.exceptionRequest?.exceptionType === 'Material Exception') {
            // Board Committee approval - Admin/Super Admin
            hasExceptionApprovalAuthority = ['Admin', 'Super Admin'].includes(
              currentRole,
            );
          } else if (
            policy.exceptionRequest?.exceptionType === 'Immaterial Exception'
          ) {
            // CEO approval - Approver/Admin/Super Admin
            hasExceptionApprovalAuthority = [
              'Approver',
              'Admin',
              'Super Admin',
            ].includes(currentRole);
          }

          if (hasExceptionApprovalAuthority) {
            actions.push(
              {
                id: 'approve-exception',
                label: 'Approve Exception',
                variant: 'default' as const,
                color: 'green',
              },
              {
                id: 'reject-exception',
                label: 'Reject Exception',
                variant: 'destructive' as const,
                color: 'red',
              },
            );
          }
          break;
      }
    }

    return actions;
  };

  const availableActions = getAvailableActions();

  const getActionIcon = (actionId: string) => {
    return '';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPrimitive.Portal>
        {/* Transparent overlay */}
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black opacity-40" />
        {/* Custom content without default overlay */}
        <DialogPrimitive.Content
          className={cn(
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid max-h-[90vh] w-full max-w-4xl translate-x-[-50%] translate-y-[-50%] gap-4 overflow-y-auto border bg-background p-6 shadow-lg duration-200 sm:rounded-lg',
          )}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {showDocumentViewer ? (
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCloseDocumentViewer}
                    className="mr-2"
                  >
                    ← Back
                  </Button>
                  <FileText className="h-5 w-5 text-blue-600" />
                  Policy Document Viewer
                </div>
              ) : (
                <span>{policy.name}</span>
              )}
              {!showDocumentViewer && (
                <div className="flex items-center gap-2">
                  <StatusBadge status={policy.status} />
                  {policy.exceptionStatus &&
                    policy.exceptionStatus !== 'None' && (
                      <Badge
                        variant="outline"
                        className="border-purple-200 bg-purple-50 text-purple-700"
                      >
                        Exception: {policy.exceptionStatus}
                      </Badge>
                    )}
                </div>
              )}
            </DialogTitle>
            <DialogDescription>
              {showDocumentViewer
                ? policy?.attachments?.[0]?.originalName
                : `Policy ID: ${policy.policyId} | Document Code: ${policy.documentCode}`}
            </DialogDescription>

            {/* Document Viewer Content */}
            {showDocumentViewer ? (
              <div className="mt-4">
                {policy?.attachments?.[0] && (
                  <div className="h-[70vh] w-full overflow-hidden rounded-lg border">
                    <iframe
                      src={
                        policy.attachments[0].url.startsWith('http')
                          ? policy.attachments[0].url
                          : `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}${policy.attachments[0].url}`
                      }
                      className="h-full w-full"
                      title="Policy Document"
                      style={{ border: 'none' }}
                    />
                  </div>
                )}
              </div>
            ) : (
              <>
                {/* Quick Action Bar */}
                {/* <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <p className="text-xs text-gray-600 uppercase tracking-wide">Current Status</p>
                  <StatusBadge status={policy.status} />
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 uppercase tracking-wide">Your Role</p>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                    {userRole}
                  </span>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 uppercase tracking-wide">Available Actions</p>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                    {availableActions.length} action{availableActions.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
              {availableActions.length > 0 && (
                <div className="text-right">
                  <p className="text-xs text-gray-600 mb-1">Quick Actions</p>
                  <div className="flex gap-2">
                    {availableActions.slice(0, 2).map((action) => (
                      <Button
                        key={action.id}
                        size="sm"
                        variant={action.variant}
                        onClick={() => handleAction(action.id)}
                        disabled={actionLoading || loading}
                        className="text-xs"
                      >
                        {getActionIcon(action.id)} {action.label}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
              </div>
            </div> */}
              </>
            )}
          </DialogHeader>

          {!showDocumentViewer && (
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">Policy Details</TabsTrigger>
                <TabsTrigger value="exception">Exception</TabsTrigger>
                <TabsTrigger value="comments">Comments & History</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="mt-6 space-y-6">
                {/* Policy Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Policy Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Document Name
                        </Label>
                        <p className="text-sm">
                          {policy.documentName || policy.name}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Document Type
                        </Label>
                        <p className="text-sm">
                          {policy.documentType || 'Policy'}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Version
                        </Label>
                        <p className="text-sm">{policy.version || '1.0'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Department
                        </Label>
                        <p className="text-sm">{policy.department}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Policy Category
                        </Label>
                        <p className="text-sm">
                          {policy.policyCategory || 'Operational Policies'}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Classification
                        </Label>
                        <p className="text-sm">
                          {policy.classification || 'Internal'}
                        </p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-600">
                        Categories
                      </Label>
                      <div className="mt-1 flex flex-wrap gap-2">
                        {policy.categories.map((category, index) => (
                          <Badge key={index} variant="secondary">
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {policy.description && (
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Description
                        </Label>
                        <p className="text-sm text-gray-700">
                          {policy.description}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Policy Document */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <FileText className="h-5 w-5" />
                      Policy Document
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Existing Policy Document */}
                    {policy.attachments &&
                      policy.attachments.length > 0 &&
                      !showReplaceUpload && (
                        <div className="space-y-3">
                          <Label className="text-sm font-medium text-gray-600">
                            Current Policy Document
                          </Label>
                          <div className="flex items-center justify-between rounded-lg border bg-gray-50 p-3">
                            <div className="flex items-center gap-3">
                              <FileText className="h-4 w-4 text-blue-600" />
                              <div>
                                <p className="text-sm font-medium">
                                  {policy.attachments?.[0]?.originalName}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {policy.attachments?.[0] &&
                                    formatFileSize(
                                      policy.attachments[0].size,
                                    )}{' '}
                                  • Uploaded{' '}
                                  {policy.attachments?.[0] &&
                                    new Date(
                                      policy.attachments[0].uploadedAt,
                                    ).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {/* Primary OnlyOffice Button - Dynamic based on permissions */}
                              <Button
                                variant="default"
                                size="sm"
                                onClick={handleEditDocument}
                                className="bg-blue-600 text-white hover:bg-blue-700"
                              >
                                {canEditPolicy() ? (
                                  <>
                                    <Edit className="mr-1 h-4 w-4" />
                                    Edit with OnlyOffice
                                  </>
                                ) : (
                                  <>
                                    <FileText className="mr-1 h-4 w-4" />
                                    View in OnlyOffice
                                  </>
                                )}
                              </Button>

                              {/* Download Button */}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  if (policy.attachments?.[0]) {
                                    const url =
                                      policy.attachments[0].url.startsWith(
                                        'http',
                                      )
                                        ? policy.attachments[0].url
                                        : `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}${policy.attachments[0].url}`;
                                    window.open(url, '_blank');
                                  }
                                }}
                                className="text-gray-600 hover:bg-gray-50 hover:text-gray-700"
                              >
                                <Download className="mr-1 h-4 w-4" />
                                Download
                              </Button>

                              {/* Replace Button - Only show if user can edit */}
                              {canEditPolicy() && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleRemoveDocument()}
                                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                                >
                                  <Trash2 className="mr-1 h-4 w-4" />
                                  Replace
                                </Button>
                              )}
                            </div>
                          </div>
                          <Separator />
                        </div>
                      )}

                    {/* No Document Warning */}
                    {(!policy.attachments ||
                      policy.attachments.length === 0) && (
                      <div className="rounded-lg border border-amber-200 bg-amber-50 p-3">
                        <p className="text-sm text-amber-800">
                          <strong>No Policy Document:</strong>{' '}
                          {policy.status === 'Request Initiated'
                            ? 'Policy is awaiting governance approval. Documents can be created after approval for drafting.'
                            : policy.status === 'Draft'
                              ? 'A policy document must be attached before this policy can be submitted for review.'
                              : 'No policy document has been attached to this policy.'}
                          {canCreateDocument() && (
                            <span>
                              {' '}
                              You can upload a document or create a new one
                              using the options below.
                            </span>
                          )}
                        </p>
                      </div>
                    )}

                    {/* Replace Mode Indicator */}
                    {showReplaceUpload && (
                      <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                        <p className="text-sm text-blue-800">
                          <strong>Replacing Document:</strong> The new document
                          will replace the current policy document.
                        </p>
                      </div>
                    )}

                    {/* File Upload Section - Show when user can edit and no document exists OR when replacing */}
                    {canEditPolicy() &&
                      (!policy.attachments ||
                        policy.attachments.length === 0 ||
                        showReplaceUpload) && (
                        <div className="space-y-3">
                          <Label className="text-sm font-medium text-gray-600">
                            {showReplaceUpload
                              ? 'Replace Policy Document'
                              : 'Upload Policy Document'}
                          </Label>
                          <p className="text-xs text-gray-500">
                            {showReplaceUpload
                              ? 'Select a new Word document (.doc, .docx) or PDF file to replace the current policy document. Maximum file size: 10MB.'
                              : 'Upload a Word document (.doc, .docx) or PDF file containing the policy details. Maximum file size: 10MB.'}
                          </p>

                          <div className="space-y-3">
                            {/* File Input */}
                            <div className="relative">
                              <input
                                ref={fileInputRef}
                                type="file"
                                accept=".doc,.docx,.pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf"
                                onChange={handleFileSelect}
                                className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                              />
                              <div className="flex items-center gap-3 rounded-lg border-2 border-dashed border-gray-300 p-3 transition-colors hover:border-blue-400">
                                <Upload className="h-5 w-5 text-gray-400" />
                                <div className="flex-1">
                                  {selectedFile ? (
                                    <div>
                                      <p className="text-sm font-medium text-gray-900">
                                        {selectedFile.name}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {formatFileSize(selectedFile.size)}
                                      </p>
                                    </div>
                                  ) : (
                                    <div>
                                      <p className="text-sm text-gray-600">
                                        {showReplaceUpload
                                          ? 'Click to browse or drag and drop the new policy document'
                                          : 'Click to browse or drag and drop your policy document'}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        Word documents (.doc, .docx) or PDF
                                        files only
                                      </p>
                                    </div>
                                  )}
                                </div>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={handleBrowseClick}
                                >
                                  Browse
                                </Button>
                              </div>
                            </div>

                            {/* Upload Button */}
                            {selectedFile && (
                              <div className="flex items-center gap-2">
                                <Button
                                  onClick={handleFileUpload}
                                  disabled={uploadLoading}
                                  className="flex items-center gap-2"
                                >
                                  {uploadLoading ? (
                                    <>
                                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                      Uploading...
                                    </>
                                  ) : (
                                    <>
                                      <Upload className="h-4 w-4" />
                                      {showReplaceUpload
                                        ? 'Replace Document'
                                        : 'Upload Policy Document'}
                                    </>
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  onClick={
                                    showReplaceUpload
                                      ? handleCancelReplace
                                      : () => {
                                          setSelectedFile(null);
                                          if (fileInputRef.current) {
                                            fileInputRef.current.value = '';
                                          }
                                        }
                                  }
                                  disabled={uploadLoading}
                                >
                                  Cancel
                                </Button>
                              </div>
                            )}

                            {/* Create New Document Option */}
                            {!selectedFile && canCreateDocument() && (
                              <div className="border-t border-gray-200 pt-4">
                                <div className="text-center">
                                  <p className="mb-3 text-sm text-gray-600">
                                    Or create a new document
                                  </p>
                                  <Button
                                    variant="outline"
                                    onClick={handleCreateDocument}
                                    className="w-full"
                                  >
                                    <FileText className="mr-2 h-4 w-4" />
                                    Create New Document
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                    {/* Info messages */}
                    {!canEditPolicy() && (
                      <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                        <p className="text-sm text-blue-800">
                          <strong>Note:</strong>{' '}
                          {policy.status === 'Request Initiated'
                            ? `Policy is awaiting governance approval. Documents can only be created after the policy is approved for drafting.`
                            : policy.status !== 'Draft'
                              ? `Policy documents can only be edited when the policy is in "Draft" status. Current status: ${policy.status}.`
                              : `You don't have permission to edit this policy. Only the policy owner, admins, and super admins can edit policies.`}
                        </p>
                      </div>
                    )}

                    {policy.status === 'Draft' &&
                      policy.attachments &&
                      policy.attachments.length > 0 &&
                      !showReplaceUpload && (
                        <div className="rounded-lg border border-amber-200 bg-amber-50 p-3">
                          <p className="text-sm text-amber-800">
                            <strong>Note:</strong> Only one policy document can
                            be attached. To upload a new document, click Replace
                            on the current document.
                          </p>
                        </div>
                      )}
                  </CardContent>
                </Card>

                {/* Workflow Actions */}
                <Card className="border-2 border-blue-200 bg-blue-50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      Workflow Actions
                    </CardTitle>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-700">
                        <strong>Current Status:</strong>{' '}
                        <StatusBadge status={policy.status} />
                        {policy.exceptionStatus &&
                          policy.exceptionStatus !== 'None' && (
                            <Badge
                              variant="outline"
                              className="ml-2 border-purple-200 bg-purple-50 text-purple-700"
                            >
                              Exception: {policy.exceptionStatus}
                            </Badge>
                          )}
                      </p>
                      <p className="text-sm text-gray-700">
                        <strong>Your Role:</strong>{' '}
                        <span className="rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                          {userRole}
                        </span>
                      </p>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {availableActions.length > 0 ? (
                      <>
                        {/* Retirement Request Form */}
                        {availableActions.some(
                          (action) => action.id === 'request-retirement',
                        ) && (
                          <div className="space-y-4 border-t border-gray-200 pt-4">
                            <h4 className="text-sm font-medium text-gray-700">
                              Retirement Request Details
                            </h4>
                            <div>
                              <Label
                                htmlFor="retirementJustification"
                                className="text-sm font-medium"
                              >
                                Justification for Retirement *
                              </Label>
                              <Textarea
                                id="retirementJustification"
                                placeholder="Provide clear justification for retiring this policy..."
                                value={retirementJustification}
                                onChange={(e) =>
                                  setRetirementJustification(e.target.value)
                                }
                                className="mt-1"
                                rows={3}
                                required
                              />
                            </div>
                            <div>
                              <Label
                                htmlFor="retirementEffectiveDate"
                                className="text-sm font-medium"
                              >
                                Effective Retirement Date (Optional)
                              </Label>
                              <Input
                                id="retirementEffectiveDate"
                                type="date"
                                value={retirementEffectiveDate}
                                onChange={(
                                  e: React.ChangeEvent<HTMLInputElement>,
                                ) => setRetirementEffectiveDate(e.target.value)}
                                className="mt-1"
                              />
                            </div>
                          </div>
                        )}

                        {/* Exception Request Form */}
                        {availableActions.some(
                          (action) => action.id === 'request-exception',
                        ) && (
                          <div className="space-y-4 border-t border-gray-200 pt-4">
                            <h4 className="text-sm font-medium text-gray-700">
                              Exception Request Details
                            </h4>
                            <div>
                              <Label
                                htmlFor="exceptionJustification"
                                className="text-sm font-medium"
                              >
                                Justification for Exception *
                              </Label>
                              <Textarea
                                id="exceptionJustification"
                                placeholder="Provide clear justification for requesting this policy exception..."
                                value={exceptionJustification}
                                onChange={(e) =>
                                  setExceptionJustification(e.target.value)
                                }
                                className="mt-1"
                                rows={3}
                                required
                              />
                            </div>
                            <div>
                              <Label
                                htmlFor="exceptionSpecificSection"
                                className="text-sm font-medium"
                              >
                                Specific Policy Section *
                              </Label>
                              <Input
                                id="exceptionSpecificSection"
                                placeholder="e.g., Section 3.2 - Data Retention Requirements"
                                value={exceptionSpecificSection}
                                onChange={(e) =>
                                  setExceptionSpecificSection(e.target.value)
                                }
                                className="mt-1"
                                required
                              />
                            </div>
                            <div>
                              <Label
                                htmlFor="exceptionType"
                                className="text-sm font-medium"
                              >
                                Exception Type *
                              </Label>
                              <select
                                id="exceptionType"
                                value={exceptionType}
                                onChange={(e) =>
                                  setExceptionType(
                                    e.target.value as
                                      | 'Material Exception'
                                      | 'Immaterial Exception',
                                  )
                                }
                                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                required
                              >
                                <option value="Immaterial Exception">
                                  Immaterial Exception (CEO Approval)
                                </option>
                                <option value="Material Exception">
                                  Material Exception (Board Approval)
                                </option>
                              </select>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label
                                  htmlFor="exceptionEffectiveDate"
                                  className="text-sm font-medium"
                                >
                                  Effective Date (Optional)
                                </Label>
                                <Input
                                  id="exceptionEffectiveDate"
                                  type="date"
                                  value={exceptionEffectiveDate}
                                  onChange={(e) =>
                                    setExceptionEffectiveDate(e.target.value)
                                  }
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label
                                  htmlFor="exceptionExpiryDate"
                                  className="text-sm font-medium"
                                >
                                  Expiry Date (Optional)
                                </Label>
                                <Input
                                  id="exceptionExpiryDate"
                                  type="date"
                                  value={exceptionExpiryDate}
                                  onChange={(e) =>
                                    setExceptionExpiryDate(e.target.value)
                                  }
                                  className="mt-1"
                                />
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Review Workflow Forms */}
                        {availableActions.some(
                          (action) => action.id === 'initiate-review',
                        ) && (
                          <div className="space-y-4 border-t border-gray-200 pt-4">
                            <h4 className="text-sm font-medium text-gray-700">
                              Review Details
                            </h4>
                            <div>
                              <Label
                                htmlFor="reviewType"
                                className="text-sm font-medium"
                              >
                                Review Type *
                              </Label>
                              <select
                                id="reviewType"
                                value={reviewType}
                                onChange={(e) => setReviewType(e.target.value)}
                                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                required
                              >
                                <option value="Scheduled Review">
                                  Scheduled Review
                                </option>
                                <option value="Ad-hoc Review">
                                  Ad-hoc Review
                                </option>
                                <option value="Compliance Review">
                                  Compliance Review
                                </option>
                              </select>
                            </div>
                          </div>
                        )}

                        {availableActions.some(
                          (action) => action.id === 'owner-review',
                        ) && (
                          <div className="space-y-4 border-t border-gray-200 pt-4">
                            <h4 className="text-sm font-medium text-gray-700">
                              Policy Owner Review
                            </h4>
                            <div>
                              <Label
                                htmlFor="ownerReviewDecision"
                                className="text-sm font-medium"
                              >
                                Review Decision *
                              </Label>
                              <select
                                id="ownerReviewDecision"
                                value={ownerReviewDecision}
                                onChange={(e) =>
                                  setOwnerReviewDecision(
                                    e.target.value as
                                      | 'No Updates Required'
                                      | 'Updates Required',
                                  )
                                }
                                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                required
                              >
                                <option value="No Updates Required">
                                  No Updates Required
                                </option>
                                <option value="Updates Required">
                                  Updates Required
                                </option>
                              </select>
                            </div>
                            {ownerReviewDecision === 'Updates Required' && (
                              <div>
                                <Label
                                  htmlFor="changesDescription"
                                  className="text-sm font-medium"
                                >
                                  Description of Required Changes *
                                </Label>
                                <Textarea
                                  id="changesDescription"
                                  placeholder="Describe what changes are needed to the policy..."
                                  value={changesDescription}
                                  onChange={(e) =>
                                    setChangesDescription(e.target.value)
                                  }
                                  className="mt-1"
                                  rows={3}
                                  required
                                />
                              </div>
                            )}
                          </div>
                        )}

                        <div>
                          <Label
                            htmlFor="comments"
                            className="text-sm font-medium"
                          >
                            Comments (Optional)
                          </Label>
                          <Textarea
                            id="comments"
                            placeholder="Add comments about this action..."
                            value={comments}
                            onChange={(e) => setComments(e.target.value)}
                            className="mt-1"
                            rows={3}
                          />
                        </div>

                        <div className="space-y-3">
                          <p className="text-sm font-medium text-gray-700">
                            Available Actions:
                          </p>
                          <div className="flex flex-wrap gap-3">
                            {availableActions.map((action) => {
                              const isDisabled =
                                actionLoading ||
                                loading ||
                                action.disabled ||
                                (action.id === 'request-retirement' &&
                                  !retirementJustification.trim()) ||
                                (action.id === 'request-exception' &&
                                  (!exceptionJustification.trim() ||
                                    !exceptionSpecificSection.trim())) ||
                                (action.id === 'owner-review' &&
                                  ownerReviewDecision === 'Updates Required' &&
                                  !changesDescription.trim());
                              const button = (
                                <Button
                                  key={action.id}
                                  variant={action.variant}
                                  onClick={() => handleAction(action.id)}
                                  disabled={isDisabled}
                                  className={`min-w-[160px] font-medium ${
                                    action.variant === 'default'
                                      ? 'bg-blue-600 hover:bg-blue-700'
                                      : ''
                                  } ${
                                    action.variant === 'destructive'
                                      ? 'bg-red-600 hover:bg-red-700'
                                      : ''
                                  } ${
                                    action.disabled
                                      ? 'cursor-not-allowed opacity-50'
                                      : ''
                                  }`}
                                  size="lg"
                                >
                                  {actionLoading ? (
                                    <div className="flex items-center gap-2">
                                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                      Processing...
                                    </div>
                                  ) : (
                                    action.label
                                  )}
                                </Button>
                              );

                              // Wrap with tooltip if action has tooltip text
                              if (action.tooltip) {
                                return (
                                  <div
                                    key={action.id}
                                    className="group relative"
                                  >
                                    {button}
                                    <div className="pointer-events-none absolute bottom-full left-1/2 z-50 mb-2 -translate-x-1/2 transform whitespace-nowrap rounded-lg bg-gray-900 px-3 py-2 text-sm text-white opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                                      {action.tooltip}
                                      <div className="absolute left-1/2 top-full -translate-x-1/2 transform border-4 border-transparent border-t-gray-900"></div>
                                    </div>
                                  </div>
                                );
                              }

                              return button;
                            })}
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="py-6 text-center">
                        <p className="font-medium text-gray-600">
                          No actions available
                        </p>
                        <p className="mt-1 text-sm text-gray-500">
                          You dont have permission to perform actions on this
                          policy in its current status.
                        </p>
                        <div className="mt-3 rounded-lg bg-gray-100 p-3">
                          <p className="text-xs text-gray-600">
                            <strong>Current Status:</strong> {policy.status}
                            {policy.exceptionStatus &&
                              policy.exceptionStatus !== 'None' && (
                                <>
                                  <br />
                                  <strong>Exception Status:</strong>{' '}
                                  {policy.exceptionStatus}
                                </>
                              )}
                            <br />
                            <strong>Your Role:</strong> {user?.role || userRole}
                            <br />
                            <strong>Policy Owner:</strong>{' '}
                            {policy.policyOwner?.name || 'Unknown'}
                            <br />
                            <strong>Can Edit:</strong>{' '}
                            {canEditPolicy() ? 'Yes' : 'No'}
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Policy Owner & Stakeholders */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Stakeholders</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Policy Owner
                        </Label>
                        <p className="text-sm">
                          {policy.policyOwner?.name} (
                          {policy.policyOwner?.email})
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Initiated By
                        </Label>
                        <p className="text-sm">
                          {policy.initiatedBy?.name} (
                          {policy.initiatedBy?.email})
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Approval Authority
                        </Label>
                        <p className="text-sm">
                          {policy.approvalAuthority || 'CEO'}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">
                          Priority Score
                        </Label>
                        <p className="text-sm">
                          {policy.priorityScore || 5}/10
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="exception" className="mt-6">
                <ExceptionDetails policy={policy} />
              </TabsContent>

              <TabsContent value="comments" className="mt-6">
                <CommentsAndHistory policy={policy} />
              </TabsContent>
            </Tabs>
          )}

          {/* <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={actionLoading}>
            Close
          </Button>
        </DialogFooter> */}

          {/* Close button */}
          <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>

      {/* Policy Publication Modal */}
      <PolicyPublicationModal
        isOpen={showPublicationModal}
        onClose={() => setShowPublicationModal(false)}
        policyName={policy.name}
        policyDepartment={policy.department}
        onSubmit={handlePublicationSubmit}
      />
    </Dialog>
  );
};
