{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.9", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@svgr/webpack": "^8.1.0", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "jqwidgets-scripts": "^20.0.0", "lucide-react": "^0.454.0", "next": "14.2.15", "prettier-plugin-tailwindcss": "^0.6.8", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-loader-spinner": "^6.1.6", "react-paginate": "^8.2.0", "react-query": "^3.39.3", "recharts": "^2.14.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/core-js": "^2.5.8", "@types/cors": "^2.8.17", "@types/lodash": "^4.17.7", "@types/node": "^20.16.1", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/react-virtualized": "^9.21.30", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "autoprefixer": "^10.4.17", "eslint": "^8", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.1.0", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}