'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Policy } from '@/lib/api';

interface CommentsAndHistoryProps {
  policy: Policy;
}

export const CommentsAndHistory: React.FC<CommentsAndHistoryProps> = ({
  policy,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Comments & History</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Workflow History */}
        {policy.workflowHistory && policy.workflowHistory.length > 0 && (
          <div>
            <Label className="mb-2 block text-sm font-medium text-gray-600">
              Workflow History
            </Label>
            <div className="max-h-60 space-y-3 overflow-y-auto">
              {policy.workflowHistory
                .sort(
                  (a, b) =>
                    new Date(b.timestamp).getTime() -
                    new Date(a.timestamp).getTime(),
                )
                .map((entry, index) => {
                  // Determine styling based on actor role and action
                  const isGovernanceReview =
                    entry.actor?.role === 'Governance Reviewer';
                  const isGrcReview = entry.actor?.role === 'GRC Reviewer';
                  const isApprovalAction =
                    entry.action === 'Approved' || entry.action === 'Rejected';

                  let borderColor = 'border-blue-200'; // default
                  let actionLabel = entry.action || 'Status Change';

                  if (isGovernanceReview) {
                    borderColor = 'border-purple-200';
                    actionLabel = `Governance Review - ${entry.action}`;
                  } else if (isGrcReview) {
                    borderColor = 'border-orange-200';
                    actionLabel = `GRC Review - ${entry.action}`;
                  }

                  return (
                    <div
                      key={index}
                      className={`border-l-2 ${borderColor} pb-3 pl-4`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="mb-1 flex items-center gap-2">
                            <span className="text-sm font-medium text-gray-900">
                              {actionLabel}
                            </span>
                            <span
                              className={`rounded-full px-2 py-1 text-xs ${
                                isApprovalAction && entry.action === 'Approved'
                                  ? 'bg-green-100 text-green-800'
                                  : isApprovalAction &&
                                      entry.action === 'Rejected'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-blue-100 text-blue-800'
                              }`}
                            >
                              {entry.status}
                            </span>
                          </div>
                          <div className="mb-2 text-xs text-gray-500">
                            By {entry.actor?.name || 'System'} (
                            {entry.actor?.role || 'Unknown'}) •{' '}
                            {new Date(entry.timestamp).toLocaleString()}
                          </div>
                          {entry.comments && (
                            <div className="rounded border-l-2 border-gray-300 bg-gray-50 p-2 text-sm text-gray-700">
                              <span className="text-xs font-medium text-gray-500">
                                Comment:
                              </span>
                              <p className="mt-1">{entry.comments}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Approval Workflow */}
        {policy.approvalWorkflow && policy.approvalWorkflow.length > 0 && (
          <div>
            <Label className="mb-2 block text-sm font-medium text-gray-600">
              Approval Workflow
            </Label>
            <div className="space-y-3">
              {policy.approvalWorkflow
                .sort(
                  (a, b) =>
                    (b.actionDate ? new Date(b.actionDate).getTime() : 0) -
                    (a.actionDate ? new Date(a.actionDate).getTime() : 0),
                )
                .map((approval, index) => (
                  <div
                    key={index}
                    className="border-l-2 border-green-200 pb-3 pl-4"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <span
                            className={`rounded-full px-2 py-1 text-xs ${
                              approval.status === 'Approved'
                                ? 'bg-green-100 text-green-800'
                                : approval.status === 'Rejected'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-yellow-100 text-yellow-800'
                            }`}
                          >
                            {approval.status}
                          </span>
                        </div>
                        <div className="mb-2 text-xs text-gray-500">
                          {approval.approver.email} •{' '}
                          {approval.actionDate
                            ? new Date(approval.actionDate).toLocaleString()
                            : 'Pending'}
                        </div>
                        {approval.comments && (
                          <div className="rounded border-l-2 border-gray-300 bg-gray-50 p-2 text-sm text-gray-700">
                            <span className="text-xs font-medium text-gray-500">
                              Comment:
                            </span>
                            <p className="mt-1">{approval.comments}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* No comments message */}
        {(!policy.workflowHistory || policy.workflowHistory.length === 0) &&
          (!policy.approvalWorkflow ||
            policy.approvalWorkflow.length === 0) && (
            <div className="py-6 text-center">
              <p className="text-sm text-gray-500">
                No comments or history available yet.
              </p>
            </div>
          )}
      </CardContent>
    </Card>
  );
};
