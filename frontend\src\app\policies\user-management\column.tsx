'use client';

import * as React from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export type User = {
  _id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  subDepartment?: string;
  position?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  reportingManager?: {
    _id: string;
    name: string;
  };
};

const columnHelper = createColumnHelper<User>();

const getRoleBadgeColor = (role: string) => {
  switch (role) {
    case 'Super Admin':
      return 'bg-red-500';
    case 'Admin':
      return 'bg-orange-500';
    case 'Approver':
      return 'bg-purple-500';
    case 'Reviewer':
      return 'bg-blue-500';
    case 'Creator':
      return 'bg-green-500';
    case 'Publisher':
      return 'bg-indigo-500';
    case 'Viewer':
      return 'bg-gray-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusBadgeColor = (isActive: boolean) => {
  return isActive ? 'bg-green-500' : 'bg-red-500';
};

export const columns = [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }),

  // Name column
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Name</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('name')}
      </div>
    ),
    enableHiding: false,
  }),

  // Email column
  columnHelper.accessor('email', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Email</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('email')}
      </div>
    ),
    enableHiding: false,
  }),

  // Role column
  columnHelper.accessor('role', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Role</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const role = row.getValue('role') as string;
      return (
        <Badge className={`text-white ${getRoleBadgeColor(role)}`}>
          {role}
        </Badge>
      );
    },
    enableHiding: false,
  }),

  // Department column
  columnHelper.accessor('department', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Department
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('department')}
      </div>
    ),
    enableHiding: false,
  }),

  // Position column
  columnHelper.accessor('position', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Position
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('position') || 'N/A'}
      </div>
    ),
    enableHiding: false,
  }),

  // Status column
  columnHelper.accessor('isActive', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Status</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const isActive = row.getValue('isActive') as boolean;
      return (
        <Badge className={`text-white ${getStatusBadgeColor(isActive)}`}>
          {isActive ? 'Active' : 'Inactive'}
        </Badge>
      );
    },
    enableHiding: false,
  }),

  // Last Login column
  columnHelper.accessor('lastLogin', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Last Login
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const lastLogin = row.getValue('lastLogin') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {lastLogin ? new Date(lastLogin).toLocaleDateString() : 'Never'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // Actions column
  columnHelper.display({
    id: 'actions',
    header: () => (
      <div className="text-right text-sm font-bold text-customBlueSecondary">
        Actions
      </div>
    ),
    cell: ({ row, table }) => {
      const user = row.original;
      const { onEdit, onProfile, onToggleStatus } =
        (table.options.meta as any) || {};

      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onProfile?.(user)}>
                View Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(user)}>
                Edit User
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onToggleStatus?.(user._id)}>
                {user.isActive ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false,
  }),
];
