const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const http = require('http');
const path = require('path');
require('dotenv').config();

const connectDB = require('./config/database');
const errorHandler = require('./middleware/errorHandler');
const authRoutes = require('./routes/auth');
const policyRoutes = require('./routes/policies');
const userRoutes = require('./routes/users');
const notificationRoutes = require('./routes/notifications');
const onlyofficeRoutes = require('./routes/onlyoffice');
const socketService = require('./services/socketService');
const NotificationService = require('./services/notificationService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5001;

// Trust proxy for proper IP detection behind Apache/reverse proxy
// Only trust the first proxy (Apache) for security
app.set('trust proxy', 1);
console.log('🔧 Trust proxy setting applied: 1 (fixed for rate limiting)');

// Connect to MongoDB
connectDB();

// Security middleware with custom CSP for iframe embedding
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "http://localhost:8080", "https://grc-v4.autoresilience.com"],
      imgSrc: ["'self'", "data:", "https:", "http://localhost:8080", "https://grc-v4.autoresilience.com"],
      fontSrc: ["'self'", "https:", "data:", "http://localhost:8080", "https://grc-v4.autoresilience.com"],
      connectSrc: ["'self'", "http://localhost:8080", "https://grc-v4.autoresilience.com", "wss://grc-v4.autoresilience.com"],
      frameSrc: ["'self'", "http://localhost:8080", "https://grc-v4.autoresilience.com"],
      frameAncestors: ["'self'", "http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:8080", "https://grc-v4.autoresilience.com"], // Allow iframe embedding from frontend
      workerSrc: ["'self'", "blob:", "http://localhost:8080", "https://grc-v4.autoresilience.com"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10000, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api/', limiter);

// CORS configuration - Allow all origins
app.use(cors({
  origin: '*',
  credentials: false
}));

// Upload routes (before body parser to handle multipart/form-data)
const uploadRoutes = require('./routes/uploads');
app.use('/api/uploads', uploadRoutes);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// Initialize Socket.IO
const io = socketService.initialize(server);

// Initialize notification service with Socket.IO
const notificationService = new NotificationService(io);

// Start scheduled notification tasks
notificationService.startScheduledTasks();

// Make Socket.IO and notification service available to routes
app.use((req, res, next) => {
  req.io = io;
  req.notificationService = notificationService;
  next();
});

// Serve uploaded files statically
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/policies', policyRoutes);
app.use('/api/users', userRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/onlyoffice', onlyofficeRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
  });
});

// Global error handler
app.use(errorHandler);

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`🔔 WebSocket server initialized`);
  console.log(`📡 Real-time notifications enabled`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

module.exports = app;
