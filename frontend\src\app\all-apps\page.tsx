'use client';
import React from 'react';
import Card from '@/app/all-apps/cards';
import cardData from '@/app/all-apps/carddata';

import Image from 'next/image';
import grclogo from '@/assests/images/grclogo.png';

import ascent from '@/assests/images/ascent.png';
import BellIcon from '@/assests/images/layout/bell.svg';

import AppDrawer from './AppDrawer';

import UserPopover from '@/components/UserPopover';

// const isActive = (path: string) => {
//   return pathname === path;
// };

const HomePage = () => {
  // const pathname = usePathname();

  return (
    <main>
      <nav className="fixed left-0 top-0 z-30 flex h-16 w-full items-center justify-between border-b bg-white font-bold">
        <div className="ml-4 flex items-center justify-center space-x-6">
          <Image src={grclogo} alt="grclogo" className="h-10 w-10" />
          <p className="font-roboto text-xl font-semibold text-customBlue">
            GRC
          </p>
        </div>
        <div className="mr-4 flex items-center space-x-4">
          <BellIcon />
          <AppDrawer />
          {/* <Layout /> */}
          <UserPopover />
          <Image src={ascent} alt="ascent" className="mx-6 h-8 w-24" />
        </div>
      </nav>
      <div className="mt-16 flex">
        <div className="flex min-h-screen-minus-navbar w-full flex-col bg-customCanvas">
          <div className="mx-8 my-4 min-h-screen-minus-all">
            <div className="min-h-screen bg-customCanvas py-8">
              <div className="container mx-auto px-4">
                {cardData.map((section) => (
                  <div key={section.category} className="mb-12">
                    <h2 className="mb-4 text-xl font-bold text-customBlue">
                      {section.category}
                    </h2>
                    <p className="mb-6 text-sm text-customGray">
                      {section.text}
                    </p>

                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                      {section.cards.map((card, index) => (
                        <Card
                          key={index}
                          title={card.title}
                          description={card.description}
                          icon={card.icon}
                          href={card.href}
                          tag={card.tag}
                          tagColor={card.tagColor}
                          buttonLabel={card.buttonLabel}
                          isOutline={card.isOutline}
                          onClick={() => alert(`${card.title} clicked!`)}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default HomePage;
