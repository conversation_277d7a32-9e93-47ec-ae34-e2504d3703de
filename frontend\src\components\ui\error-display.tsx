'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON><PERSON>, ArrowLeft, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorDisplayProps {
  title?: string;
  message?: string;
  error?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  showTechnicalDetails?: boolean;
  className?: string;
  variant?: 'default' | 'minimal' | 'inline';
}

export function ErrorDisplay({
  title = 'Something went wrong',
  message = 'We encountered an unexpected error. Please try again or contact support if the problem persists.',
  error,
  onRetry,
  onGoBack,
  showTechnicalDetails = true,
  className,
  variant = 'default',
}: ErrorDisplayProps) {
  if (variant === 'minimal') {
    return (
      <div className={cn('py-8 text-center', className)}>
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-50">
          <AlertTriangle className="h-6 w-6 text-red-500" />
        </div>
        <h3 className="mb-2 text-lg font-medium text-gray-900">{title}</h3>
        <p className="mb-4 text-gray-600">{message}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 font-medium text-white transition-colors duration-200 hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4" />
            Try Again
          </button>
        )}
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div
        className={cn(
          'rounded-lg border border-red-200 bg-red-50 p-4',
          className,
        )}
      >
        <div className="flex items-start gap-3">
          <AlertTriangle className="mt-0.5 h-5 w-5 flex-shrink-0 text-red-500" />
          <div className="flex-1">
            <h4 className="mb-1 text-sm font-medium text-red-800">{title}</h4>
            <p className="text-sm text-red-700">{message}</p>
            {error && showTechnicalDetails && (
              <details className="mt-2">
                <summary className="cursor-pointer text-xs text-red-600 hover:text-red-800">
                  Technical details
                </summary>
                <div className="mt-1 break-all rounded bg-red-100 p-2 font-mono text-xs text-red-800">
                  {error}
                </div>
              </details>
            )}
            {onRetry && (
              <button
                onClick={onRetry}
                className="mt-3 rounded bg-red-600 px-3 py-1.5 text-sm font-medium text-white transition-colors duration-200 hover:bg-red-700"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default full-screen variant
  return (
    <div
      className={cn(
        'flex min-h-full items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-6',
        className,
      )}
    >
      <div className="w-full max-w-md">
        {/* Error Card */}
        <div className="rounded-2xl border border-gray-200 bg-white p-8 text-center shadow-xl">
          {/* Error Icon */}
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-50">
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>

          {/* Error Title */}
          <h2 className="mb-3 text-xl font-semibold text-gray-900">{title}</h2>

          {/* Error Message */}
          <p className="mb-6 leading-relaxed text-gray-600">{message}</p>

          {/* Error Details (Collapsible) */}
          {error && showTechnicalDetails && (
            <details className="mb-6 text-left">
              <summary className="flex cursor-pointer items-center justify-center gap-1 text-sm text-gray-500 transition-colors hover:text-gray-700">
                <HelpCircle className="h-4 w-4" />
                View technical details
              </summary>
              <div className="mt-2 break-all rounded-lg bg-gray-50 p-3 font-mono text-xs text-gray-600">
                {error}
              </div>
            </details>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 sm:flex-row">
            {onRetry && (
              <button
                onClick={onRetry}
                className="flex flex-1 items-center justify-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors duration-200 hover:bg-blue-700"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again
              </button>
            )}
            {onGoBack && (
              <button
                onClick={onGoBack}
                className="flex flex-1 items-center justify-center gap-2 rounded-lg bg-gray-100 px-6 py-3 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-200"
              >
                <ArrowLeft className="h-4 w-4" />
                Go Back
              </button>
            )}
          </div>
        </div>

        {/* Help Text */}
        <p className="mt-6 text-center text-sm text-gray-500">
          Need help? Contact our{' '}
          <a href="#" className="text-blue-600 underline hover:text-blue-700">
            support team
          </a>
        </p>
      </div>
    </div>
  );
}

export default ErrorDisplay;
