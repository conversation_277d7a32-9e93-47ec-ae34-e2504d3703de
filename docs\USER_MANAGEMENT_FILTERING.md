# User Management Filtering Implementation

## Overview

The user management system implements a **dual-layer filtering architecture** that combines DataTable-level filtering with backend server-side filtering for optimal performance and user experience.

## Architecture Components

### 1. Backend Server-Side Filtering

**Location**: [`backend/src/controllers/userController.js`](../backend/src/controllers/userController.js)

#### Supported Query Parameters:
- `role` - Filter by user role (C<PERSON>, Reviewer, Approver, etc.)
- `department` - Filter by department (PMO, Development, Legal, etc.)
- `isActive` - Filter by active status (true/false)
- `search` - Text search across multiple fields
- `sortBy` - Sort field (name, email, role, department, createdAt, lastLogin, isActive)
- `sortOrder` - Sort direction (asc/desc)
- `page` - Page number for pagination
- `limit` - Items per page (max 100)
- `position` - Filter by job position
- `subDepartment` - Filter by sub-department

#### Advanced Features:
```javascript
// Multi-field text search
if (search && search.trim()) {
  const searchRegex = { $regex: search.trim(), $options: 'i' };
  query.$or = [
    { name: searchRegex },
    { email: searchRegex },
    { position: searchRegex },
    { subDepartment: searchRegex },
    { phoneNumber: searchRegex },
  ];
}

// Dynamic sorting with validation
const validSortFields = ['name', 'email', 'role', 'department', 'createdAt', 'lastLogin', 'isActive'];
const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
```

#### Performance Optimizations:
- **Lean queries** for better performance
- **Population** of related fields (reportingManager)
- **Pagination limits** (max 100 items per page)
- **Index-optimized** MongoDB queries

### 2. Frontend DataTable Filtering

**Location**: [`frontend/src/app/policies/user-management/datatable.tsx`](../frontend/src/app/policies/user-management/datatable.tsx)

#### TanStack Table Features:
- **Column-specific filtering** using `columnFilters` state
- **Built-in search** for individual columns
- **Column visibility controls** via dropdown menu
- **Client-side sorting** for immediate feedback
- **Server-side pagination** support

#### Configuration:
```typescript
const table = useReactTable({
  data,
  columns,
  onSortingChange: setSorting,
  onColumnFiltersChange: setColumnFilters,
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  // Server-side pagination when available
  ...(pagination && {
    manualPagination: true,
    pageCount: pagination.totalPages,
  }),
});
```

### 3. Debounced Search Hook

**Location**: [`frontend/src/hooks/useDebounce.ts`](../frontend/src/hooks/useDebounce.ts)

Prevents excessive API calls during user typing:

```typescript
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
```

### 4. Enhanced User Management Page

**Location**: [`frontend/src/app/policies/user-management/page.tsx`](../frontend/src/app/policies/user-management/page.tsx)

#### Filter State Management:
```typescript
const [filters, setFilters] = useState({
  role: 'all',
  department: 'all',
  isActive: 'all',
  search: '',
  page: 1,
  limit: 10,
});

// Debounce search to avoid excessive API calls
const debouncedSearch = useDebounce(filters.search, 500);
```

#### Filter Handlers:
```typescript
const handleFilterChange = (key: string, value: string) => {
  setFilters(prev => ({
    ...prev,
    [key]: value,
    page: 1, // Reset to first page when filters change
  }));
};

const handleSearchChange = (value: string) => {
  setFilters(prev => ({
    ...prev,
    search: value,
    page: 1, // Reset to first page when search changes
  }));
};
```

## Filter Flow Architecture

```mermaid
graph TD
    A[User Input] --> B{Filter Type}
    
    B -->|Search Text| C[Debounced Search Hook]
    B -->|Dropdown Selection| D[Immediate Filter Update]
    B -->|DataTable Column| E[TanStack Table Filter]
    
    C --> F[API Call with Query Parameters]
    D --> F
    E --> G[Client-side Filtering]
    
    F --> H[MongoDB Query Execution]
    H --> I[Server-side Filtered Results]
    I --> J[API Response with Pagination]
    
    G --> K[DataTable Re-render]
    J --> K
    
    K --> L[Updated UI Display]
```

## API Endpoints

### GET /api/users

**Query Parameters:**
```
GET /api/users?role=Admin&department=IT&isActive=true&search=john&page=1&limit=10&sortBy=name&sortOrder=asc
```

**Response Format:**
```json
{
  "success": true,
  "data": {
    "users": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "filters": {
      "role": "Admin",
      "department": "IT",
      "isActive": "true",
      "search": "john",
      "sortBy": "name",
      "sortOrder": "asc"
    }
  }
}
```

## Performance Benefits

### 1. **Reduced Network Traffic**
- Server-side filtering reduces payload size
- Debounced search prevents excessive API calls
- Pagination limits data transfer

### 2. **Improved Database Performance**
- Indexed MongoDB queries
- Lean queries for faster execution
- Optimized aggregation pipelines

### 3. **Enhanced User Experience**
- Real-time search feedback (debounced)
- Immediate filter application
- Responsive pagination controls
- Column visibility management

### 4. **Scalability**
- Handles large datasets efficiently
- Server-side processing reduces client load
- Configurable page sizes

## Usage Examples

### Basic Filtering
```typescript
// Filter by role and department
const filters = {
  role: 'Admin',
  department: 'IT',
  page: 1,
  limit: 20
};

fetchUsers(filters);
```

### Advanced Search
```typescript
// Multi-field search with sorting
const filters = {
  search: 'john doe',
  sortBy: 'lastLogin',
  sortOrder: 'desc',
  isActive: true
};

fetchUsers(filters);
```

### DataTable Column Filtering
```typescript
// Filter specific column in DataTable
table.getColumn('name')?.setFilterValue('john');
```

## Best Practices

1. **Always reset page to 1** when applying new filters
2. **Use debounced search** for text inputs to avoid excessive API calls
3. **Validate sort fields** on the backend to prevent injection attacks
4. **Limit maximum page size** to prevent performance issues
5. **Use lean queries** for better MongoDB performance
6. **Implement proper error handling** for failed filter operations

## Future Enhancements

1. **Advanced Filter UI** - Date range pickers, multi-select dropdowns
2. **Saved Filter Presets** - Allow users to save and reuse filter combinations
3. **Export Functionality** - Export filtered results to CSV/Excel
4. **Real-time Updates** - WebSocket integration for live data updates
5. **Filter Analytics** - Track most used filters for UX improvements