'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X, FileX, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Policy } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface PolicyRetirementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (retirementData: {
    policyId: string;
    justification: string;
    effectiveDate?: string;
    comments?: string;
  }) => Promise<void>;
  loading?: boolean;
  publishedPolicies: Policy[];
}

export const PolicyRetirementModal: React.FC<PolicyRetirementModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  loading = false,
  publishedPolicies,
}) => {
  const [selectedPolicyId, setSelectedPolicyId] = useState('');
  const [justification, setJustification] = useState('');
  const [effectiveDate, setEffectiveDate] = useState('');
  const [comments, setComments] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const { toast } = useToast();

  // Get selected policy details
  const selectedPolicy = publishedPolicies.find(
    (p) => p._id === selectedPolicyId,
  );

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      setSelectedPolicyId('');
      setJustification('');
      setEffectiveDate('');
      setComments('');
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPolicyId) {
      toast({
        title: 'Policy Required',
        description: 'Please select a policy to retire',
        variant: 'destructive',
      });
      return;
    }

    if (!justification.trim()) {
      toast({
        title: 'Justification Required',
        description: 'Please provide a justification for retiring this policy',
        variant: 'destructive',
      });
      return;
    }

    setSubmitting(true);
    try {
      await onSubmit({
        policyId: selectedPolicyId,
        justification: justification.trim(),
        effectiveDate: effectiveDate || undefined,
        comments: comments.trim() || undefined,
      });
    } catch (error) {
      console.error('Error submitting retirement request:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!submitting) {
      onClose();
    }
  };

  // Get minimum date (today)
  const today = new Date().toISOString().split('T')[0];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPrimitive.Portal>
        {/* Transparent overlay */}
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black opacity-40" />
        {/* Custom content without default overlay */}
        <DialogPrimitive.Content
          className={cn(
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid max-h-[90vh] w-full max-w-2xl translate-x-[-50%] translate-y-[-50%] gap-4 overflow-y-auto border bg-background p-6 shadow-lg duration-200 sm:rounded-lg',
          )}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileX className="h-5 w-5 text-orange-600" />
              Request Policy Retirement
            </DialogTitle>
            <DialogDescription>
              Submit a request to retire a published policy. This will initiate
              the retirement workflow process.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Warning Card */}
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="pt-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="mt-0.5 h-5 w-5 text-orange-600" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-orange-800">
                      Important Notice
                    </p>
                    <p className="text-sm text-orange-700">
                      Policy retirement is a formal process that requires
                      governance approval and final authorization. Once retired,
                      the policy will no longer be active and cannot be easily
                      restored.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Policy Selection */}
            <div className="space-y-2">
              <Label htmlFor="policy-select" className="text-sm font-medium">
                Select Policy to Retire *
              </Label>
              <Select
                value={selectedPolicyId}
                onValueChange={setSelectedPolicyId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a published policy..." />
                </SelectTrigger>
                <SelectContent>
                  {publishedPolicies.length === 0 ? (
                    <SelectItem value="" disabled>
                      No published policies available
                    </SelectItem>
                  ) : (
                    publishedPolicies.map((policy) => (
                      <SelectItem key={policy._id} value={policy._id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{policy.name}</span>
                          <span className="text-xs text-gray-500">
                            {policy.policyId} • {policy.department} • v
                            {policy.version || '1.0'}
                          </span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {selectedPolicy && (
                <div className="mt-2 rounded-lg border border-gray-200 bg-gray-50 p-3">
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="font-medium text-gray-600">
                        Policy Type:
                      </span>
                      <p className="text-gray-800">
                        {selectedPolicy.policyType || 'Operational'}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Owner:</span>
                      <p className="text-gray-800">
                        {selectedPolicy.policyOwner?.name}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">
                        Published:
                      </span>
                      <p className="text-gray-800">
                        {selectedPolicy.publishedDate
                          ? new Date(
                              selectedPolicy.publishedDate,
                            ).toLocaleDateString()
                          : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">
                        Classification:
                      </span>
                      <p className="text-gray-800">
                        {selectedPolicy.classification || 'Internal'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Justification */}
            <div className="space-y-2">
              <Label htmlFor="justification" className="text-sm font-medium">
                Justification for Retirement *
              </Label>
              <Textarea
                id="justification"
                placeholder="Provide a clear and detailed justification for why this policy should be retired. Include reasons such as policy obsolescence, regulatory changes, business process changes, etc."
                value={justification}
                onChange={(e) => setJustification(e.target.value)}
                rows={4}
                className="resize-none"
                required
              />
              <p className="text-xs text-gray-500">
                This justification will be reviewed by the governance team and
                included in the retirement documentation.
              </p>
            </div>

            {/* Effective Date */}
            <div className="space-y-2">
              <Label htmlFor="effective-date" className="text-sm font-medium">
                Proposed Effective Retirement Date (Optional)
              </Label>
              <Input
                id="effective-date"
                type="date"
                value={effectiveDate}
                onChange={(e) => setEffectiveDate(e.target.value)}
                min={today}
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                If not specified, the retirement will be effective immediately
                upon final approval.
              </p>
            </div>

            {/* Additional Comments */}
            <div className="space-y-2">
              <Label htmlFor="comments" className="text-sm font-medium">
                Additional Comments (Optional)
              </Label>
              <Textarea
                id="comments"
                placeholder="Add any additional context, transition plans, or special considerations for this retirement request..."
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>

            {/* Retirement Process Info */}
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-blue-800">
                  Retirement Process Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2 text-sm text-blue-700">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                    <span>
                      <strong>Step 1:</strong> Retirement request submitted for
                      governance review
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                    <span>
                      <strong>Step 2:</strong> Governance team reviews and
                      approves/rejects request
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                    <span>
                      <strong>Step 3:</strong> Final approval by appropriate
                      authority (CEO/Board)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-400"></div>
                    <span>
                      <strong>Complete:</strong> Policy marked as retired and
                      archived
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={submitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  submitting ||
                  loading ||
                  !selectedPolicyId ||
                  !justification.trim()
                }
                className="bg-orange-600 hover:bg-orange-700"
              >
                {submitting ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Submitting Request...
                  </div>
                ) : (
                  <>
                    <FileX className="mr-2 h-4 w-4" />
                    Submit Retirement Request
                  </>
                )}
              </Button>
            </div>
          </form>

          {/* Close button */}
          <DialogPrimitive.Close
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            disabled={submitting}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
  );
};
