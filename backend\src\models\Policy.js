const mongoose = require('mongoose');

const policySchema = new mongoose.Schema({
  policyId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  name: {
    type: String,
    required: [true, 'Policy name is required'],
    trim: true,
    maxlength: [200, 'Policy name cannot exceed 200 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
  },
  // Document Information
  documentName: {
    type: String,
    trim: true,
    maxlength: [200, 'Document name cannot exceed 200 characters'],
  },
  documentCode: {
    type: String,
    trim: true,
    unique: true,
    sparse: true,
    maxlength: [50, 'Document code cannot exceed 50 characters'],
  },
  documentType: {
    type: String,
    enum: ['Policy', 'SoW', 'Framework', 'Procedure', 'Guideline', 'Standard'],
    default: 'Policy',
  },
  version: {
    type: String,
    default: '1.0',
    trim: true,
  },
  versionNumber: {
    type: Number,
    default: 1.0,
    min: [0.1, 'Version number must be at least 0.1'],
  },

  // Department Information
  department: {
    type: String,
    required: [true, 'Department is required'],
    enum: [
      'HR', 'IT', 'Finance', 'Legal', 'Operations', 'Security', 'Compliance',
      'PMO Advisory Investment Activation', 'Development', 'Procurement', 'Marketing',
      'Golf Excellence', 'Risk Management', 'Sales & Sponsorships', 'Events',
      'Federation Office', 'Academies', 'Governance', 'Business Continuity Management',
      'Events Management', 'Local Golf', 'Tournaments', 'Internal Audit', 'Cybersecurity',
      'Admin & HSSE', 'CEO Office', 'Strategy', 'Golf Operations'
    ],
  },
  subDepartment: {
    type: String,
    trim: true,
    maxlength: [100, 'Sub-department cannot exceed 100 characters'],
  },

  // Policy Classification
  policyCategory: {
    type: String,
    enum: ['Corporate Policies', 'Operational Policies'],
    required: [true, 'Policy category is required for approval routing'],
  },
  policyType: {
    type: String,
    enum: ['Corporate', 'Operational', 'Technical', 'Administrative', 'Strategic'],
    default: 'Corporate',
  },
  categories: [{
    type: String,
    enum: ['Organizational', 'Compliance', 'Corporate', 'Security', 'HR', 'IT', 'Finance'],
    required: true,
  }],
  classification: {
    type: String,
    enum: ['Public', 'Internal', 'Confidential', 'Restricted', 'Top Secret'],
    default: 'Internal',
  },

  // Approval Authority based on Policy Category
  approvalAuthority: {
    type: String,
    enum: ['Board Committee', 'CEO', 'Department Head'],
    required: true,
  },

  // Priority and Scoring
  priorityScore: {
    type: Number,
    min: [1, 'Priority score must be at least 1'],
    max: [10, 'Priority score cannot exceed 10'],
    default: 5,
  },
  // Ownership and Responsibility
  policyOwner: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
  },
  initiatedBy: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    department: {
      type: String,
    },
  },
  reviewedBy: [{
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    reviewDate: {
      type: Date,
    },
    comments: {
      type: String,
      trim: true,
    },
  }],
  endorsedBy: [{
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    endorsementDate: {
      type: Date,
    },
    comments: {
      type: String,
      trim: true,
    },
  }],
  authorizedApprover: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    role: {
      type: String,
    },
  },

  // Governance Review
  governanceReview: {
    reviewer: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    reviewDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // GRC Review
  grcReview: {
    reviewer: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    reviewDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // Status Information
  status: {
    type: String,
    enum: [
      'Request Initiated',
      'Draft',
      'Under Review',
      'Pending Approval',
      'Approved',
      'Published',
      'Archived',
      'Retirement Requested',
      'Retirement Pending Approval',
      'Retired'
    ],
    default: 'Request Initiated',
  },
  detailedStatus: {
    type: String,
    trim: true,
    maxlength: [200, 'Detailed status cannot exceed 200 characters'],
  },


  // Workflow Tracking
  workflowHistory: [{
    status: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    actor: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
      role: String,
    },
    comments: {
      type: String,
      trim: true,
    },
    action: {
      type: String,
      enum: ['Created', 'Submitted', 'Approved', 'Rejected', 'Published', 'Archived', 'Retirement Requested', 'Retirement Approved', 'Retirement Rejected', 'Retired', 'Exception Requested', 'Exception Approved', 'Exception Rejected', 'Review Initiated', 'Owner Review Completed', 'Review Governance Approved', 'Review Governance Rejected', 'Review Approved', 'Review Rejected'],
    },
  }],


  // Retirement Workflow
  retirementRequest: {
    requestedBy: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    requestDate: Date,
    justification: {
      type: String,
      trim: true,
      maxlength: [1000, 'Retirement justification cannot exceed 1000 characters'],
    },
    effectiveDate: Date,
  },

  // Retirement Governance Review
  retirementGovernanceReview: {
    reviewer: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    reviewDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // Retirement Dates
  retirementRequestedDate: {
    type: Date,
  },
  retiredDate: {
    type: Date,
  },

  // Exception Workflow - Separate from main policy status
  exceptionStatus: {
    type: String,
    enum: [
      'None',
      'Requested',
      'Under Review',
      'Pending Approval',
      'Approved',
      'Rejected',
      'Expired'
    ],
    default: 'None',
  },
  
  exceptionRequest: {
    requestedBy: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    requestDate: Date,
    justification: {
      type: String,
      trim: true,
      maxlength: [1000, 'Exception justification cannot exceed 1000 characters'],
    },
    specificSection: {
      type: String,
      trim: true,
      maxlength: [500, 'Specific section cannot exceed 500 characters'],
    },
    exceptionType: {
      type: String,
      enum: ['Material Exception', 'Immaterial Exception'],
      required: function() {
        return this.exceptionStatus && this.exceptionStatus !== 'None';
      }
    },
    effectiveDate: Date,
    expiryDate: Date,
    comments: {
      type: String,
      trim: true,
      maxlength: [1000, 'Exception comments cannot exceed 1000 characters'],
    },
  },

  // Exception Governance Review
  exceptionGovernanceReview: {
    reviewer: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    reviewDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // Exception Approval
  exceptionApproval: {
    approver: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    approvalDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // Exception Dates
  exceptionRequestedDate: {
    type: Date,
  },
  exceptionApprovedDate: {
    type: Date,
  },
  exceptionRejectedDate: {
    type: Date,
  },

  // Review Workflow - Separate from main policy status
  reviewStatus: {
    type: String,
    enum: [
      'None',
      'Due',
      'In Progress',
      'Under Review',
      'Pending Approval',
      'Approved',
      'Rejected'
    ],
    default: 'None',
  },
  
  reviewRequest: {
    initiatedBy: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    initiatedDate: Date,
    reviewType: {
      type: String,
      enum: ['Scheduled Review', 'Ad-hoc Review', 'Compliance Review'],
      default: 'Scheduled Review'
    },
    comments: {
      type: String,
      trim: true,
      maxlength: [1000, 'Review comments cannot exceed 1000 characters'],
    },
  },

  // Review by Policy Owner
  ownerReview: {
    reviewer: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    reviewDate: Date,
    decision: {
      type: String,
      enum: ['No Updates Required', 'Updates Required'],
    },
    updatesRequired: {
      type: Boolean,
      default: false,
    },
    comments: String,
    changesDescription: {
      type: String,
      trim: true,
      maxlength: [2000, 'Changes description cannot exceed 2000 characters'],
    },
  },

  // Review Governance Review
  reviewGovernanceReview: {
    reviewer: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    reviewDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // Review Approval (Board/CEO based on policy category)
  reviewApproval: {
    approver: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    approvalDate: Date,
    decision: {
      type: String,
      enum: ['Approved', 'Rejected'],
    },
    comments: String,
  },

  // Review Dates
  reviewInitiatedDate: {
    type: Date,
  },
  reviewCompletedDate: {
    type: Date,
  },
  previousReviewDate: {
    type: Date,
  },

  content: {
    type: String,
    default: '',
  },
  attachments: [{
    filename: {
      type: String,
      required: true,
    },
    originalName: {
      type: String,
      required: true,
    },
    url: {
      type: String,
      required: true,
    },
    size: {
      type: Number,
      required: true,
    },
    mimeType: {
      type: String,
      required: true,
    },
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  versionHistory: [{
    version: {
      type: String,
      required: true,
    },
    modifiedBy: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
    },
    modifiedAt: {
      type: Date,
      default: Date.now,
    },
    size: Number,
    action: {
      type: String,
      enum: ['created', 'modified', 'renamed'],
    },
  }],
  approvalWorkflow: [{
    step: {
      type: Number,
      required: true,
    },
    approver: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      email: {
        type: String,
        required: true,
      },
    },
    status: {
      type: String,
      enum: ['Pending', 'Approved', 'Rejected'],
      default: 'Pending',
    },
    comments: {
      type: String,
      trim: true,
    },
    actionDate: {
      type: Date,
    },
  }],
  // Date Management
  startDate: {
    type: Date,
    default: Date.now,
  },
  dueDate: {
    type: Date,
  },
  completionDate: {
    type: Date,
  },
  approvalDate: {
    type: Date,
  },
  lastReviewDate: {
    type: Date,
  },
  nextReviewDate: {
    type: Date,
  },
  effectiveDate: {
    type: Date,
  },
  publishedDate: {
    type: Date,
  },
  archivedDate: {
    type: Date,
  },

  // Publication Settings
  publicationSettings: {
    targetAudience: {
      type: String,
      enum: ['All Employees', 'Selected Groups', 'Department Only'],
      default: 'All Employees',
    },
    selectedGroups: [{
      type: String,
    }],
    publishedBy: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      name: String,
      email: String,
    },
    isPublished: {
      type: Boolean,
      default: false,
    },
  },

  // Acknowledgement Tracking
  acknowledgements: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    userName: String,
    userEmail: String,
    viewedDate: {
      type: Date,
      default: Date.now,
    },
    acknowledgedDate: Date,
    isAcknowledged: {
      type: Boolean,
      default: false,
    },
    ipAddress: String,
    userAgent: String,
  }],

  // Legacy metadata for backward compatibility
  metadata: {
    tags: [{
      type: String,
      trim: true,
    }],
    priority: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Critical'],
      default: 'Medium',
    },
    dueDate: {
      type: Date,
    },
    effectiveDate: {
      type: Date,
    },
    reviewDate: {
      type: Date,
    },
  },

  // Timestamps
  requestedAt: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for better query performance
policySchema.index({ policyId: 1 });
policySchema.index({ documentCode: 1 });
policySchema.index({ status: 1 });
policySchema.index({ detailedStatus: 1 });
policySchema.index({ department: 1 });
policySchema.index({ subDepartment: 1 });
policySchema.index({ policyType: 1 });
policySchema.index({ documentType: 1 });
policySchema.index({ classification: 1 });
policySchema.index({ priorityScore: 1 });
policySchema.index({ 'policyOwner.id': 1 });
policySchema.index({ 'initiatedBy.id': 1 });
policySchema.index({ createdAt: -1 });
policySchema.index({ dueDate: 1 });
policySchema.index({ nextReviewDate: 1 });

// Virtual for formatted policy ID
policySchema.virtual('formattedPolicyId').get(function() {
  return this.policyId ? this.policyId.toUpperCase() : '';
});

// Pre-save middleware to update the updatedAt field
policySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to generate policy ID
policySchema.statics.generatePolicyId = function(department, categories, documentType = 'Policy') {
  const deptCode = department.substring(0, 3).toUpperCase();
  const catCode = categories[0] ? categories[0].substring(0, 3).toUpperCase() : 'GEN';
  const docTypeCode = documentType.substring(0, 3).toUpperCase();
  const timestamp = Date.now().toString().slice(-6);
  return `${deptCode}_${catCode}_${docTypeCode}_${timestamp}`;
};

// Static method to generate document code
policySchema.statics.generateDocumentCode = function(department, documentType, sequence) {
  const deptCode = department.substring(0, 3).toUpperCase();
  const docTypeCode = documentType.substring(0, 3).toUpperCase();
  const year = new Date().getFullYear();
  const seqNum = String(sequence).padStart(4, '0');
  return `${deptCode}-${docTypeCode}-${year}-${seqNum}`;
};

// Instance method to check if policy can be edited
policySchema.methods.canBeEdited = function() {
  return this.status === 'Request Initiated';
};

// Instance method to get approval authority based on policy category
policySchema.methods.getApprovalAuthority = function() {
  if (this.policyCategory === 'Corporate Policies') {
    return 'Board Committee';
  } else if (this.policyCategory === 'Operational Policies') {
    return 'CEO';
  }
  return 'Department Head';
};

// Instance method to check if policy needs governance review
policySchema.methods.needsGovernanceReview = function() {
  return this.status === 'Request Initiated' && !this.governanceReview?.decision;
};

// Instance method to check if policy can be submitted for review
policySchema.methods.canBeSubmittedForReview = function() {
  return this.status === 'Draft';
};

// Instance method to check if policy needs GRC review
policySchema.methods.needsGrcReview = function() {
  return this.status === 'Under Review' && !this.grcReview?.decision;
};

// Instance method to get acknowledgement rate
policySchema.methods.getAcknowledgementRate = function() {
  if (!this.acknowledgements || this.acknowledgements.length === 0) return 0;
  const acknowledged = this.acknowledgements.filter(ack => ack.isAcknowledged).length;
  return (acknowledged / this.acknowledgements.length) * 100;
};

// Instance method to get next version number
policySchema.methods.getNextVersion = function() {
  return Math.round((this.versionNumber + 0.1) * 10) / 10;
};

// Instance method to get next version string
policySchema.methods.getNextVersionString = function() {
  const nextVersion = this.getNextVersion();
  return nextVersion.toFixed(1);
};

// Instance method to check if policy is overdue
policySchema.methods.isOverdue = function() {
  return this.dueDate && new Date() > this.dueDate;
};

// Instance method to check if policy needs review
policySchema.methods.needsReview = function() {
  return this.nextReviewDate && new Date() > this.nextReviewDate;
};

// Instance method to get priority level based on score
policySchema.methods.getPriorityLevel = function() {
  if (this.priorityScore >= 9) return 'Critical';
  if (this.priorityScore >= 7) return 'High';
  if (this.priorityScore >= 4) return 'Medium';
  return 'Low';
};

// Instance method to check if policy can be retired
policySchema.methods.canBeRetired = function() {
  return this.status === 'Published';
};

// Instance method to check if policy needs retirement governance review
policySchema.methods.needsRetirementGovernanceReview = function() {
  return this.status === 'Retirement Requested' && !this.retirementGovernanceReview?.decision;
};

// Instance method to check if policy can be approved for retirement
policySchema.methods.canBeApprovedForRetirement = function() {
  return this.status === 'Retirement Pending Approval';
};

// Instance method to check if policy can have exception requested
policySchema.methods.canHaveExceptionRequested = function() {
  return this.status === 'Published';
};

// Instance method to check if policy needs exception governance review
policySchema.methods.needsExceptionGovernanceReview = function() {
  return this.exceptionStatus === 'Requested' && !this.exceptionGovernanceReview?.decision;
};

// Instance method to check if policy can be approved for exception
policySchema.methods.canBeApprovedForException = function() {
  return this.exceptionStatus === 'Pending Approval';
};

// Instance method to check if policy has active exception
policySchema.methods.hasActiveException = function() {
  return this.exceptionStatus === 'Approved' &&
         this.exceptionRequest?.expiryDate &&
         new Date() < new Date(this.exceptionRequest.expiryDate);
};

// Instance method to check if exception is expired
policySchema.methods.isExceptionExpired = function() {
  return this.exceptionStatus === 'Approved' &&
         this.exceptionRequest?.expiryDate &&
         new Date() >= new Date(this.exceptionRequest.expiryDate);
};

// Instance method to get exception approval authority based on exception type
policySchema.methods.getExceptionApprovalAuthority = function() {
  if (this.exceptionRequest?.exceptionType === 'Material Exception') {
    return 'Board Committee';
  } else if (this.exceptionRequest?.exceptionType === 'Immaterial Exception') {
    return 'CEO';
  }
  return 'Department Head';
};

// Review workflow instance methods

// Instance method to check if policy can have review initiated
policySchema.methods.canHaveReviewInitiated = function() {
  return this.status === 'Published' && this.reviewStatus === 'None';
};

// Instance method to check if policy needs review based on nextReviewDate
policySchema.methods.needsScheduledReview = function() {
  return this.nextReviewDate && new Date() >= new Date(this.nextReviewDate) && this.reviewStatus === 'None';
};

// Instance method to check if policy needs owner review
policySchema.methods.needsOwnerReview = function() {
  return this.reviewStatus === 'In Progress' && !this.ownerReview?.decision;
};

// Instance method to check if policy needs review governance review
policySchema.methods.needsReviewGovernanceReview = function() {
  return this.reviewStatus === 'Under Review' && !this.reviewGovernanceReview?.decision;
};

// Instance method to check if policy can be approved for review
policySchema.methods.canBeApprovedForReview = function() {
  return this.reviewStatus === 'Pending Approval';
};

// Instance method to get review approval authority based on policy category
policySchema.methods.getReviewApprovalAuthority = function() {
  if (this.policyCategory === 'Corporate Policies') {
    return 'Board Committee';
  } else if (this.policyCategory === 'Operational Policies') {
    return 'CEO';
  }
  return 'Department Head';
};

// Instance method to check if review is overdue
policySchema.methods.isReviewOverdue = function() {
  return this.nextReviewDate && new Date() > new Date(this.nextReviewDate) && this.reviewStatus === 'None';
};

// Instance method to calculate next review date (typically 1 year from completion)
policySchema.methods.calculateNextReviewDate = function() {
  const nextReview = new Date();
  nextReview.setFullYear(nextReview.getFullYear() + 1);
  return nextReview;
};

module.exports = mongoose.model('Policy', policySchema);
