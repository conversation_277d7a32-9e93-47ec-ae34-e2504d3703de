'use client';

import { useState, useEffect } from 'react';
import { DataTable } from './components/datatable';
import { columns } from './components/column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';
import { BarChart3 } from 'lucide-react';

const Exception = () => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch all published policies to filter for exceptions
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    updatePolicyStatus,
    governanceReview,
    grcReview,
    submitPolicyForReview,
    approvePolicy,
    rejectPolicy,
    requestException,
    exceptionGovernanceReview,
    approveException,
    rejectException,
  } = usePolicies({
    status: 'Published', // Only fetch published policies
    autoFetch: false,
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Filter policies to show only approved exceptions
  const filteredPolicies = policies.filter(
    (policy) => policy.exceptionStatus === 'Approved',
  );

  // Handle policy row click to open details modal
  const handlePolicyClick = (policy: Policy) => {
    setSelectedPolicy(policy);
    setIsDetailsModalOpen(true);
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle status update from details modal
  const handleStatusUpdate = async (
    policyId: string,
    newStatus: string,
    comments?: string,
    effectiveDate?: string,
    selectedGroups?: string[],
  ): Promise<boolean> => {
    const result = await updatePolicyStatus(
      policyId,
      newStatus,
      comments,
      effectiveDate,
      selectedGroups,
    );
    refetchPolicies();
    return result !== null;
  };

  // Handle governance review from details modal
  const handleGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await governanceReview(policyId, decision, comments);
    refetchPolicies();
    return result;
  };

  // Handle GRC review from details modal
  const handleGrcReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await grcReview(policyId, decision, comments);
    refetchPolicies();
    return result;
  };

  // Handle exception request from details modal
  const handleRequestException = async (
    policyId: string,
    justification: string,
    specificSection: string,
    exceptionType: 'Material Exception' | 'Immaterial Exception',
    effectiveDate?: string,
    expiryDate?: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await requestException(
      policyId,
      justification,
      specificSection,
      exceptionType,
      effectiveDate,
      expiryDate,
      comments,
    );
    refetchPolicies();
    return result;
  };

  // Handle exception governance review from details modal
  const handleExceptionGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await exceptionGovernanceReview(
      policyId,
      decision,
      comments,
    );
    refetchPolicies();
    return result;
  };

  // Handle approve exception from details modal
  const handleApproveException = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveException(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle reject exception from details modal
  const handleRejectException = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectException(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Transform API data to match table structure
  const transformedPolicies = filteredPolicies.map((policy, index) => ({
    id: index,
    policyId: policy.policyId,
    documentCode: policy.documentCode || '',
    name: policy.name,
    documentName: policy.documentName || policy.name,
    documentType: policy.documentType || 'Policy',
    version: policy.version || '1.0',
    versionNumber: policy.versionNumber || 1.0,
    policyType: policy.policyType || 'Corporate',
    categories: policy.categories.join(', '),
    department: policy.department,
    subDepartment: policy.subDepartment || '',
    policyOwner: policy.policyOwner.name,
    priorityScore: policy.priorityScore || 5,
    status: policy.status,
    detailedStatus: policy.detailedStatus || '',
    exceptionStatus: policy.exceptionStatus || '',
    exceptionType: policy.exceptionRequest?.exceptionType || '',
    exceptionJustification: policy.exceptionRequest?.justification || '',
    exceptionSpecificSection: policy.exceptionRequest?.specificSection || '',
    exceptionEffectiveDate: policy.exceptionRequest?.effectiveDate || '',
    exceptionExpiryDate: policy.exceptionRequest?.expiryDate || '',
    exceptionRequestedBy: policy.exceptionRequest?.requestedBy?.name || '',
    exceptionApprovedDate: policy.exceptionApprovedDate || '',
    classification: policy.classification || 'Internal',
    publishedDate: policy.publishedDate || '',
  }));

  // Calculate category counts from current filtered policies
  const categoryCounts = filteredPolicies.reduce(
    (acc, policy) => {
      policy.categories.forEach((category) => {
        acc[category] = (acc[category] || 0) + 1;
      });
      return acc;
    },
    {} as Record<string, number>,
  );

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading approved exception policies..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Exception Policies"
          message="We couldn't load the approved exception policies. Please try again."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="bg-gray-100 py-2 font-roboto">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">
              Policy Exception
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Browse and access policies with approved exceptions
            </p>
          </div>
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-green-500 bg-gradient-to-r from-green-50 to-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-green-700">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-6 w-6" />
                  <span>Approved Policy Exceptions</span>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-3xl font-bold text-green-700">
                    {policiesLoading ? '...' : filteredPolicies.length}
                  </div>
                  <div className="text-sm text-gray-500">
                    approved exceptions
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Policies with approved exceptions that are currently in effect
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedPolicies}
            loading={policiesLoading}
            onRowClick={(row: any) => {
              // Find the original policy from the transformed data
              const originalPolicy = policies.find(
                (p) => p._id === row.policyId || p.policyId === row.policyId,
              );
              if (originalPolicy) {
                handlePolicyClick(originalPolicy);
              }
            }}
          />
        </div>
      </div>

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={handleStatusUpdate}
        onGovernanceReview={handleGovernanceReview}
        onGrcReview={handleGrcReview}
        onRequestException={handleRequestException}
        onExceptionGovernanceReview={handleExceptionGovernanceReview}
        onApproveException={handleApproveException}
        onRejectException={handleRejectException}
        onPolicyUpdate={refetchPolicies}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default Exception;
