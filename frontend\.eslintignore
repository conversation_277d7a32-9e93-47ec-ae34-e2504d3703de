{
  "extends": [
    "next/core-web-vitals",
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier"
  ],
  "plugins": ["prettier", "@typescript-eslint", "unused-imports", "import"],
  "parser": "@typescript-eslint/parser",
  "rules": {
    "no-unused-vars": "off", // Ensure this is turned off
    "@typescript-eslint/no-unused-vars": "off", // Ensure this is turned off
    "unused-imports/no-unused-imports": "error", // Ensure this is turned on
    "@typescript-eslint/no-var-requires": "off",
    "@next/next/no-img-element": "off",
    "unused-imports/no-unused-vars": [
      "error",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_"
      }
    ],
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],
    "import/no-unresolved": "error", // This checks if imports resolve to valid files
    "import/named": "error", // This checks for named exports in imported modules
    "import/default": "error", // This checks for default exports in imported modules
    "import/namespace": "error" // This checks for namespace imports in imported modules
  },
  "root": true
}