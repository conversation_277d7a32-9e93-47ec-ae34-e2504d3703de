# GRC Web App - Comprehensive API Documentation

## Overview
This document provides comprehensive API documentation for the GRC (Governance, Risk, and Compliance) web application backend. The system manages policy lifecycle, user authentication, notifications, document editing, and file uploads.

## Technology Stack
- **Runtime**: Node.js (>=16.0.0)
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: <PERSON><PERSON> validation library
- **File Upload**: Multer middleware
- **Real-time Communication**: Socket.IO
- **Document Editing**: OnlyOffice integration
- **Security**: Helmet, CORS, Rate limiting
- **Environment**: dotenv for configuration

## Base URL
- **Development**: `http://localhost:5000/api`
- **Production**: `${NEXT_PUBLIC_API_URL}/api`

## Authentication & Authorization

### JWT Token Structure
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

### User Roles & Permissions
- **Super Admin**: Full system access
- **Admin**: Manage policies and users (except super admin functions)
- **Approver**: Approve/reject policies and requests, view reports (Governance department)
- **Creator**: Create and edit own policies, request new policies
- **Reviewer**: Review policy documents for accuracy and compliance (GRC department)
- **Publisher**: Publish approved policies to repository
- **Viewer**: Read-only access to reports

## Database Schemas

### Policy Schema (Enhanced)
```javascript
{
  _id: ObjectId,
  policyId: String, // Auto-generated unique identifier
  name: String, // Required, max 200 chars
  description: String, // Optional, max 1000 chars

  // Document Information
  documentName: String, // max 200 chars
  documentCode: String, // Unique, max 50 chars
  documentType: String, // "Policy", "SoW", "Framework", "Procedure", "Guideline", "Standard"
  version: String, // Default "1.0"
  versionNumber: Number, // Default 1.0, min 0.1

  // Department Information
  department: String, // Required, enum values
  subDepartment: String, // max 100 chars

  // Policy Classification
  policyCategory: String, // "Corporate Policies", "Operational Policies" - Required
  policyType: String, // "Corporate", "Operational", "Technical", "Administrative", "Strategic"
  categories: [String], // Required array of categories
  classification: String, // "Public", "Internal", "Confidential", "Restricted", "Top Secret"

  // Approval Authority
  approvalAuthority: String, // "Board Committee", "CEO", "Department Head" - Required

  // Priority and Scoring
  priorityScore: Number, // 1-10, default 5

  // Ownership and Responsibility
  policyOwner: {
    id: ObjectId, // ref: User, required
    name: String, // required
    email: String // required
  },
  initiatedBy: {
    id: ObjectId, // ref: User
    name: String,
    email: String,
    department: String
  },
  reviewedBy: [{
    id: ObjectId, // ref: User
    name: String,
    email: String,
    reviewDate: Date,
    comments: String
  }],
  endorsedBy: [{
    id: ObjectId, // ref: User
    name: String,
    email: String,
    endorsementDate: Date,
    comments: String
  }],
  authorizedApprover: {
    id: ObjectId, // ref: User
    name: String,
    email: String,
    role: String
  },

  // Governance Review
  governanceReview: {
    reviewer: {
      id: ObjectId, // ref: User
      name: String,
      email: String
    },
    reviewDate: Date,
    decision: String, // "Approved", "Rejected"
    comments: String
  },

  // Status and Workflow
  status: String, // Enum: "Request Initiated", "Draft", "Under Review", "Pending Approval", "Approved", "Published", "Archived", "Under Annual Review"

  // Dates
  requestedAt: Date,
  draftCompletedAt: Date,
  submittedForReviewAt: Date,
  reviewCompletedAt: Date,
  approvedAt: Date,
  publishedAt: Date,
  archivedAt: Date,
  nextReviewDate: Date,

  // Content and Attachments
  content: String, // Policy document content
  attachments: [{
    filename: String,
    originalName: String,
    url: String,
    size: Number,
    mimetype: String,
    uploadedAt: Date,
    uploadedBy: {
      id: ObjectId,
      name: String
    }
  }],

  // Workflow History
  workflowHistory: [{
    action: String,
    performedBy: {
      id: ObjectId,
      name: String,
      email: String
    },
    timestamp: Date,
    comments: String,
    previousStatus: String,
    newStatus: String
  }],

  // Metadata
  tags: [String],
  isActive: Boolean, // default true
  createdAt: Date,
  updatedAt: Date
}
```

### User Schema (Enhanced)
```javascript
{
  _id: ObjectId,
  name: String, // Required, max 100 chars
  email: String, // Required, unique, validated
  password: String, // Required, min 6 chars, hashed, select: false
  role: String, // Enum: "Creator", "Reviewer", "Approver", "Publisher", "Viewer", "Admin", "Super Admin"

  // Department Information
  department: String, // Required, extensive enum list
  subDepartment: String,
  position: String,
  reportingManager: ObjectId, // ref: User

  // Permissions
  permissions: [String], // Array of permission strings

  // Contact Information
  phoneNumber: String,

  // Status and Activity
  isActive: Boolean, // default true
  lastLogin: Date,
  loginAttempts: Number, // default 0
  lockUntil: Date,

  // Timestamps
  createdAt: Date,
  updatedAt: Date
}
```

### Notification Schema
```javascript
{
  _id: ObjectId,

  // Recipient information
  recipient: {
    id: ObjectId, // ref: User, required
    email: String, // required
    role: String // required
  },

  // Sender information (optional for system notifications)
  sender: {
    id: ObjectId, // ref: User
    name: String,
    email: String
  },

  // Notification classification
  type: String, // Enum: "policy_workflow", "user_management", "system", "reminder"
  category: String, // Enum: "approval_required", "status_change", "assignment", "due_date", "system_update"

  // Content
  title: String, // Required, max 200 chars
  message: String, // Required, max 500 chars

  // Priority and urgency
  priority: String, // Enum: "low", "medium", "high", "urgent", default "medium"

  // Related data
  data: {
    policyId: ObjectId, // ref: Policy
    policyName: String,
    previousStatus: String,
    newStatus: String,
    actionRequired: Boolean, // default false
    dueDate: Date,
    actionUrl: String // URL to navigate to when clicked
  },

  // Status tracking
  isRead: Boolean, // default false
  readAt: Date,
  isArchived: Boolean, // default false
  archivedAt: Date,

  // Delivery tracking
  deliveryStatus: String, // Enum: "pending", "sent", "delivered", "failed"
  deliveryAttempts: Number, // default 0
  lastDeliveryAttempt: Date,

  // Expiration
  expiresAt: Date,

  // Timestamps
  createdAt: Date,
  updatedAt: Date
}
```

## API Endpoints Overview

### Authentication Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `POST` | `/api/auth/register` | Register new user | Public |
| `POST` | `/api/auth/login` | User login | Public |
| `POST` | `/api/auth/logout` | User logout | Public |
| `GET` | `/api/auth/me` | Get current user info | Private |
| `PUT` | `/api/auth/profile` | Update user profile | Private |

### Policy Management Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `GET` | `/api/policies` | Get all policies with filtering and pagination | Private |
| `GET` | `/api/policies/stats` | Get policy statistics for dashboard | Public/Private |
| `GET` | `/api/policies/:id` | Get specific policy by ID | Private |
| `POST` | `/api/policies/request` | Request policy initiation | Creator, Admin, Super Admin |
| `PUT` | `/api/policies/:id` | Update policy | Private (with ownership check) |
| `DELETE` | `/api/policies/:id` | Delete policy | Private (with ownership check) |
| `PUT` | `/api/policies/:id/status` | Update policy status | Private (role-based) |
| `GET` | `/api/policies/:id/history` | Get policy history/audit trail | Private |

### Policy Workflow Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `POST` | `/api/policies/:id/submit-for-review` | Submit policy for review (Draft → Under Review) | Creator, Admin, Super Admin |
| `POST` | `/api/policies/:id/governance-review` | Governance review (Request Initiated → Draft) | Approver (Governance) |
| `POST` | `/api/policies/:id/grc-review` | GRC review (Under Review → Pending Approval) | Reviewer (GRC) |
| `POST` | `/api/policies/:id/approve` | Approve policy | Approver |
| `POST` | `/api/policies/:id/reject` | Reject policy | Approver, Reviewer |

### File Upload Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `POST` | `/uploads/policies/:id/attachments` | Upload policy attachments | Private |

### User Management Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `GET` | `/api/users` | Get all users | Admin, Super Admin |
| `GET` | `/api/users/:id` | Get specific user | Admin, Super Admin |
| `POST` | `/api/users` | Create new user | Admin, Super Admin |
| `PUT` | `/api/users/:id` | Update user | Admin, Super Admin |
| `DELETE` | `/api/users/:id` | Deactivate user | Admin, Super Admin |

### Notification Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `GET` | `/api/notifications` | Get user notifications | Private |
| `GET` | `/api/notifications/unread-count` | Get unread notification count | Private |
| `PUT` | `/api/notifications/:id/read` | Mark notification as read | Private |
| `PUT` | `/api/notifications/read-all` | Mark all notifications as read | Private |
| `DELETE` | `/api/notifications/:id` | Delete notification | Private |
| `PUT` | `/api/notifications/:id/archive` | Archive notification | Private |
| `GET` | `/api/notifications/preferences` | Get notification preferences | Private |
| `PUT` | `/api/notifications/preferences` | Update notification preferences | Private |
| `POST` | `/api/notifications` | Create notification | Admin, Super Admin |

### OnlyOffice Integration Endpoints
| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| `GET` | `/api/onlyoffice/config/:policyId` | Get OnlyOffice editor configuration | Private |
| `GET` | `/api/onlyoffice/documents/:policyId/content` | Get document content for OnlyOffice | Public (with validation) |
| `POST` | `/api/onlyoffice/callback/:policyId` | Handle callback from OnlyOffice | Public (with validation) |
| `POST` | `/api/onlyoffice/documents/:policyId/create` | Create new document | Private |

## Detailed API Documentation

### Authentication APIs

#### POST /api/auth/register
Register a new user in the system.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "role": "Creator",
  "department": "IT",
  "phoneNumber": "+1234567890"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "Creator",
    "department": "IT",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "User registered successfully"
}
```

#### POST /api/auth/login
Authenticate user and return JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "Creator",
    "department": "IT",
    "lastLogin": "2024-01-15T10:30:00.000Z"
  },
  "message": "Login successful"
}
```

#### GET /api/auth/me
Get current authenticated user information.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "Creator",
    "department": "IT",
    "permissions": ["create_policy", "edit_policy"],
    "lastLogin": "2024-01-15T10:30:00.000Z"
  }
}
```

### Policy Management APIs

#### GET /api/policies
Get all policies with filtering, pagination, and search capabilities.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `status` (string): Filter by policy status
- `department` (string): Filter by department
- `policyOwner` (string): Filter by policy owner ID
- `search` (string): Search in policy name and description
- `sortBy` (string): Sort field (default: 'createdAt')
- `sortOrder` (string): Sort order 'asc' or 'desc' (default: 'desc')
- `management` (boolean): Filter for management view
- `myActions` (boolean): Filter for user's actionable items

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "policies": [
      {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "policyId": "ORG_SEC_IT_v.1.0",
        "name": "Information Security Policy",
        "description": "Comprehensive security guidelines",
        "status": "Draft",
        "department": "IT",
        "policyCategory": "Corporate Policies",
        "policyOwner": {
          "id": "64f8a1b2c3d4e5f6a7b8c9d1",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "version": "1.0",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 45,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "metadata": {
      "totalPolicies": 45,
      "statusCounts": {
        "requestInitiated": 5,
        "draft": 12,
        "underReview": 8,
        "pendingApproval": 6,
        "approved": 10,
        "published": 3,
        "archived": 1
      }
    }
  }
}
```

#### POST /api/policies/request
Request initiation of a new policy.

**Access:** Creator, Admin, Super Admin

**Request Body:**
```json
{
  "name": "Information Security Policy",
  "description": "Policy for information security guidelines",
  "documentName": "InfoSec Policy Document",
  "documentType": "Policy",
  "policyCategory": "Corporate Policies",
  "policyType": "Corporate",
  "categories": ["Organizational", "Security"],
  "classification": "Internal",
  "department": "IT",
  "subDepartment": "Cybersecurity",
  "priorityScore": 8,
  "approvalAuthority": "CEO",
  "tags": ["security", "compliance", "mandatory"],
  "nextReviewDate": "2025-01-15T00:00:00.000Z"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "policyId": "ORG_SEC_IT_v.1.0",
    "name": "Information Security Policy",
    "status": "Request Initiated",
    "policyOwner": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d1",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "department": "IT",
    "policyCategory": "Corporate Policies",
    "approvalAuthority": "CEO",
    "requestedAt": "2024-01-15T10:30:00.000Z",
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Policy request initiated successfully"
}
```

#### GET /api/policies/:id
Get detailed information about a specific policy.

**Path Parameters:**
- `id` (string): Policy MongoDB ObjectId

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "policyId": "ORG_SEC_IT_v.1.0",
    "name": "Information Security Policy",
    "description": "Comprehensive security guidelines",
    "documentName": "InfoSec Policy Document",
    "documentType": "Policy",
    "version": "1.0",
    "versionNumber": 1.0,
    "status": "Draft",
    "department": "IT",
    "policyCategory": "Corporate Policies",
    "categories": ["Organizational", "Security"],
    "classification": "Internal",
    "approvalAuthority": "CEO",
    "priorityScore": 8,
    "policyOwner": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d1",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "attachments": [
      {
        "filename": "policy-1642234567890-123456789.docx",
        "originalName": "security_policy_draft.docx",
        "url": "/uploads/policies/policy-1642234567890-123456789.docx",
        "size": 245760,
        "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "uploadedAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "workflowHistory": [
      {
        "action": "Policy Request Initiated",
        "performedBy": {
          "id": "64f8a1b2c3d4e5f6a7b8c9d1",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "timestamp": "2024-01-15T10:30:00.000Z",
        "newStatus": "Request Initiated"
      }
    ],
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

#### GET /api/policies/stats
Get policy statistics for dashboard and reporting.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "metadata": {
      "totalPolicies": 45,
      "statusCounts": {
        "requestInitiated": 5,
        "draft": 12,
        "underReview": 8,
        "pendingApproval": 6,
        "approved": 10,
        "published": 3,
        "archived": 1,
        "underAnnualReview": 0
      },
      "statusBreakdown": {
        "Request Initiated": 5,
        "Draft": 12,
        "Under Review": 8,
        "Pending Approval": 6,
        "Approved": 10,
        "Published": 3,
        "Archived": 1
      },
      "totalPoliciesCount": 45,
      "totalStatusCounts": {
        "requestInitiated": 5,
        "draft": 12,
        "underReview": 8,
        "pendingApproval": 6,
        "approved": 10,
        "published": 3,
        "archived": 1,
        "underAnnualReview": 0
      }
    }
  }
}
```

### Policy Workflow APIs

#### POST /api/policies/:id/submit-for-review
Submit a policy for review (Draft → Under Review).

**Access:** Creator, Admin, Super Admin
**Path Parameters:** `id` - Policy ID

**Request Body:**
```json
{
  "comments": "Policy draft completed and ready for review"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "status": "Under Review",
    "submittedForReviewAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Policy submitted for review successfully"
}
```

#### POST /api/policies/:id/governance-review
Perform governance review (Request Initiated → Draft).

**Access:** Approver (Governance department)
**Path Parameters:** `id` - Policy ID

**Request Body:**
```json
{
  "decision": "Approved",
  "comments": "Policy request approved. Proceed with drafting."
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "status": "Draft",
    "governanceReview": {
      "reviewer": {
        "id": "64f8a1b2c3d4e5f6a7b8c9d2",
        "name": "Jane Smith",
        "email": "<EMAIL>"
      },
      "reviewDate": "2024-01-15T10:30:00.000Z",
      "decision": "Approved",
      "comments": "Policy request approved. Proceed with drafting."
    }
  },
  "message": "Governance review completed successfully"
}
```

#### POST /api/policies/:id/approve
Approve a policy.

**Access:** Approver
**Path Parameters:** `id` - Policy ID

**Request Body:**
```json
{
  "comments": "Policy approved for implementation"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "status": "Approved",
    "approvedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Policy approved successfully"
}
```

### User Management APIs

#### GET /api/users
Get all users with filtering and pagination.

**Access:** Admin, Super Admin

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `role` (string): Filter by user role
- `department` (string): Filter by department
- `isActive` (boolean): Filter by active status
- `search` (string): Search in name and email

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "Creator",
        "department": "IT",
        "isActive": true,
        "lastLogin": "2024-01-15T10:30:00.000Z",
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10
    }
  }
}
```

#### POST /api/users
Create a new user.

**Access:** Admin, Super Admin

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "role": "Reviewer",
  "department": "GRC",
  "phoneNumber": "+1234567890"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d1",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "Reviewer",
    "department": "GRC",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "User created successfully"
}
```

### Notification APIs

#### GET /api/notifications
Get user notifications with filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `unreadOnly` (boolean): Show only unread notifications
- `type` (string): Filter by notification type
- `category` (string): Filter by notification category

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "title": "Policy Approval Required",
        "message": "Information Security Policy requires your approval",
        "type": "policy_workflow",
        "category": "approval_required",
        "priority": "high",
        "isRead": false,
        "data": {
          "policyId": "64f8a1b2c3d4e5f6a7b8c9d1",
          "policyName": "Information Security Policy",
          "actionRequired": true,
          "actionUrl": "/policies/64f8a1b2c3d4e5f6a7b8c9d1"
        },
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 15,
      "itemsPerPage": 20
    },
    "unreadCount": 5
  }
}
```

#### GET /api/notifications/unread-count
Get count of unread notifications.

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "unreadCount": 5
  }
}
```

#### PUT /api/notifications/:id/read
Mark a notification as read.

**Path Parameters:** `id` - Notification ID

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

#### PUT /api/notifications/preferences
Update notification preferences.

**Request Body:**
```json
{
  "emailNotifications": true,
  "pushNotifications": false,
  "categories": {
    "approval_required": true,
    "status_change": true,
    "assignment": false,
    "due_date": true,
    "system_update": false
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "preferences": {
      "emailNotifications": true,
      "pushNotifications": false,
      "categories": {
        "approval_required": true,
        "status_change": true,
        "assignment": false,
        "due_date": true,
        "system_update": false
      }
    }
  },
  "message": "Notification preferences updated successfully"
}
```

### OnlyOffice Integration APIs

#### GET /api/onlyoffice/config/:policyId
Get OnlyOffice editor configuration for a policy document.

**Path Parameters:** `policyId` - Policy ID

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "document": {
      "fileType": "docx",
      "key": "policy_64f8a1b2c3d4e5f6a7b8c9d0_1642234567890",
      "title": "Information Security Policy.docx",
      "url": "http://localhost:5000/api/onlyoffice/documents/64f8a1b2c3d4e5f6a7b8c9d0/content"
    },
    "documentType": "word",
    "editorConfig": {
      "mode": "edit",
      "lang": "en",
      "callbackUrl": "http://localhost:5000/api/onlyoffice/callback/64f8a1b2c3d4e5f6a7b8c9d0",
      "user": {
        "id": "64f8a1b2c3d4e5f6a7b8c9d1",
        "name": "John Doe"
      }
    }
  }
}
```

#### POST /api/onlyoffice/callback/:policyId
Handle callback from OnlyOffice Document Server.

**Path Parameters:** `policyId` - Policy ID

**Request Body:**
```json
{
  "status": 2,
  "url": "https://documentserver/url/to/edited/document.docx",
  "key": "policy_64f8a1b2c3d4e5f6a7b8c9d0_1642234567890"
}
```

**Response (200 OK):**
```json
{
  "error": 0
}
```

### File Upload APIs

#### POST /uploads/policies/:id/attachments
Upload policy attachments.

**Path Parameters:** `id` - Policy ID
**Content-Type:** `multipart/form-data`

**Form Data:**
- `file`: File to upload (Word documents, PDF files)

**Supported File Types:**
- `.docx` (Word documents)
- `.doc` (Word documents)
- `.pdf` (PDF files)

**File Size Limit:** 10MB

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "attachment": {
      "filename": "policy-1642234567890-123456789.docx",
      "originalName": "security_policy_draft.docx",
      "url": "/uploads/policies/policy-1642234567890-123456789.docx",
      "size": 245760,
      "mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "uploadedAt": "2024-01-15T10:30:00.000Z"
    }
  },
  "message": "File uploaded successfully"
}
```

## Error Handling

### Standard Error Response Format
All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information (development only)",
  "statusCode": 400
}
```

### HTTP Status Codes
- `200` - OK (Success)
- `201` - Created (Resource created successfully)
- `400` - Bad Request (Validation error, malformed request)
- `401` - Unauthorized (Authentication required or failed)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Resource not found)
- `409` - Conflict (Resource already exists)
- `422` - Unprocessable Entity (Validation failed)
- `429` - Too Many Requests (Rate limit exceeded)
- `500` - Internal Server Error (Server error)

### Common Error Examples

#### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation error",
  "error": "Policy name must be at least 3 characters long, Department is required",
  "statusCode": 400
}
```

#### Authentication Error (401)
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "No token provided",
  "statusCode": 401
}
```

#### Authorization Error (403)
```json
{
  "success": false,
  "message": "Access denied",
  "error": "Insufficient permissions to perform this action",
  "statusCode": 403
}
```

#### Resource Not Found (404)
```json
{
  "success": false,
  "message": "Policy not found",
  "statusCode": 404
}
```

#### File Upload Error (400)
```json
{
  "success": false,
  "message": "File upload failed",
  "error": "Invalid file type. Only Word documents (.doc, .docx) and PDF files are allowed.",
  "statusCode": 400
}
```

## Validation Rules

### Policy Validation
- **name**: Required, 3-200 characters
- **description**: Optional, max 1000 characters
- **documentName**: Optional, max 200 characters
- **documentCode**: Optional, unique, max 50 characters
- **documentType**: Enum values: "Policy", "SoW", "Framework", "Procedure", "Guideline", "Standard"
- **policyCategory**: Required, "Corporate Policies" or "Operational Policies"
- **categories**: Required array, at least one category
- **department**: Required, must be from predefined list
- **priorityScore**: Number between 1-10
- **approvalAuthority**: Required, "Board Committee", "CEO", or "Department Head"

### User Validation
- **name**: Required, 2-100 characters
- **email**: Required, valid email format, unique
- **password**: Required, minimum 6 characters
- **role**: Must be valid role from enum
- **department**: Required, must be from predefined list

### Notification Validation
- **title**: Required, max 200 characters
- **message**: Required, max 500 characters
- **type**: Required, valid notification type
- **category**: Required, valid notification category
- **priority**: Optional, defaults to "medium"

## Business Logic & Workflow

### Policy Lifecycle Status Flow
```
Request Initiated → Draft → Under Review → Pending Approval → Approved → Published
                     ↓         ↓              ↓              ↓         ↓
                  Rejected  Rejected      Rejected       Archived  Archived
                     ↓         ↓              ↓              ↓         ↓
                   Draft     Draft          Draft      Under Annual Review
```

### Status Transitions & Required Roles

| From Status | To Status | Action | Required Role | API Endpoint |
|-------------|-----------|--------|---------------|--------------|
| Request Initiated | Draft | Governance Review | Approver (Governance) | `POST /api/policies/:id/governance-review` |
| Request Initiated | Rejected | Governance Review | Approver (Governance) | `POST /api/policies/:id/reject` |
| Draft | Under Review | Submit for Review | Creator, Admin, Super Admin | `POST /api/policies/:id/submit-for-review` |
| Under Review | Pending Approval | GRC Review | Reviewer (GRC) | `POST /api/policies/:id/grc-review` |
| Under Review | Draft | GRC Review (Reject) | Reviewer (GRC) | `POST /api/policies/:id/reject` |
| Pending Approval | Approved | Final Approval | Approver | `POST /api/policies/:id/approve` |
| Pending Approval | Draft | Final Approval (Reject) | Approver | `POST /api/policies/:id/reject` |
| Approved | Published | Publish | Publisher | `PUT /api/policies/:id/status` |
| Published | Archived | Archive | Admin, Super Admin | `PUT /api/policies/:id/status` |
| Published | Under Annual Review | Annual Review | System/Admin | `PUT /api/policies/:id/status` |

### Policy ID Generation
Policy IDs are auto-generated using the format:
```
{CATEGORY}_{TYPE}_{DEPARTMENT}_v.{VERSION}
```

Examples:
- `ORG_SEC_IT_v.1.0` - Organizational Security policy from IT department, version 1.0
- `CORP_HR_HR_v.2.1` - Corporate HR policy from HR department, version 2.1

### Approval Authority Routing
Based on `policyCategory` and `approvalAuthority`:

- **Corporate Policies + Board Committee**: Requires board-level approval
- **Corporate Policies + CEO**: Requires CEO approval
- **Operational Policies + Department Head**: Requires department head approval

### Notification System
Automatic notifications are sent for:
- Policy status changes
- Approval requests
- Review assignments
- Due date reminders
- System updates

### Real-time Features
- WebSocket integration for real-time notifications
- Live document editing with OnlyOffice
- Real-time status updates
- Collaborative workflow management

## Security Considerations

### Authentication & Authorization
- JWT token-based authentication
- Role-based access control (RBAC)
- Token expiration and refresh mechanisms
- Secure password hashing with bcrypt

### Data Protection
- Input validation and sanitization
- SQL injection prevention (MongoDB)
- XSS protection with Helmet.js
- CORS configuration for cross-origin requests
- Rate limiting to prevent abuse

### File Upload Security
- File type validation (MIME type checking)
- File size limits (10MB maximum)
- Secure file storage with unique naming
- Virus scanning (recommended for production)

### API Security
- HTTPS enforcement (production)
- Request/response logging
- Error message sanitization
- API versioning for backward compatibility

## Performance Considerations

### Database Optimization
- Indexed fields for faster queries
- Pagination for large datasets
- Aggregation pipelines for statistics
- Connection pooling

### Caching Strategy
- Redis caching for frequently accessed data
- API response caching
- Static file caching
- Session management

### Monitoring & Logging
- Request/response logging with Morgan
- Error tracking and reporting
- Performance monitoring
- Health check endpoints

## Development & Deployment

### Environment Configuration
```bash
# Required Environment Variables
NODE_ENV=development|production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/grc_db
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d

# OnlyOffice Configuration
ONLYOFFICE_SERVER_URL=http://localhost:8080
ONLYOFFICE_JWT_SECRET=your_onlyoffice_jwt_secret

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### Docker Configuration
The application includes Docker support with:
- MongoDB container
- OnlyOffice Document Server container
- Redis container (for caching)
- Application container

### API Testing
Use the provided test scripts or tools like Postman:
```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Seed database with sample data
npm run seed
```

## Conclusion

This comprehensive API documentation covers all aspects of the GRC Web Application backend, including:

- Complete endpoint documentation with request/response examples
- Database schemas and relationships
- Authentication and authorization mechanisms
- File upload and document management
- Real-time notifications and WebSocket integration
- OnlyOffice document editing integration
- Error handling and validation
- Security considerations and best practices
- Performance optimization strategies

The API is designed to be RESTful, secure, and scalable, supporting the complete policy management lifecycle from initiation to publication and archival.
