const Joi = require('joi');

// Validation middleware factory
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: errorMessage,
      });
    }
    
    next();
  };
};

// User registration validation schema
const registerSchema = Joi.object({
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name cannot exceed 100 characters',
    'any.required': 'Name is required',
  }),
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required',
  }),
  role: Joi.string().valid('Creator', 'Reviewer', 'Approver', 'Publisher', 'Viewer', 'Admin', 'Super Admin').default('Creator'),
  department: Joi.string().valid('HR', 'IT', 'Finance', 'Legal', 'Operations', 'Security', 'Compliance').required().messages({
    'any.required': 'Department is required',
    'any.only': 'Department must be one of: HR, IT, Finance, Legal, Operations, Security, Compliance',
  }),
  phoneNumber: Joi.string().optional(),
});

// User login validation schema
const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required',
  }),
});

// Policy request validation schema
const policyRequestSchema = Joi.object({
  name: Joi.string().min(3).max(200).required().messages({
    'string.min': 'Policy name must be at least 3 characters long',
    'string.max': 'Policy name cannot exceed 200 characters',
    'any.required': 'Policy name is required',
  }),
  description: Joi.string().max(1000).optional().messages({
    'string.max': 'Description cannot exceed 1000 characters',
  }),

  // Document Information
  documentName: Joi.string().max(200).optional().messages({
    'string.max': 'Document name cannot exceed 200 characters',
  }),
  documentCode: Joi.string().max(50).optional().messages({
    'string.max': 'Document code cannot exceed 50 characters',
  }),
  documentType: Joi.string().valid('Policy', 'SoW', 'Framework', 'Procedure', 'Guideline', 'Standard').default('Policy'),
  version: Joi.string().max(20).default('1.0'),

  // Policy Classification
  policyCategory: Joi.string().valid('Corporate Policies', 'Operational Policies').required().messages({
    'any.required': 'Policy category is required',
    'any.only': 'Policy category must be either Corporate Policies or Operational Policies',
  }),
  policyType: Joi.string().valid('Corporate', 'Operational', 'Technical', 'Administrative', 'Strategic').default('Corporate'),
  categories: Joi.array().items(
    Joi.string().valid('Organizational', 'Compliance', 'Corporate', 'Security', 'HR', 'IT', 'Finance')
  ).min(1).required().messages({
    'array.min': 'At least one category is required',
    'any.required': 'Categories are required',
    'any.only': 'Categories must be one of: Organizational, Compliance, Corporate, Security, HR, IT, Finance',
  }),
  classification: Joi.string().valid('Public', 'Internal', 'Confidential', 'Restricted', 'Top Secret').default('Internal'),

  // Department Information
  department: Joi.string().valid('HR', 'IT', 'Finance', 'Legal', 'Operations', 'Security', 'Compliance').required().messages({
    'any.required': 'Department is required',
    'any.only': 'Department must be one of: HR, IT, Finance, Legal, Operations, Security, Compliance',
  }),
  subDepartment: Joi.string().max(100).optional().messages({
    'string.max': 'Sub-department cannot exceed 100 characters',
  }),

  // Priority and Scoring
  priorityScore: Joi.number().integer().min(1).max(10).default(5).messages({
    'number.min': 'Priority score must be at least 1',
    'number.max': 'Priority score cannot exceed 10',
  }),
  priority: Joi.string().valid('Low', 'Medium', 'High', 'Critical').default('Medium'),

  // Date Management
  startDate: Joi.date().iso().optional().messages({
    'date.format': 'Start date must be a valid ISO date',
  }),
  dueDate: Joi.date().iso().optional().messages({
    'date.format': 'Due date must be a valid ISO date',
  }),
  nextReviewDate: Joi.date().iso().optional().messages({
    'date.format': 'Next review date must be a valid ISO date',
  }),
});

// Policy update validation schema - allows all fields that can be edited in "Request Initiated" status
const policyUpdateSchema = Joi.object({
  name: Joi.string().min(3).max(200).optional().messages({
    'string.min': 'Policy name must be at least 3 characters long',
    'string.max': 'Policy name cannot exceed 200 characters',
  }),
  description: Joi.string().max(1000).optional().messages({
    'string.max': 'Description cannot exceed 1000 characters',
  }),

  // Document Information
  documentName: Joi.string().max(200).optional().messages({
    'string.max': 'Document name cannot exceed 200 characters',
  }),
  documentCode: Joi.string().max(50).optional().messages({
    'string.max': 'Document code cannot exceed 50 characters',
  }),
  documentType: Joi.string().valid('Policy', 'SoW', 'Framework', 'Procedure', 'Guideline', 'Standard').optional(),
  version: Joi.string().max(20).optional(),

  // Policy Classification
  policyCategory: Joi.string().valid('Corporate Policies', 'Operational Policies').optional().messages({
    'any.only': 'Policy category must be either Corporate Policies or Operational Policies',
  }),
  policyType: Joi.string().valid('Corporate', 'Operational', 'Technical', 'Administrative', 'Strategic').optional(),
  categories: Joi.array().items(
    Joi.string().valid('Organizational', 'Compliance', 'Corporate', 'Security', 'HR', 'IT', 'Finance')
  ).min(1).optional().messages({
    'array.min': 'At least one category is required',
    'any.only': 'Categories must be one of: Organizational, Compliance, Corporate, Security, HR, IT, Finance',
  }),
  classification: Joi.string().valid('Public', 'Internal', 'Confidential', 'Restricted', 'Top Secret').optional(),

  // Department Information
  department: Joi.string().valid('HR', 'IT', 'Finance', 'Legal', 'Operations', 'Security', 'Compliance',
    'PMO Advisory Investment Activation', 'Development', 'Procurement', 'Marketing', 'Golf Excellence',
    'Risk Management', 'Sales & Sponsorships', 'Events', 'Federation Office', 'Academies', 'Governance',
    'Business Continuity Management', 'Events Management', 'Local Golf', 'Tournaments', 'Internal Audit',
    'Cybersecurity', 'Admin & HSSE', 'CEO Office', 'Strategy', 'Golf Operations').optional(),
  subDepartment: Joi.string().max(100).optional().messages({
    'string.max': 'Sub-department cannot exceed 100 characters',
  }),

  // Priority and Scoring
  priorityScore: Joi.number().min(1).max(10).optional().messages({
    'number.min': 'Priority score must be at least 1',
    'number.max': 'Priority score cannot exceed 10',
  }),

  // Legacy fields
  priority: Joi.string().valid('Low', 'Medium', 'High', 'Critical').optional(),

  // Date fields
  dueDate: Joi.date().iso().optional().messages({
    'date.format': 'Due date must be a valid ISO date',
  }),
  startDate: Joi.date().iso().optional().messages({
    'date.format': 'Start date must be a valid ISO date',
  }),
  nextReviewDate: Joi.date().iso().optional().messages({
    'date.format': 'Next review date must be a valid ISO date',
  }),

  // Content and metadata
  content: Joi.string().optional(),
  'metadata.priority': Joi.string().valid('Low', 'Medium', 'High', 'Critical').optional(),
  'metadata.dueDate': Joi.date().iso().optional().messages({
    'date.format': 'Due date must be a valid ISO date',
  }),
  'metadata.tags': Joi.array().items(Joi.string()).optional(),
});

// Status update validation schema
const statusUpdateSchema = Joi.object({
  status: Joi.string().valid(
    'Request Initiated', 'Draft', 'Pending Approval', 'Approved', 'Published', 'Archived', 'Under Annual Review'
  ).required().messages({
    'any.required': 'Status is required',
    'any.only': 'Status must be one of: Request Initiated, Draft, Pending Approval, Approved, Published, Archived, Under Annual Review',
  }),
  comments: Joi.string().max(500).optional().messages({
    'string.max': 'Comments cannot exceed 500 characters',
  }),
});

// Approval/Rejection validation schema
const approvalSchema = Joi.object({
  comments: Joi.string().max(500).optional().messages({
    'string.max': 'Comments cannot exceed 500 characters',
  }),
});

// Governance review validation schema
const governanceReviewSchema = Joi.object({
  decision: Joi.string().valid('Approved', 'Rejected').required().messages({
    'any.required': 'Decision is required',
    'any.only': 'Decision must be either Approved or Rejected',
  }),
  comments: Joi.string().max(500).optional().messages({
    'string.max': 'Comments cannot exceed 500 characters',
  }),
});

// GRC review validation schema
const grcReviewSchema = Joi.object({
  decision: Joi.string().valid('Approved', 'Rejected').required().messages({
    'any.required': 'Decision is required',
    'any.only': 'Decision must be either Approved or Rejected',
  }),
  comments: Joi.string().max(500).optional().messages({
    'string.max': 'Comments cannot exceed 500 characters',
  }),
});

// Notification validation schema
const notificationSchema = Joi.object({
  recipientId: Joi.string().required().messages({
    'any.required': 'Recipient ID is required',
  }),
  type: Joi.string().valid('policy_workflow', 'user_management', 'system', 'reminder').required().messages({
    'any.required': 'Notification type is required',
    'any.only': 'Type must be one of: policy_workflow, user_management, system, reminder',
  }),
  category: Joi.string().valid('approval_required', 'status_change', 'assignment', 'due_date', 'system_update').required().messages({
    'any.required': 'Notification category is required',
    'any.only': 'Category must be one of: approval_required, status_change, assignment, due_date, system_update',
  }),
  title: Joi.string().min(1).max(200).required().messages({
    'string.min': 'Title is required',
    'string.max': 'Title cannot exceed 200 characters',
    'any.required': 'Title is required',
  }),
  message: Joi.string().min(1).max(500).required().messages({
    'string.min': 'Message is required',
    'string.max': 'Message cannot exceed 500 characters',
    'any.required': 'Message is required',
  }),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium'),
  data: Joi.object({
    policyId: Joi.string().optional(),
    policyName: Joi.string().optional(),
    actionRequired: Joi.boolean().optional(),
    dueDate: Joi.date().iso().optional(),
    actionUrl: Joi.string().optional(),
  }).optional(),
  expiresAt: Joi.date().iso().optional().messages({
    'date.format': 'Expiration date must be a valid ISO date',
  }),
});

// Export validation middleware functions
const validateRegister = validate(registerSchema);
const validateLogin = validate(loginSchema);
const validatePolicyRequest = validate(policyRequestSchema);
const validatePolicyUpdate = validate(policyUpdateSchema);
const validateStatusUpdate = validate(statusUpdateSchema);
const validateApproval = validate(approvalSchema);
const validateGovernanceReview = validate(governanceReviewSchema);
const validateGrcReview = validate(grcReviewSchema);
const validateNotification = validate(notificationSchema);

module.exports = {
  validate,
  validateRegister,
  validateLogin,
  validatePolicyRequest,
  validatePolicyUpdate,
  validateStatusUpdate,
  validateApproval,
  validateGovernanceReview,
  validateGrcReview,
  validateNotification,
};
