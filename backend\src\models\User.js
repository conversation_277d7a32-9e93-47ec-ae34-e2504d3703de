const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email address',
    ],
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false, // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: [
      'Creator', 
      'Reviewer', 
      'Approver', 
      'Publisher', 
      'Viewer', 
      'Admin', 
      'Super Admin'
    ],
    default: 'Viewer',
  },
  department: {
    type: String,
    required: [true, 'Department is required'],
    enum: [
      'PMO Advisory Investment Activation',
      'Development',
      'Legal',
      'Procurement',
      'Marketing',
      'Golf Excellence',
      'Risk Management',
      'HR',
      'Sales & Sponsorships',
      'Events',
      'Compliance',
      'Finance',
      'Federation Office',
      'Academies',
      'Governance',
      'GRC',
      'Business Continuity Management',
      'Events Management',
      'Local Golf',
      'Tournaments',
      'Internal Audit',
      'IT',
      'Cybersecurity',
      'Admin & HSSE',
      'CEO Office',
      'Strategy',
      'Golf Operations'
    ],
  },
  subDepartment: {
    type: String,
    trim: true,
  },
  position: {
    type: String,
    trim: true,
  },
  reportingManager: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  permissions: [{
    type: String,
    enum: [
      'create_policy',
      'edit_policy',
      'delete_policy',
      'approve_policy',
      'view_all_policies',
      'manage_users',
      'view_reports',
      'export_data',
    ],
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  lastLogin: {
    type: Date,
  },
  profileImage: {
    type: String,
    default: '',
  },
  phoneNumber: {
    type: String,
    trim: true,
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
  },
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true,
    },
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light',
    },
    language: {
      type: String,
      default: 'en',
    },
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ department: 1 });
userSchema.index({ isActive: 1 });

// Virtual for full name (if needed for display)
userSchema.virtual('displayName').get(function() {
  return this.name;
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash password if it's been modified
  if (!this.isModified('password')) {
    return next();
  }

  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to update updatedAt
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to check password
userSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Instance method to generate JWT token
userSchema.methods.getSignedJwtToken = function() {
  return jwt.sign(
    { 
      id: this._id,
      email: this.email,
      role: this.role,
      department: this.department,
    },
    process.env.JWT_SECRET,
    {
      expiresIn: process.env.JWT_EXPIRE || '7d',
    }
  );
};

// Instance method to check if user has permission
userSchema.methods.hasPermission = function(permission) {
  return this.permissions.includes(permission) || this.role === 'Super Admin';
};

// Instance method to get user permissions based on role
userSchema.methods.getRolePermissions = function() {
  const rolePermissions = {
    'Super Admin': [
      'create_policy', 'edit_policy', 'delete_policy', 'approve_policy',
      'view_all_policies', 'manage_users', 'view_reports', 'export_data'
    ],
    'Admin': [
      'create_policy', 'edit_policy', 'delete_policy', 'approve_policy',
      'view_all_policies', 'manage_users', 'view_reports', 'export_data'
    ],
    'Creator': [
      'create_policy', 'edit_policy', 'view_reports'
    ],
    'Reviewer': [
      'view_all_policies', 'view_reports'
    ],
    'Approver': [
      'approve_policy', 'view_all_policies', 'view_reports'
    ],
    'Publisher': [
      'view_all_policies', 'view_reports'
    ],
    'Viewer': [
      'view_reports'
    ],
  };

  return rolePermissions[this.role] || [];
};

// Static method to find users by department
userSchema.statics.findByDepartment = function(department) {
  return this.find({ department, isActive: true });
};

// Static method to find approvers
userSchema.statics.findApprovers = function() {
  return this.find({ 
    role: { $in: ['Approver', 'Admin', 'Super Admin'] },
    isActive: true 
  });
};

module.exports = mongoose.model('User', userSchema);
