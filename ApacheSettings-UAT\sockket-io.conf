<VirtualHost *:443>
    ServerName grc-temp-app.autoresilience.com

    SSLEngine on
    SSLCertificateFile /etc/apache2/certificates/certificate.crt
    SSLCertificateKeyFile /etc/apache2/certificates/private.key
    SSLCACertificateFile /etc/apache2/certificates/ca_bundle.crt

    ProxyPreserveHost On
    ProxyRequests Off
    AllowEncodedSlashes NoDecode

    <Proxy *>
        Require all granted
    </Proxy>

    RewriteEngine On

    # --- WebSocket Proxy for Socket.IO on /app/socket.io/ ---
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/app/socket.io/(.*)$ ws://127.0.0.1:5000/app/socket.io/$1 [P,L]

    ProxyPass /app/socket.io/ http://127.0.0.1:5000/app/socket.io/
    ProxyPassReverse /app/socket.io/ http://127.0.0.1:5000/app/socket.io/

    # --- Auth routes ---
    ProxyPass /api/auth/ http://127.0.0.1:5000/api/auth/
    ProxyPassReverse /api/auth/ http://127.0.0.1:5000/api/auth/

# --- OnlyOffice Callbacks ---
ProxyPass /api/onlyoffice/ http://127.0.0.1:5000/api/onlyoffice/
ProxyPassReverse /api/onlyoffice/ http://127.0.0.1:5000/api/onlyoffice/


    # --- All other API routes ---
    ProxyPass / http://127.0.0.1:5000/
    ProxyPassReverse / http://127.0.0.1:5000/

    # --- Performance / Stability settings ---
    ProxyTimeout 600
    Timeout 600
    ProxyBadHeader Ignore
    SetEnv proxy-nokeepalive 1
    SetEnv proxy-sendchunked 1
    ProxyIOBufferSize 65536

    # --- Security Headers ---
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self' https: data: blob:; connect-src 'self' https: wss:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:;"
</VirtualHost>

