'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationProvider } from '@/contexts/NotificationContext';

export default function EditorLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { isAuthenticated, loading, user } = useAuth();

  // Handle authentication redirect
  useEffect(() => {
    console.log('Editor Layout Auth Check:', {
      loading,
      isAuthenticated,
      user: !!user,
      hasToken: !!localStorage.getItem('token'),
    });
    // Only redirect if we're sure there's no authentication
    if (!loading && !isAuthenticated && !localStorage.getItem('token')) {
      console.log('Redirecting to login from editor layout');
      router.push('/login');
    }
  }, [loading, isAuthenticated, router, user]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  // Show loading while redirecting if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Redirecting to login...</p>
      </div>
    );
  }

  return (
    <NotificationProvider>
      <main className="h-screen w-full overflow-hidden">{children}</main>
    </NotificationProvider>
  );
}
