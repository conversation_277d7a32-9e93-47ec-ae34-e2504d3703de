import React from 'react';
import * as Popover from '@radix-ui/react-popover'; // Import Radix Popover
import {
  Shield,
  BarChart2,
  ClipboardCheck,
  AlertOctagon,
  ClipboardList,
  LayoutGrid,
  Bug,
  Lock,
  Users,
  Info,
  LucideLayoutGrid,
} from 'lucide-react';

export default function AppDrawer() {
  const subscribedApps = [
    {
      name: 'Policy Management',
      icon: <Shield className="h-7 w-7 text-green-500" />,
      route: '/policies/dashboard',
    },
    {
      name: 'Enterprise Risk Management',
      icon: <BarChart2 className="h-6 w-6 text-green-500" />,
      route: '/enterprise-risk',
    },
    {
      name: 'Business Continuity Management',
      icon: <ClipboardCheck className="h-6 w-6 text-green-500" />,
      route: '/business-continuity',
    },
    {
      name: 'Crisis Management',
      icon: <AlertOctagon className="h-6 w-6 text-green-500" />,
      route: '/crisis-management',
    },
    {
      name: 'Compliance Management',
      icon: <ClipboardList className="h-6 w-6 text-green-500" />,
      route: '/compliance/dashboard',
    },
    {
      name: 'Audit Management',
      icon: <LucideLayoutGrid className="h-6 w-6 text-green-500" />,
      route: '/audit/dashboard',
    },
  ];

  const exploreMoreApps = [
    {
      name: 'Information Security Management',
      icon: <Bug className="h-6 w-6 text-green-500" />,
      route: '/information-security',
    },
    {
      name: 'Operational Risk Management',
      icon: <Shield className="h-6 w-6 text-green-500" />,
      route: '/operational-risk',
    },
    {
      name: 'Incident Management',
      icon: <Info className="h-6 w-6 text-green-500" />,
      route: '/incident-management',
    },
    {
      name: 'Data Privacy Management',
      icon: <Lock className="h-6 w-6 text-green-500" />,
      route: '/data-privacy',
    },
    {
      name: 'Cyber Security Management',
      icon: <Users className="h-6 w-6 text-green-500" />,
      route: '/cyber-security',
    },
    {
      name: 'Third Party Risk Management',
      icon: <Shield className="h-6 w-6 text-green-500" />,
      route: '/third-party-risk',
    },
  ];

  const handleNavigation = (route: string) => {
    window.location.href = route; // Redirect user to the selected module
  };

  return (
    <div className="mr-16 flex items-center justify-center">
      <Popover.Root>
        <Popover.Trigger asChild>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200">
            <LayoutGrid className="h-6 w-6 text-gray-700" />
          </button>
        </Popover.Trigger>
        {/* <Popover.Content
                        align="start" 
                        className="w-[500px] p-4 rounded-lg shadow-md bg-white border border-gray-200 mt-3 mr-12"
                    >
                
                        <div>
                        <h2 className="mb-4 text-lg font-semibold text-gray-700">Subscribed Apps</h2>
                        <div className="grid grid-cols-4 gap-4">
                            {subscribedApps.map((app) => (
                            <button
                                key={app.name}
                                onClick={() => handleNavigation(app.route)}
                                className="flex flex-col items-center p-2 rounded-lg hover:bg-gray-100"
                            >
                                {app.icon}
                                <span className="mt-2 text-sm text-gray-600 text-center">{app.name}</span>
                            </button>
                            ))}
                        </div>
                        </div>

                
                        <div className="my-6 border-t border-gray-200"></div>


                        <div>
                        <h2 className="mb-4 text-lg font-semibold text-gray-700">Explore More Apps</h2>
                        <div className="grid grid-cols-4 gap-4">
                            {exploreMoreApps.map((app) => (
                            <button
                                key={app.name}
                                onClick={() => handleNavigation(app.route)}
                                className="flex flex-col items-center p-2 rounded-lg hover:bg-gray-100"
                            >
                                {app.icon}
                                <span className="mt-2 text-sm text-gray-600 text-center">{app.name}</span>
                            </button>
                            ))}
                        </div>
                        </div>
                    </Popover.Content> */}
        <Popover.Content
          align="start"
          className="mr-16 mt-2 w-[500px] translate-x-4 transform rounded-lg border border-gray-200 bg-white p-4 shadow-lg"
        >
          <div>
            <h2 className="mb-4 text-lg font-semibold text-gray-700">
              Subscribed Apps
            </h2>
            <div className="grid grid-cols-3 gap-4">
              {subscribedApps.map((app) => (
                <button
                  key={app.name}
                  onClick={() => handleNavigation(app.route)}
                  className="flex flex-col items-center rounded-lg p-2 hover:bg-gray-100"
                >
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 text-green-500">
                    {app.icon}
                  </div>
                  <span className="mt-2 text-center text-sm text-gray-600">
                    {app.name}
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="my-6 border-t border-gray-200"></div>
          <div>
            <h2 className="mb-4 text-lg font-semibold text-gray-700">
              Explore More Apps
            </h2>
            <div className="grid grid-cols-3 gap-4">
              {exploreMoreApps.map((app) => (
                <button
                  key={app.name}
                  onClick={() => handleNavigation(app.route)}
                  className="flex flex-col items-center rounded-lg p-2 hover:bg-gray-100"
                >
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 text-green-500">
                    {app.icon}
                  </div>
                  <span className="mt-2 text-center text-sm text-gray-600">
                    {app.name}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </Popover.Content>
      </Popover.Root>
    </div>
  );
}
