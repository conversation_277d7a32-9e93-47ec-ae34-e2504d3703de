const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  // Recipient information
  recipient: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Recipient ID is required'],
    },
    email: {
      type: String,
      required: [true, 'Recipient email is required'],
    },
    role: {
      type: String,
      required: [true, 'Recipient role is required'],
    },
  },

  // Sender information (optional for system notifications)
  sender: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    name: String,
    email: String,
  },

  // Notification classification
  type: {
    type: String,
    enum: ['policy_workflow', 'user_management', 'system', 'reminder'],
    required: [true, 'Notification type is required'],
  },
  category: {
    type: String,
    enum: ['approval_required', 'status_change', 'assignment', 'due_date', 'system_update'],
    required: [true, 'Notification category is required'],
  },

  // Content
  title: {
    type: String,
    required: [true, 'Notification title is required'],
    maxlength: [200, 'Title cannot exceed 200 characters'],
  },
  message: {
    type: String,
    required: [true, 'Notification message is required'],
    maxlength: [500, 'Message cannot exceed 500 characters'],
  },

  // Priority and urgency
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
  },

  // Related data
  data: {
    policyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Policy',
    },
    policyName: String,
    previousStatus: String,
    newStatus: String,
    actionRequired: {
      type: Boolean,
      default: false,
    },
    dueDate: Date,
    actionUrl: String, // URL to navigate to when clicked
  },

  // Status tracking
  isRead: {
    type: Boolean,
    default: false,
  },
  readAt: {
    type: Date,
  },
  isArchived: {
    type: Boolean,
    default: false,
  },
  archivedAt: {
    type: Date,
  },

  // Delivery tracking
  deliveryStatus: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'failed'],
    default: 'pending',
  },
  deliveredAt: Date,
  failureReason: String,

  // Expiration (for temporary notifications)
  expiresAt: {
    type: Date,
    index: { expireAfterSeconds: 0 }, // MongoDB TTL index
  },

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Indexes for performance
notificationSchema.index({ 'recipient.id': 1, createdAt: -1 });
notificationSchema.index({ 'recipient.id': 1, isRead: 1 });
notificationSchema.index({ type: 1, category: 1 });
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Pre-save middleware to update updatedAt
notificationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Set readAt when marking as read
  if (this.isModified('isRead') && this.isRead && !this.readAt) {
    this.readAt = new Date();
  }
  
  // Set archivedAt when archiving
  if (this.isModified('isArchived') && this.isArchived && !this.archivedAt) {
    this.archivedAt = new Date();
  }
  
  next();
});

// Static method to create notification
notificationSchema.statics.createNotification = async function(notificationData) {
  try {
    const notification = new this(notificationData);
    await notification.save();
    return notification;
  } catch (error) {
    throw new Error(`Failed to create notification: ${error.message}`);
  }
};

// Static method to find user notifications
notificationSchema.statics.findUserNotifications = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    unreadOnly = false,
    type,
    category,
  } = options;

  const query = { 'recipient.id': userId };
  
  if (unreadOnly) query.isRead = false;
  if (type) query.type = type;
  if (category) query.category = category;
  
  // Don't include archived notifications by default
  query.isArchived = { $ne: true };

  const skip = (page - 1) * limit;

  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('sender.id', 'name email');
};

// Static method to get unread count
notificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({
    'recipient.id': userId,
    isRead: false,
    isArchived: { $ne: true },
  });
};

// Static method to mark notifications as read
notificationSchema.statics.markAsRead = function(userId, notificationIds = null) {
  const query = { 'recipient.id': userId };
  
  if (notificationIds) {
    query._id = { $in: notificationIds };
  }
  
  return this.updateMany(query, {
    $set: {
      isRead: true,
      readAt: new Date(),
      updatedAt: new Date(),
    },
  });
};

// Static method to delete old notifications
notificationSchema.statics.cleanupOldNotifications = function(daysOld = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  return this.deleteMany({
    createdAt: { $lt: cutoffDate },
    isRead: true,
  });
};

// Instance method to mark as delivered
notificationSchema.methods.markAsDelivered = function() {
  this.deliveryStatus = 'delivered';
  this.deliveredAt = new Date();
  return this.save();
};

// Instance method to mark as failed
notificationSchema.methods.markAsFailed = function(reason) {
  this.deliveryStatus = 'failed';
  this.failureReason = reason;
  return this.save();
};

// Virtual for time ago
notificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diffInMinutes = Math.floor((now - this.createdAt) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
});

// Ensure virtual fields are serialized
notificationSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Notification', notificationSchema);
