const Policy = require('../models/Policy');
const User = require('../models/User');

// @desc    Get all policies with filtering and pagination
// @route   GET /api/policies
// @access  Private
const getAllPolicies = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      department,
      policyOwner,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      management,
      myActions,
    } = req.query;

    // Build query object
    const query = {};

    // Apply filters
    if (status) {
      query.status = status;
    }

    if (department) {
      query.department = department;
    }

    if (policyOwner) {
      query['policyOwner.id'] = policyOwner;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { policyId: { $regex: search, $options: 'i' } },
        { documentName: { $regex: search, $options: 'i' } },
        { documentCode: { $regex: search, $options: 'i' } },
        { documentType: { $regex: search, $options: 'i' } },
        { policyType: { $regex: search, $options: 'i' } },
        { subDepartment: { $regex: search, $options: 'i' } },
        { classification: { $regex: search, $options: 'i' } },
        { detailedStatus: { $regex: search, $options: 'i' } },
      ];
    }

    // Role-based access control
    const isManagementView = management === 'true';
    const isMyActionsView = myActions === 'true';

    // Apply role-based filtering based on view type
    if (isMyActionsView) {
      // My Actions view - show only policies user can act on
      switch (req.user.role) {
        case 'Creator':
          // Creators can only see policies they own
          query['policyOwner.id'] = req.user._id;
          break;

        case 'Viewer':
          // Viewers can only see published policies (but can't act on them)
          query.status = 'Published';
          break;

        // Reviewer, Approver, Publisher, Admin, Super Admin can see all policies for actions
        case 'Reviewer':
        case 'Approver':
        case 'Publisher':
        case 'Admin':
        case 'Super Admin':
          // No additional filtering - they can see all policies to act on them
          break;

        default:
          // Unknown roles get no policies
          query._id = { $exists: false };
          break;
      }
    } else if (!isManagementView) {
      // Regular views (Dashboard, PolicyHub) - everyone can see published and archived policies
      // Only apply ownership filtering for non-published/non-archived policies
      if (req.user.role === 'Creator') {
        // Creators can see their own policies (all statuses) + all published policies + all archived policies
        query.$or = [
          { 'policyOwner.id': req.user._id },
          { status: 'Published' },
          { status: 'Archived' }
        ];
      }
      // All other roles can see all policies (for dashboard/policyhub)
    }
    // Management view shows all policies regardless of role

    // Prepare total count query (for metadata calculations)
    const totalCountQuery = {};
    if (isMyActionsView) {
      // Same filtering as main query for My Actions
      switch (req.user.role) {
        case 'Creator':
          totalCountQuery['policyOwner.id'] = req.user._id;
          break;

        case 'Viewer':
          totalCountQuery.status = 'Published';
          break;

        // Other roles see all policies
        case 'Reviewer':
        case 'Approver':
        case 'Publisher':
        case 'Admin':
        case 'Super Admin':
          // No filtering
          break;

        default:
          totalCountQuery._id = { $exists: false };
          break;
      }
    } else if (!isManagementView) {
      // Regular views - same logic as main query
      if (req.user.role === 'Creator') {
        totalCountQuery.$or = [
          { 'policyOwner.id': req.user._id },
          { status: 'Published' }
        ];
      }
    }

    // Debug logging for role-based filtering
    console.log(`User role: ${req.user.role}, Management view: ${isManagementView}, My Actions view: ${isMyActionsView}, Query filters:`, JSON.stringify(query));
    console.log('Total count query:', JSON.stringify(totalCountQuery));

    // Calculate pagination
    const pageNum = parseInt(page, 10);
    // For management views, policy hub (published policies), and my actions, use a much higher limit
    const isPublishedOnlyView = status === 'Published' && !isMyActionsView;
    const limitNum = (isManagementView || isPublishedOnlyView || isMyActionsView) ? 1000 : parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const policies = await Policy.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .populate('policyOwner.id', 'name email department')
      .populate('initiatedBy.id', 'name email department')
      .populate('reviewedBy.id', 'name email department')
      .populate('endorsedBy.id', 'name email department')
      .populate('authorizedApprover.id', 'name email department')
      .lean();

    // Get total count for pagination
    const total = await Policy.countDocuments(query);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    // Get filtered status counts (matching current query)
    const filteredStatusCounts = await Policy.aggregate([
      { $match: query }, // Apply the same filters as the main query
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Get total status counts (respecting role-based access control)
    // totalCountQuery is already defined above

    const totalStatusCounts = await Policy.aggregate([
      { $match: totalCountQuery },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Convert filtered counts to object
    const filteredStatusCountsObj = {};
    filteredStatusCounts.forEach(item => {
      filteredStatusCountsObj[item._id] = item.count;
    });

    // Convert total counts to object
    const totalStatusCountsObj = {};
    totalStatusCounts.forEach(item => {
      totalStatusCountsObj[item._id] = item.count;
    });

    // Individual filtered status counts for current view
    const filteredRequestInitiated = filteredStatusCountsObj['Request Initiated'] || 0;
    const filteredDraft = filteredStatusCountsObj['Draft'] || 0;
    const filteredUnderReview = filteredStatusCountsObj['Under Review'] || 0;
    const filteredPendingApproval = filteredStatusCountsObj['Pending Approval'] || 0;
    const filteredApproved = filteredStatusCountsObj['Approved'] || 0;
    const filteredPublished = filteredStatusCountsObj['Published'] || 0;
    const filteredArchived = filteredStatusCountsObj['Archived'] || 0;
    // Individual total status counts for dashboard overview
    const totalRequestInitiated = totalStatusCountsObj['Request Initiated'] || 0;
    const totalDraft = totalStatusCountsObj['Draft'] || 0;
    const totalUnderReview = totalStatusCountsObj['Under Review'] || 0;
    const totalPendingApproval = totalStatusCountsObj['Pending Approval'] || 0;
    const totalApproved = totalStatusCountsObj['Approved'] || 0;
    const totalPublished = totalStatusCountsObj['Published'] || 0;
    const totalArchived = totalStatusCountsObj['Archived'] || 0;

    // Get total count of all policies (respecting role-based access control)
    const totalPoliciesCount = await Policy.countDocuments(totalCountQuery);

    // Debug logging for results
    console.log('QUERY RESULTS:');
    console.log('- Policies returned:', policies.length);
    console.log('- Total count from query:', total);
    console.log('- Total policies count:', totalPoliciesCount);
    console.log('- Total status counts:', totalStatusCountsObj);
    console.log('- Filtered status counts:', filteredStatusCountsObj);

    res.status(200).json({
      success: true,
      data: {
        policies,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage,
          hasPrevPage,
        },
        metadata: {
          // Filtered counts (matching current query/filters)
          totalPolicies: total,
          statusCounts: {
            requestInitiated: filteredRequestInitiated,
            draft: filteredDraft,
            underReview: filteredUnderReview,
            pendingApproval: filteredPendingApproval,
            approved: filteredApproved,
            published: filteredPublished,
            archived: filteredArchived,
          },
          statusBreakdown: filteredStatusCountsObj,

          // Total counts (all policies, for dashboard overview)
          totalPoliciesCount,
          totalStatusCounts: {
            requestInitiated: totalRequestInitiated,
            draft: totalDraft,
            underReview: totalUnderReview,
            pendingApproval: totalPendingApproval,
            approved: totalApproved,
            published: totalPublished,
            archived: totalArchived,
          },
          totalStatusBreakdown: totalStatusCountsObj,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single policy by ID
// @route   GET /api/policies/:id
// @access  Private
const getPolicyById = async (req, res, next) => {
  try {
    const policy = await Policy.findById(req.params.id)
      .populate('policyOwner.id', 'name email department')
      .populate('initiatedBy.id', 'name email department')
      .populate('reviewedBy.id', 'name email department')
      .populate('endorsedBy.id', 'name email department')
      .populate('authorizedApprover.id', 'name email department')
      .populate('approvalWorkflow.approver.id', 'name email department');

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check access permissions
    if (req.user.role === 'Creator' &&
        policy.policyOwner.id.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this policy',
      });
    }

    res.status(200).json({
      success: true,
      data: policy,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Request policy initiation
// @route   POST /api/policies/request
// @access  Private (Creator, Admin, Super Admin only)
const requestPolicyInitiation = async (req, res, next) => {
  try {
    const {
      name,
      description,
      documentName,
      documentCode,
      documentType = 'Policy',
      version = '1.0',
      policyCategory = 'Operational Policies',
      policyType = 'Corporate',
      categories,
      department,
      subDepartment,
      priorityScore = 5,
      classification = 'Internal',
      priority = 'Medium',
      dueDate,
      startDate,
      nextReviewDate,
      comments, // Add this line
    } = req.body;

    // Generate unique policy ID and document code if not provided
    const policyId = Policy.generatePolicyId(department, categories, documentType);
    let finalDocumentCode = documentCode;

    if (!finalDocumentCode) {
      // Generate a sequence number (in production, this should be atomic)
      let count = await Policy.countDocuments({ department, documentType });
      let attempts = 0;
      const maxAttempts = 10;

      // Keep trying to generate a unique document code
      while (attempts < maxAttempts) {
        finalDocumentCode = Policy.generateDocumentCode(department, documentType, count + 1);

        // Check if this document code already exists
        const existingPolicy = await Policy.findOne({ documentCode: finalDocumentCode });
        if (!existingPolicy) {
          break; // Found a unique code
        }

        count++;
        attempts++;
      }

      if (attempts >= maxAttempts) {
        return res.status(500).json({
          success: false,
          error: 'Unable to generate unique document code. Please try again.',
        });
      }
    } else {
      // If user provided a document code, check if it already exists
      const existingPolicy = await Policy.findOne({ documentCode: finalDocumentCode });
      if (existingPolicy) {
        return res.status(400).json({
          success: false,
          error: `Document code "${finalDocumentCode}" already exists. Please use a different document code.`,
        });
      }
    }

    // Create policy object with all new fields
    const policyData = {
      policyId,
      name,
      description,

      // Document Information
      documentName: documentName || name,
      documentCode: finalDocumentCode,
      documentType,
      version,
      versionNumber: parseFloat(version) || 1.0,

      // Department Information
      department,
      subDepartment,

      // Policy Classification
      policyType,
      categories,
      classification,

      // Priority and Scoring
      priorityScore: parseInt(priorityScore) || 5,

      // Ownership and Responsibility
      policyOwner: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      initiatedBy: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        department: req.user.department,
      },

      // Policy Category and Approval Authority
      policyCategory,
      approvalAuthority: policyCategory === 'Corporate Policies' ? 'Board Committee' : 'CEO',

      // Status Information
      status: 'Request Initiated',

      // Initialize workflow history
      workflowHistory: [{
        status: 'Request Initiated',
        timestamp: new Date(),
        actor: {
          id: req.user._id,
          name: req.user.name,
          email: req.user.email,
          role: req.user.role || 'Creator',
        },
        action: 'Created',
        comments: comments || '',
      }],

      // Date Management
      startDate: startDate ? new Date(startDate) : new Date(),
      dueDate: dueDate ? new Date(dueDate) : null,
      nextReviewDate: nextReviewDate ? new Date(nextReviewDate) : null,

      // Legacy metadata for backward compatibility
      metadata: {
        priority,
        dueDate: dueDate ? new Date(dueDate) : null,
      },

      requestedAt: new Date(),
    };

    const policy = await Policy.create(policyData);

    // Send notification to governance reviewers
    if (req.notificationService) {
      try {
        await req.notificationService.notifyPolicyRequestInitiated(policy, req.user);
      } catch (notificationError) {
        console.error('Failed to send policy initiation notifications:', notificationError);
        // Don't fail the request if notification fails
      }
    }

    res.status(201).json({
      success: true,
      data: policy,
      message: 'Policy request initiated successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update policy
// @route   PUT /api/policies/:id
// @access  Private
const updatePolicy = async (req, res, next) => {
  try {
    let policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if user can edit this policy
    if (req.user.role === 'Creator' &&
        policy.policyOwner.id.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this policy',
      });
    }

    // Check if policy can be edited
    if (!policy.canBeEdited()) {
      return res.status(400).json({
        success: false,
        message: `Policy with status '${policy.status}' cannot be edited`,
      });
    }

    // Update policy
    policy = await Policy.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy updated successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete policy
// @route   DELETE /api/policies/:id
// @access  Private
const deletePolicy = async (req, res, next) => {
  try {
    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check permissions
    if (req.user.role === 'Creator' &&
        policy.policyOwner.id.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this policy',
      });
    }

    // Only allow deletion of certain statuses
    if (!['Not Started', 'Draft'].includes(policy.status)) {
      return res.status(400).json({
        success: false,
        message: `Policy with status '${policy.status}' cannot be deleted`,
      });
    }

    await Policy.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Policy deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update policy status
// @route   PUT /api/policies/:id/status
// @access  Private (Role-based)
const updatePolicyStatus = async (req, res, next) => {
  try {
    const { status, comments, effectiveDate, targetAudience, selectedGroups } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Role-based validation for specific status changes
    if (status === 'Published') {
      // Only Publishers, Admins, or Super Admins can publish
      if (!['Publisher', 'Admin', 'Super Admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only Publishers can publish policies',
        });
      }
    }

    // Validate status transition
    const validTransitions = {
      'Request Initiated': ['Draft'],
      'Draft': ['Under Review'],
      'Under Review': ['Pending Approval', 'Draft'],
      'Pending Approval': ['Approved', 'Draft'],
      'Approved': ['Published', 'Archived'],
      'Published': ['Archived'],
      'Archived': ['Draft', 'Published'],
    };

    if (!validTransitions[policy.status]?.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status transition from '${policy.status}' to '${status}'`,
      });
    }

    policy.status = status;
    
    // Handle publication-specific fields
    if (status === 'Published') {
      // Set published date
      policy.publishedDate = new Date();
      
      // Set effective date if provided
      if (effectiveDate) {
        policy.effectiveDate = new Date(effectiveDate);
      } else {
        // Default to published date if no effective date provided
        policy.effectiveDate = policy.publishedDate;
      }
      
      // Update publication settings
      policy.publicationSettings = {
        targetAudience: targetAudience || 'All Employees',
        selectedGroups: selectedGroups || [],
        publishedBy: {
          id: req.user._id,
          name: req.user.name,
          email: req.user.email,
        },
        isPublished: true,
      };
    }
    
    // Add to workflow history
    if (!policy.workflowHistory) {
      policy.workflowHistory = [];
    }
    
    policy.workflowHistory.push({
      status,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: status === 'Published' ? 'Published' : 'Approved',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications for policy publishing
    if (status === 'Published' && req.notificationService) {
      try {
        await req.notificationService.notifyPolicyPublished(policy, req.user);
      } catch (notificationError) {
        console.error('Failed to send policy published notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy status updated successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get policy statistics for dashboard
// @route   GET /api/policies/stats
// @access  Public
const getPolicyStats = async (req, res, next) => {
  try {
    const stats = await Policy.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);

    // Transform to object format
    const statusCounts = {};
    stats.forEach(stat => {
      statusCounts[stat._id] = stat.count;
    });

    // Calculate totals and individual status counts
    const totalPolicies = await Policy.countDocuments();

    // Individual status counts for workflow cards
    const requestInitiated = statusCounts['Request Initiated'] || 0;
    const draft = statusCounts['Draft'] || 0;
    const underReview = statusCounts['Under Review'] || 0;
    const pendingApproval = statusCounts['Pending Approval'] || 0;
    const approved = statusCounts['Approved'] || 0;
    // Legacy calculations for existing cards
    const approvedPolicies = approved;
    const pendingPolicies = requestInitiated + draft + pendingApproval + underReview;
    const rejectedPolicies = 0; // No longer using rejected status

    res.status(200).json({
      success: true,
      data: {
        totalPolicies,
        approvedPolicies,
        pendingPolicies,
        rejectedPolicies,

        // Individual workflow status counts
        requestInitiated,
        draft,
        underReview,
        pendingApproval,
        approved,

        // Complete status breakdown
        statusBreakdown: statusCounts,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Approve policy
// @route   POST /api/policies/:id/approve
// @access  Private (Approvers only)
const approvePolicy = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.status !== 'Pending Approval') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be pending approval to be approved',
      });
    }

    policy.status = 'Approved';

    // Add to approval workflow
    policy.approvalWorkflow.push({
      step: policy.approvalWorkflow.length + 1,
      approver: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      status: 'Approved',
      comments: comments || '',
      actionDate: new Date(),
    });

    await policy.save();

    // Send notifications for policy approval
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyApproval(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send policy approval notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy approved successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reject policy
// @route   POST /api/policies/:id/reject
// @access  Private (Approvers only)
const rejectPolicy = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (!['Pending Approval', 'Draft'].includes(policy.status)) {
      return res.status(400).json({
        success: false,
        message: 'Policy must be pending approval or in draft to be rejected',
      });
    }

    // For rejection, move back to Draft for revision
    policy.status = 'Draft';

    // Add to approval workflow
    policy.approvalWorkflow.push({
      step: policy.approvalWorkflow.length + 1,
      approver: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      status: 'Rejected',
      comments: comments || '',
      actionDate: new Date(),
    });

    await policy.save();

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy rejected successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get policy history/audit trail
// @route   GET /api/policies/:id/history
// @access  Private
const getPolicyHistory = async (req, res, next) => {
  try {
    const policy = await Policy.findById(req.params.id)
      .populate('approvalWorkflow.approver.id', 'name email department')
      .select('approvalWorkflow createdAt updatedAt status');

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    res.status(200).json({
      success: true,
      data: {
        history: policy.approvalWorkflow,
        createdAt: policy.createdAt,
        updatedAt: policy.updatedAt,
        currentStatus: policy.status,
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Governance review of policy request
// @route   POST /api/policies/:id/governance-review
// @access  Private (Governance team only)
const governanceReview = async (req, res, next) => {
  try {
    const { decision, comments } = req.body;

    // Validate user has correct role and department for governance review
    if (req.user.role !== 'Approver' || req.user.department !== 'Governance') {
      if (!['Admin', 'Super Admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only Approvers from Governance department can perform governance review',
        });
      }
    }

    const policy = await Policy.findById(req.params.id);
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.status !== 'Request Initiated') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be in Request Initiated status for governance review',
      });
    }

    // Update governance review
    policy.governanceReview = {
      reviewer: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      reviewDate: new Date(),
      decision,
      comments,
    };

    // Update status based on decision
    if (decision === 'Approved') {
      policy.status = 'Draft';
      policy.detailedStatus = 'Approved by Governance - Ready for drafting';
    } else {
      policy.status = 'Request Initiated';
      policy.detailedStatus = 'Rejected by Governance - Requires revision';
    }

    // Add to workflow history
    policy.workflowHistory.push({
      status: policy.status,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: 'Governance Reviewer',
      },
      action: decision,
      comments,
    });

    await policy.save();

    // Send notification to policy owner
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyGovernanceReview(policy, req.user, decision, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send governance review notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: `Policy ${decision.toLowerCase()} by governance team`,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    GRC review of policy draft
// @route   POST /api/policies/:id/grc-review
// @access  Private (GRC team only)
const grcReview = async (req, res, next) => {
  try {
    const { decision, comments } = req.body;

    // Validate user has correct role and department for GRC review
    if (req.user.role !== 'Reviewer' || req.user.department !== 'GRC') {
      if (!['Admin', 'Super Admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only Reviewers from GRC department can perform GRC review',
        });
      }
    }

    const policy = await Policy.findById(req.params.id);
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.status !== 'Under Review') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be in Under Review status for GRC review',
      });
    }

    // Update GRC review
    policy.grcReview = {
      reviewer: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      reviewDate: new Date(),
      decision,
      comments,
    };

    // Update status based on decision
    if (decision === 'Approved') {
      policy.status = 'Pending Approval';
      policy.detailedStatus = `Approved by GRC - Awaiting ${policy.approvalAuthority} approval`;
    } else {
      policy.status = 'Draft';
      policy.detailedStatus = 'Rejected by GRC - Requires revision';
    }

    // Add to workflow history
    policy.workflowHistory.push({
      status: policy.status,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: 'GRC Reviewer',
      },
      action: decision,
      comments,
    });

    await policy.save();

    // Send notifications for GRC review
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyGRCReview(policy, req.user, decision, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send GRC review notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: `Policy ${decision.toLowerCase()} by GRC team`,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Submit policy for review (Draft → Under Review)
// @route   POST /api/policies/:id/submit-for-review
// @access  Private (Creator only)
const submitPolicyForReview = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if policy is in Draft status
    if (policy.status !== 'Draft') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be in Draft status to submit for review',
      });
    }

    // Check if user is policy owner or has admin privileges
    const isOwner = policy.policyOwner.id.toString() === req.user._id.toString();
    const isAdmin = ['Admin', 'Super Admin'].includes(req.user.role);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Only policy owner or admin can submit policy for review',
      });
    }

    // Check if policy document is attached
    if (!policy.attachments || policy.attachments.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'A policy document must be attached before submitting for review',
      });
    }

    // Update status to Under Review
    policy.status = 'Under Review';
    policy.detailedStatus = 'Submitted for GRC review';

    // Add to workflow history
    if (!policy.workflowHistory) {
      policy.workflowHistory = [];
    }

    policy.workflowHistory.push({
      status: 'Under Review',
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Submitted',
      comments: comments || '',
    });

    await policy.save();

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy submitted for review successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Upload policy attachment
// @route   POST /api/policies/:id/attachments
// @access  Private
const uploadPolicyAttachment = async (req, res, next) => {
  try {
    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if user has permission to upload attachments
    const isOwner = policy.policyOwner.id.toString() === req.user._id.toString();
    const isAdmin = ['Admin', 'Super Admin'].includes(req.user.role);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Only policy owner or admin can upload attachments',
      });
    }

    // Check if policy is in Draft status
    if (policy.status !== 'Draft') {
      return res.status(400).json({
        success: false,
        message: 'Attachments can only be uploaded when policy is in Draft status',
      });
    }

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded',
      });
    }

    // Create attachment object
    const attachment = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      url: `/uploads/policies/${req.file.filename}`, // This would be a full URL in production
      size: req.file.size,
      mimeType: req.file.mimetype,
      uploadedAt: new Date(),
    };

    // Replace existing attachment (only one policy document allowed)
    // If there's an existing attachment, we should ideally delete the old file
    // For now, we'll just replace the attachment in the database
    policy.attachments = [attachment];

    // Save policy
    await policy.save();

    res.status(200).json({
      success: true,
      message: 'Policy document uploaded successfully',
      data: attachment,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Request policy retirement
// @route   POST /api/policies/:id/request-retirement
// @access  Private (Policy Owner, Admin, Super Admin only)
const requestPolicyRetirement = async (req, res, next) => {
  try {
    const { justification, effectiveDate, comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if policy can be retired
    if (!policy.canBeRetired()) {
      return res.status(400).json({
        success: false,
        message: 'Only published policies can be retired',
      });
    }

    // Check if user is policy owner or has admin privileges
    const isOwner = policy.policyOwner.id.toString() === req.user._id.toString();
    const isAdmin = ['Admin', 'Super Admin'].includes(req.user.role);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Only policy owner or admin can request retirement',
      });
    }

    // Update policy with retirement request
    policy.status = 'Retirement Requested';
    policy.detailedStatus = 'Retirement request submitted - awaiting governance review';
    policy.retirementRequestedDate = new Date();

    policy.retirementRequest = {
      requestedBy: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      requestDate: new Date(),
      justification: justification || '',
      effectiveDate: effectiveDate ? new Date(effectiveDate) : null,
    };

    // Add to workflow history
    if (!policy.workflowHistory) {
      policy.workflowHistory = [];
    }

    policy.workflowHistory.push({
      status: 'Retirement Requested',
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Retirement Requested',
      comments: comments || '',
    });

    await policy.save();

    // Send notification to governance reviewers
    if (req.notificationService) {
      try {
        await req.notificationService.notifyPolicyRetirementRequested(policy, req.user);
      } catch (notificationError) {
        console.error('Failed to send retirement request notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy retirement request submitted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Governance review of policy retirement request
// @route   POST /api/policies/:id/retirement-governance-review
// @access  Private (Governance team only)
const retirementGovernanceReview = async (req, res, next) => {
  try {
    const { decision, comments } = req.body;

    // Validate user has correct role and department for governance review
    if (req.user.role !== 'Approver' || req.user.department !== 'Governance') {
      if (!['Admin', 'Super Admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only Approvers from Governance department can perform retirement governance review',
        });
      }
    }

    const policy = await Policy.findById(req.params.id);
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.status !== 'Retirement Requested') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be in Retirement Requested status for governance review',
      });
    }

    // Update retirement governance review
    policy.retirementGovernanceReview = {
      reviewer: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      reviewDate: new Date(),
      decision,
      comments,
    };

    // Update status based on decision
    if (decision === 'Approved') {
      policy.status = 'Retirement Pending Approval';
      policy.detailedStatus = `Approved by Governance - Awaiting ${policy.approvalAuthority} approval for retirement`;
    } else {
      policy.status = 'Published';
      policy.detailedStatus = 'Retirement request rejected by Governance - Policy remains published';
    }

    // Add to workflow history
    policy.workflowHistory.push({
      status: policy.status,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: 'Governance Reviewer',
      },
      action: decision === 'Approved' ? 'Retirement Approved' : 'Retirement Rejected',
      comments,
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyRetirementGovernanceReview(policy, req.user, decision, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send retirement governance review notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: `Policy retirement ${decision.toLowerCase()} by governance team`,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Approve policy retirement
// @route   POST /api/policies/:id/approve-retirement
// @access  Private (Final Approvers only)
const approveRetirement = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.status !== 'Retirement Pending Approval') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be pending retirement approval',
      });
    }

    // Check approval authority based on policy category
    let hasApprovalAuthority = false;
    if (policy.policyCategory === 'Corporate Policies') {
      // Board Committee approval - Admin/Super Admin
      hasApprovalAuthority = ['Admin', 'Super Admin'].includes(req.user.role);
    } else if (policy.policyCategory === 'Operational Policies') {
      // CEO approval - Approver/Admin/Super Admin
      hasApprovalAuthority = ['Approver', 'Admin', 'Super Admin'].includes(req.user.role);
    }

    if (!hasApprovalAuthority) {
      return res.status(403).json({
        success: false,
        message: `You don't have authority to approve retirement for ${policy.policyCategory}`,
      });
    }

    policy.status = 'Retired';
    policy.detailedStatus = 'Policy has been retired and is no longer active';
    policy.retiredDate = new Date();

    // Add to workflow history
    policy.workflowHistory.push({
      status: 'Retired',
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Retired',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyRetired(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send policy retirement notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy retired successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reject policy retirement
// @route   POST /api/policies/:id/reject-retirement
// @access  Private (Final Approvers only)
const rejectRetirement = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.status !== 'Retirement Pending Approval') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be pending retirement approval',
      });
    }

    // Check approval authority based on policy category
    let hasApprovalAuthority = false;
    if (policy.policyCategory === 'Corporate Policies') {
      // Board Committee approval - Admin/Super Admin
      hasApprovalAuthority = ['Admin', 'Super Admin'].includes(req.user.role);
    } else if (policy.policyCategory === 'Operational Policies') {
      // CEO approval - Approver/Admin/Super Admin
      hasApprovalAuthority = ['Approver', 'Admin', 'Super Admin'].includes(req.user.role);
    }

    if (!hasApprovalAuthority) {
      return res.status(403).json({
        success: false,
        message: `You don't have authority to reject retirement for ${policy.policyCategory}`,
      });
    }

    // Return to published status
    policy.status = 'Published';
    policy.detailedStatus = 'Retirement request rejected - Policy remains published and active';

    // Add to workflow history
    policy.workflowHistory.push({
      status: 'Published',
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Retirement Rejected',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyRetirementRejected(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send retirement rejection notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy retirement rejected successfully',
    });
  } catch (error) {
    next(error);
  }
};

// Exception Workflow Controllers

// @desc    Request policy exception
// @route   POST /api/policies/:id/request-exception
// @access  Private (Policy Owner, Admin, Super Admin only)
const requestPolicyException = async (req, res, next) => {
  try {
    const { justification, specificSection, exceptionType, effectiveDate, expiryDate, comments } = req.body;

    // Validate required fields
    if (!justification || !specificSection || !exceptionType) {
      return res.status(400).json({
        success: false,
        message: 'Justification, specific section, and exception type are required'
      });
    }

    // Validate exception type
    if (!['Material Exception', 'Immaterial Exception'].includes(exceptionType)) {
      return res.status(400).json({
        success: false,
        message: 'Exception type must be either Material Exception or Immaterial Exception'
      });
    }

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if policy can have exception requested
    if (!policy.canHaveExceptionRequested()) {
      return res.status(400).json({
        success: false,
        message: 'Exception can only be requested for published policies',
      });
    }

    // Check if user is policy owner or has admin privileges
    const isOwner = policy.policyOwner.id.toString() === req.user._id.toString();
    const isAdmin = ['Admin', 'Super Admin'].includes(req.user.role);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Only policy owner or admin can request exception',
      });
    }

    // Check if there's already an active exception
    if (policy.exceptionStatus && policy.exceptionStatus !== 'None' && policy.exceptionStatus !== 'Rejected' && policy.exceptionStatus !== 'Expired') {
      return res.status(400).json({
        success: false,
        message: 'Policy already has an active exception request'
      });
    }

    // Update policy with exception request (policy status remains Published)
    policy.exceptionStatus = 'Requested';
    policy.detailedStatus = 'Exception request submitted - awaiting governance review';
    policy.exceptionRequestedDate = new Date();

    policy.exceptionRequest = {
      requestedBy: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      requestDate: new Date(),
      justification: justification || '',
      specificSection: specificSection || '',
      exceptionType,
      effectiveDate: effectiveDate ? new Date(effectiveDate) : null,
      expiryDate: expiryDate ? new Date(expiryDate) : null,
      comments: comments || '',
    };

    // Add to workflow history
    if (!policy.workflowHistory) {
      policy.workflowHistory = [];
    }

    policy.workflowHistory.push({
      status: `Published (Exception ${policy.exceptionStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Exception Requested',
      comments: comments || '',
    });

    await policy.save();

    // Send notification to governance reviewers
    if (req.notificationService) {
      try {
        await req.notificationService.notifyPolicyExceptionRequested(policy, req.user);
      } catch (notificationError) {
        console.error('Failed to send exception request notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy exception request submitted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Governance review of policy exception request
// @route   POST /api/policies/:id/exception-governance-review
// @access  Private (Governance team only)
const exceptionGovernanceReview = async (req, res, next) => {
  try {
    const { decision, comments } = req.body;

    // Validate user has correct role and department for governance review
    if (req.user.role !== 'Approver' || req.user.department !== 'Governance') {
      if (!['Admin', 'Super Admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only Approvers from Governance department can perform exception governance review',
        });
      }
    }

    const policy = await Policy.findById(req.params.id);
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.exceptionStatus !== 'Requested') {
      return res.status(400).json({
        success: false,
        message: 'Policy must have exception status Requested for governance review',
      });
    }

    // Update exception governance review
    policy.exceptionGovernanceReview = {
      reviewer: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      reviewDate: new Date(),
      decision,
      comments,
    };

    // Update exception status based on decision (policy status remains Published)
    if (decision === 'Approved') {
      policy.exceptionStatus = 'Pending Approval';
      const approvalAuthority = policy.getExceptionApprovalAuthority();
      policy.detailedStatus = `Approved by Governance - Awaiting ${approvalAuthority} approval for exception`;
    } else {
      policy.exceptionStatus = 'Rejected';
      policy.exceptionRejectedDate = new Date();
      policy.detailedStatus = 'Exception request rejected by Governance - Policy remains published';
    }

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Exception ${policy.exceptionStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: 'Governance Reviewer',
      },
      action: decision === 'Approved' ? 'Exception Approved' : 'Exception Rejected',
      comments,
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyExceptionGovernanceReview(policy, req.user, decision, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send exception governance review notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: `Policy exception ${decision.toLowerCase()} by governance team`,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Approve policy exception
// @route   POST /api/policies/:id/approve-exception
// @access  Private (Final Approvers only)
const approveException = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.exceptionStatus !== 'Pending Approval') {
      return res.status(400).json({
        success: false,
        message: 'Policy must have exception status Pending Approval',
      });
    }

    // Check approval authority based on exception type
    let hasApprovalAuthority = false;
    if (policy.exceptionRequest?.exceptionType === 'Material Exception') {
      // Board Committee approval - Admin/Super Admin
      hasApprovalAuthority = ['Admin', 'Super Admin'].includes(req.user.role);
    } else if (policy.exceptionRequest?.exceptionType === 'Immaterial Exception') {
      // CEO approval - Approver/Admin/Super Admin
      hasApprovalAuthority = ['Approver', 'Admin', 'Super Admin'].includes(req.user.role);
    }

    if (!hasApprovalAuthority) {
      return res.status(403).json({
        success: false,
        message: `You don't have authority to approve ${policy.exceptionRequest?.exceptionType}`,
      });
    }

    policy.exceptionStatus = 'Approved';
    policy.detailedStatus = 'Exception has been approved and is now active';
    policy.exceptionApprovedDate = new Date();

    // Update exception approval
    policy.exceptionApproval = {
      approver: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      approvalDate: new Date(),
      decision: 'Approved',
      comments: comments || '',
    };

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Exception ${policy.exceptionStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Exception Approved',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyExceptionApproved(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send exception approval notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy exception approved successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reject policy exception
// @route   POST /api/policies/:id/reject-exception
// @access  Private (Final Approvers only)
const rejectException = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (policy.exceptionStatus !== 'Pending Approval') {
      return res.status(400).json({
        success: false,
        message: 'Policy must have exception status Pending Approval',
      });
    }

    // Check approval authority based on exception type
    let hasApprovalAuthority = false;
    if (policy.exceptionRequest?.exceptionType === 'Material Exception') {
      // Board Committee approval - Admin/Super Admin
      hasApprovalAuthority = ['Admin', 'Super Admin'].includes(req.user.role);
    } else if (policy.exceptionRequest?.exceptionType === 'Immaterial Exception') {
      // CEO approval - Approver/Admin/Super Admin
      hasApprovalAuthority = ['Approver', 'Admin', 'Super Admin'].includes(req.user.role);
    }

    if (!hasApprovalAuthority) {
      return res.status(403).json({
        success: false,
        message: `You don't have authority to reject ${policy.exceptionRequest?.exceptionType}`,
      });
    }

    // Update exception status to rejected (policy status remains Published)
    policy.exceptionStatus = 'Rejected';
    policy.detailedStatus = 'Exception request rejected - Policy remains published without exception';
    policy.exceptionRejectedDate = new Date();

    // Update exception approval
    policy.exceptionApproval = {
      approver: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      approvalDate: new Date(),
      decision: 'Rejected',
      comments: comments || '',
    };

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Exception ${policy.exceptionStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Exception Rejected',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyExceptionRejected(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send exception rejection notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy exception rejected successfully',
    });
  } catch (error) {
    next(error);
  }
};

// Review Workflow Controllers

// @desc    Initiate policy review
// @route   POST /api/policies/:id/initiate-review
// @access  Private (System or Admin only)
const initiatePolicyReview = async (req, res, next) => {
  try {
    const { reviewType = 'Scheduled Review', comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if policy can have review initiated
    if (!policy.canHaveReviewInitiated()) {
      return res.status(400).json({
        success: false,
        message: 'Review can only be initiated for published policies with no active review',
      });
    }

    // Check if user has permission to initiate review
    const isOwner = policy.policyOwner.id.toString() === req.user._id.toString();
    const isAdmin = ['Admin', 'Super Admin'].includes(req.user.role);
    const isGovernanceReviewer = req.user.role === 'Approver' && req.user.department === 'Governance';

    if (!isOwner && !isAdmin && !isGovernanceReviewer) {
      return res.status(403).json({
        success: false,
        message: 'Only policy owner, governance reviewers, or admin can initiate policy review',
      });
    }

    // Update policy with review initiation
    policy.reviewStatus = 'In Progress';
    policy.reviewInitiatedDate = new Date();

    policy.reviewRequest = {
      initiatedBy: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      initiatedDate: new Date(),
      reviewType,
      comments: comments || '',
    };

    // Add to workflow history
    if (!policy.workflowHistory) {
      policy.workflowHistory = [];
    }

    policy.workflowHistory.push({
      status: `Published (Review ${policy.reviewStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Review Initiated',
      comments: comments || '',
    });

    await policy.save();

    // Send notification to policy owner
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyReviewInitiated(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send review initiation notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy review initiated successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Policy owner review
// @route   POST /api/policies/:id/owner-review
// @access  Private (Policy Owner, Admin, Super Admin only)
const ownerReview = async (req, res, next) => {
  try {
    const { decision, comments, changesDescription } = req.body;

    // Validate required fields
    if (!decision || !['No Updates Required', 'Updates Required'].includes(decision)) {
      return res.status(400).json({
        success: false,
        message: 'Decision is required and must be either "No Updates Required" or "Updates Required"'
      });
    }

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    // Check if policy needs owner review
    if (!policy.needsOwnerReview()) {
      return res.status(400).json({
        success: false,
        message: 'Policy is not in a state that requires owner review',
      });
    }

    // Check if user is policy owner or has admin privileges
    const isOwner = policy.policyOwner.id.toString() === req.user._id.toString();
    const isAdmin = ['Admin', 'Super Admin'].includes(req.user.role);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Only policy owner or admin can perform owner review',
      });
    }

    // Update owner review
    policy.ownerReview = {
      reviewer: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      reviewDate: new Date(),
      decision,
      updatesRequired: decision === 'Updates Required',
      comments: comments || '',
      changesDescription: changesDescription || '',
    };

    // Update review status based on decision
    if (decision === 'No Updates Required') {
      policy.reviewStatus = 'Under Review';
      policy.detailedStatus = 'Owner review completed - No updates required, awaiting governance review';
    } else {
      policy.reviewStatus = 'Under Review';
      policy.detailedStatus = 'Owner review completed - Updates required, awaiting governance review';
    }

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Review ${policy.reviewStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: 'Policy Owner',
      },
      action: 'Owner Review Completed',
      comments: `${decision}. ${comments || ''}`,
    });

    await policy.save();

    // Send notifications to governance reviewers
    if (req.notificationService) {
      try {
        await req.notificationService.notifyPolicyOwnerReviewCompleted(policy, req.user, decision);
      } catch (notificationError) {
        console.error('Failed to send owner review notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Owner review completed successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Governance review of policy review
// @route   POST /api/policies/:id/review-governance-review
// @access  Private (Governance team only)
const reviewGovernanceReview = async (req, res, next) => {
  try {
    const { decision, comments } = req.body;

    // Validate user has correct role and department for governance review
    if (req.user.role !== 'Approver' || req.user.department !== 'Governance') {
      if (!['Admin', 'Super Admin'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only Approvers from Governance department can perform review governance review',
        });
      }
    }

    const policy = await Policy.findById(req.params.id);
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (!policy.needsReviewGovernanceReview()) {
      return res.status(400).json({
        success: false,
        message: 'Policy is not in a state that requires review governance review',
      });
    }

    // Update review governance review
    policy.reviewGovernanceReview = {
      reviewer: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      reviewDate: new Date(),
      decision,
      comments,
    };

    // Update review status based on decision
    if (decision === 'Approved') {
      policy.reviewStatus = 'Pending Approval';
      const approvalAuthority = policy.getReviewApprovalAuthority();
      policy.detailedStatus = `Approved by Governance - Awaiting ${approvalAuthority} approval for review completion`;
    } else {
      policy.reviewStatus = 'In Progress';
      policy.detailedStatus = 'Review rejected by Governance - Requires policy owner action';
    }

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Review ${policy.reviewStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: 'Governance Reviewer',
      },
      action: decision === 'Approved' ? 'Review Governance Approved' : 'Review Governance Rejected',
      comments,
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyReviewGovernanceReview(policy, req.user, decision, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send review governance review notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: `Policy review ${decision.toLowerCase()} by governance team`,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Approve policy review
// @route   POST /api/policies/:id/approve-review
// @access  Private (Final Approvers only)
const approveReview = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (!policy.canBeApprovedForReview()) {
      return res.status(400).json({
        success: false,
        message: 'Policy review is not pending approval',
      });
    }

    // Check approval authority based on policy category
    let hasApprovalAuthority = false;
    if (policy.policyCategory === 'Corporate Policies') {
      // Board Committee approval - Admin/Super Admin
      hasApprovalAuthority = ['Admin', 'Super Admin'].includes(req.user.role);
    } else if (policy.policyCategory === 'Operational Policies') {
      // CEO approval - Approver/Admin/Super Admin
      hasApprovalAuthority = ['Approver', 'Admin', 'Super Admin'].includes(req.user.role);
    }

    if (!hasApprovalAuthority) {
      return res.status(403).json({
        success: false,
        message: `You don't have authority to approve review for ${policy.policyCategory}`,
      });
    }

    policy.reviewStatus = 'Approved';
    policy.detailedStatus = 'Policy review has been completed and approved';
    policy.reviewCompletedDate = new Date();
    policy.previousReviewDate = policy.lastReviewDate || policy.publishedDate;
    policy.lastReviewDate = new Date();
    policy.nextReviewDate = policy.calculateNextReviewDate();

    // Update review approval
    policy.reviewApproval = {
      approver: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      approvalDate: new Date(),
      decision: 'Approved',
      comments: comments || '',
    };

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Review ${policy.reviewStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Review Approved',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyReviewApproved(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send review approval notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy review approved successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reject policy review
// @route   POST /api/policies/:id/reject-review
// @access  Private (Final Approvers only)
const rejectReview = async (req, res, next) => {
  try {
    const { comments } = req.body;

    const policy = await Policy.findById(req.params.id);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found',
      });
    }

    if (!policy.canBeApprovedForReview()) {
      return res.status(400).json({
        success: false,
        message: 'Policy review is not pending approval',
      });
    }

    // Check approval authority based on policy category
    let hasApprovalAuthority = false;
    if (policy.policyCategory === 'Corporate Policies') {
      // Board Committee approval - Admin/Super Admin
      hasApprovalAuthority = ['Admin', 'Super Admin'].includes(req.user.role);
    } else if (policy.policyCategory === 'Operational Policies') {
      // CEO approval - Approver/Admin/Super Admin
      hasApprovalAuthority = ['Approver', 'Admin', 'Super Admin'].includes(req.user.role);
    }

    if (!hasApprovalAuthority) {
      return res.status(403).json({
        success: false,
        message: `You don't have authority to reject review for ${policy.policyCategory}`,
      });
    }

    // Update review status to rejected
    policy.reviewStatus = 'Rejected';
    policy.detailedStatus = 'Policy review rejected - Requires policy owner action';

    // Update review approval
    policy.reviewApproval = {
      approver: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      approvalDate: new Date(),
      decision: 'Rejected',
      comments: comments || '',
    };

    // Add to workflow history
    policy.workflowHistory.push({
      status: `Published (Review ${policy.reviewStatus})`,
      timestamp: new Date(),
      actor: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
      },
      action: 'Review Rejected',
      comments: comments || '',
    });

    await policy.save();

    // Send notifications
    if (req.notificationService) {
      try {
        const policyOwner = await User.findById(policy.policyOwner.id);
        if (policyOwner) {
          await req.notificationService.notifyPolicyReviewRejected(policy, req.user, policyOwner);
        }
      } catch (notificationError) {
        console.error('Failed to send review rejection notifications:', notificationError);
      }
    }

    res.status(200).json({
      success: true,
      data: policy,
      message: 'Policy review rejected successfully',
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllPolicies,
  getPolicyById,
  requestPolicyInitiation,
  updatePolicy,
  deletePolicy,
  updatePolicyStatus,
  getPolicyStats,
  approvePolicy,
  rejectPolicy,
  getPolicyHistory,
  governanceReview,
  grcReview,
  submitPolicyForReview,
  uploadPolicyAttachment,
  requestPolicyRetirement,
  retirementGovernanceReview,
  approveRetirement,
  rejectRetirement,
  requestPolicyException,
  exceptionGovernanceReview,
  approveException,
  rejectException,
  // Review workflow controllers
  initiatePolicyReview,
  ownerReview,
  reviewGovernanceReview,
  approveReview,
  rejectReview,
};


