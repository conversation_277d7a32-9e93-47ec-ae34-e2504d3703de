'use client';

import { useState, useEffect } from 'react';
import { DataTable } from './datatable';
import { columns } from './column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyRequestModal } from '@/components/PolicyRequestModal';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';
import {
  RotateCcw,
  FileText,
  Search,
  Clock,
  CheckCircle,
  BarChart3,
} from 'lucide-react';

const Manage = () => {
  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch policies manually after authentication is confirmed
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    requestPolicyInitiation,
    updatePolicyStatus,
    governanceReview,
    grcReview,
    submitPolicyForReview,
    approvePolicy,
    rejectPolicy,
    requestRetirement,
    retirementGovernanceReview,
    approveRetirement,
    rejectRetirement,
  } = usePolicies({ autoFetch: false, management: true });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Update selected policy when policies data changes (for real-time updates)
  useEffect(() => {
    if (selectedPolicy && policies.length > 0) {
      const updatedPolicy = policies.find((p) => p._id === selectedPolicy._id);
      if (updatedPolicy) {
        setSelectedPolicy(updatedPolicy);
      }
    }
  }, [policies, selectedPolicy]);

  // Debug logging to understand the data mismatch
  useEffect(() => {
    if (policies.length > 0 && metadata) {
      console.log('=== POLICY DATA DEBUG ===');
      console.log('Total policies in table:', policies.length);
      console.log(
        'Policies by status (actual):',
        policies.reduce(
          (acc, policy) => {
            acc[policy.status] = (acc[policy.status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        ),
      );
      console.log('Metadata totalStatusCounts:', metadata.totalStatusCounts);
      console.log('Metadata statusCounts (filtered):', metadata.statusCounts);
      console.log('Metadata totalPoliciesCount:', metadata.totalPoliciesCount);

      // Check for mismatches
      const actualTotal = policies.length;
      const metadataTotal = metadata.totalPoliciesCount;
      const statusCountsTotal = Object.values(
        metadata.totalStatusCounts || {},
      ).reduce((sum, count) => sum + count, 0);

      console.log('COMPARISON:');
      console.log('- Actual policies displayed:', actualTotal);
      console.log('- Metadata total count:', metadataTotal);
      console.log('- Sum of status counts:', statusCountsTotal);
      console.log(
        '- Match?',
        actualTotal === metadataTotal && actualTotal === statusCountsTotal,
      );
      console.log('========================');
    }
  }, [policies, metadata]);

  // Handle policy request submission
  const handlePolicyRequest = async (policyData: {
    name: string;
    description?: string;
    documentName?: string;
    documentCode?: string;
    documentType?: string;
    version?: string;
    policyCategory: 'Corporate Policies' | 'Operational Policies';
    policyType?: string;
    categories: string[];
    department: string;
    subDepartment?: string;
    priorityScore?: number;
    classification?: string;
    priority?: string;
    dueDate?: string;
    startDate?: string;
    nextReviewDate?: string;
  }) => {
    try {
      const newPolicy = await requestPolicyInitiation(policyData);
      if (newPolicy) {
        toast({
          title: 'Success',
          description: 'Policy request initiated successfully',
        });
        // Refresh policies and metadata after creating new policy
        refetchPolicies();
        setIsRequestModalOpen(false);
      }
    } catch (error) {
      console.error('Error requesting policy:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to initiate policy request';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      // Don't close the modal on error so user can fix the issue
    }
  };

  // Handle policy row click to open details modal
  const handlePolicyClick = (policy: Policy) => {
    setSelectedPolicy(policy);
    setIsDetailsModalOpen(true);
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle policy update (refresh policy data and update selected policy)
  const handlePolicyUpdate = () => {
    refetchPolicies();
    // If a policy is currently selected, update it with the latest data
    if (selectedPolicy) {
      // The policy will be updated when refetchPolicies completes and policies state updates
      // We'll use useEffect to update selectedPolicy when policies change
    }
  };

  // Handle status update from details modal
  const handleStatusUpdate = async (
    policyId: string,
    newStatus: string,
    comments?: string,
    effectiveDate?: string,
    selectedGroups?: string[],
  ): Promise<boolean> => {
    const result = await updatePolicyStatus(
      policyId,
      newStatus,
      comments,
      effectiveDate,
      selectedGroups,
    );
    // Refresh data after status update
    refetchPolicies();
    return result !== null;
  };

  // Handle governance review from details modal
  const handleGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await governanceReview(policyId, decision, comments);
    // Refresh data after governance review
    refetchPolicies();
    return result;
  };

  // Handle GRC review from details modal
  const handleGrcReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await grcReview(policyId, decision, comments);
    // Refresh data after GRC review
    refetchPolicies();
    return result;
  };

  // Handle submit for review from details modal
  const handleSubmitForReview = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await submitPolicyForReview(policyId, comments);
    // Refresh data after submission
    refetchPolicies();
    return result;
  };

  // Handle approve policy from details modal
  const handleApprovePolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approvePolicy(policyId, comments);
    // Refresh data after approval
    refetchPolicies();
    return result;
  };

  // Handle reject policy from details modal
  const handleRejectPolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectPolicy(policyId, comments);
    // Refresh data after rejection
    refetchPolicies();
    return result;
  };

  // Handle request retirement from details modal
  const handleRequestRetirement = async (
    policyId: string,
    justification: string,
    effectiveDate?: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await requestRetirement(
      policyId,
      justification,
      effectiveDate,
      comments,
    );
    // Refresh data after retirement request
    refetchPolicies();
    return result;
  };

  // Handle retirement governance review from details modal
  const handleRetirementGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await retirementGovernanceReview(
      policyId,
      decision,
      comments,
    );
    // Refresh data after retirement governance review
    refetchPolicies();
    return result;
  };

  // Handle approve retirement from details modal
  const handleApproveRetirement = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveRetirement(policyId, comments);
    // Refresh data after retirement approval
    refetchPolicies();
    return result;
  };

  // Handle reject retirement from details modal
  const handleRejectRetirement = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectRetirement(policyId, comments);
    // Refresh data after retirement rejection
    refetchPolicies();
    return result;
  };

  // Transform API data to match new table structure with all metadata fields
  const transformedPolicies = policies.map((policy, index) => ({
    id: index,
    policyId: policy.policyId,
    documentCode: policy.documentCode || '',
    name: policy.name,
    documentName: policy.documentName || policy.name,
    documentType: policy.documentType || 'Policy',
    version: policy.version || '1.0',
    versionNumber: policy.versionNumber || 1.0,
    policyType: policy.policyType || 'Corporate',
    categories: policy.categories.join(', '),
    department: policy.department,
    subDepartment: policy.subDepartment || '',
    policyOwner: policy.policyOwner.name,
    priorityScore: policy.priorityScore || 5,
    status: policy.status,
    detailedStatus: policy.detailedStatus || '',
    classification: policy.classification || 'Internal',
    startDate: policy.startDate || '',
    dueDate: policy.dueDate || '',
    completionDate: policy.completionDate || '',
    approvalDate: policy.approvalDate || '',
    authorizedApprover: policy.authorizedApprover?.name || '',
    lastReviewDate: policy.lastReviewDate || '',
    nextReviewDate: policy.nextReviewDate || '',
    initiatedBy: policy.initiatedBy?.name || '',
    reviewedBy: policy.reviewedBy?.map((r) => r.name).join(', ') || '',
    endorsedBy: policy.endorsedBy?.map((e) => e.name).join(', ') || '',
  }));

  // Note: We're using metadata.totalStatusCounts for the cards since we're in management mode

  // Focus on policy creation workflow statuses only
  // Removed Published, Under Review, and Archived as they are post-creation statuses
  const workflowStatusCards = [
    {
      title: 'Request Initiated',
      number: (metadata?.totalStatusCounts?.requestInitiated || 0).toString(),
      icon: RotateCcw,
      borderColor: 'border-l-blue-500',
      numberColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Policies awaiting governance review',
    },
    {
      title: 'Draft',
      number: (metadata?.totalStatusCounts?.draft || 0).toString(),
      icon: FileText,
      borderColor: 'border-l-cyan-500',
      numberColor: 'text-cyan-600',
      bgColor: 'bg-cyan-50',
      description: 'Being drafted by policy owners',
    },
    {
      title: 'Under Review',
      number: (metadata?.totalStatusCounts?.underReview || 0).toString(),
      icon: Search,
      borderColor: 'border-l-orange-500',
      numberColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Submitted for GRC review',
    },
    {
      title: 'Pending Approval',
      number: (metadata?.totalStatusCounts?.pendingApproval || 0).toString(),
      icon: Clock,
      borderColor: 'border-l-yellow-500',
      numberColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: 'Awaiting final approval',
    },
    {
      title: 'Approved',
      number: (metadata?.totalStatusCounts?.approved || 0).toString(),
      icon: CheckCircle,
      borderColor: 'border-l-green-500',
      numberColor: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Approved and ready for publication',
    },
    // {
    //   title: 'Under Annual Review',
    //   number: (metadata?.totalStatusCounts?.underAnnualReview || 0).toString(),
    //   icon: Calendar,
    //   borderColor: 'border-l-purple-500',
    //   numberColor: 'text-purple-600',
    //   bgColor: 'bg-purple-50',
    //   description: 'Undergoing annual review cycle',
    // },
  ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading policy management data..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="w-full bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Policies"
          message="We couldn't load the policy data. Please try again or contact support if the problem persists."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="w-full bg-gray-100 py-2">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">
              Policy Management
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Create, manage, and track policies through their lifecycle
            </p>
          </div>
          {/* Only show Request New Policy button for Creators, Admins, and Super Admins */}
          {user && ['Creator', 'Admin', 'Super Admin'].includes(user.role) && (
            <Button
              onClick={() => setIsRequestModalOpen(true)}
              className="bg-customBlue hover:bg-customBlueHover"
            >
              Request New Policy
            </Button>
          )}
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-customBlue bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-customBlue">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-6 w-6" />
                  <span>Policy Management Overview</span>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-3xl font-bold text-customBlue">
                    {policiesLoading ? '...' : policies.length}
                  </div>
                  <div className="text-sm text-gray-500">
                    of {metadata?.totalPoliciesCount || '0'} total
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Policies displayed in current view. Total database count:{' '}
                {metadata?.totalPoliciesCount || '0'}
              </p>
              {policies.length !== (metadata?.totalPoliciesCount || 0) && (
                <p className="mt-1 text-xs text-orange-600">
                  ⚠️ Note: Displayed count differs from database total. Check
                  filters or data consistency.
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Workflow Status Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Workflow Overview
          </h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {workflowStatusCards.map((card, index) => (
              <Card
                key={index}
                className={`${card.borderColor} cursor-pointer border-l-4 transition-shadow hover:shadow-md ${card.bgColor}`}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                    <div className="flex items-center space-x-2">
                      <card.icon className="h-4 w-4" />
                      <span className="truncate">{card.title}</span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-col space-y-2">
                    <p className={`text-2xl font-bold ${card.numberColor}`}>
                      {policiesLoading ? '...' : card.number}
                    </p>
                    <p className="text-xs leading-tight text-gray-500">
                      {card.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedPolicies}
            loading={policiesLoading}
            onRowClick={(row) => {
              // Find the original policy from the transformed data
              const originalPolicy = policies.find(
                (p) => p._id === row.policyId || p.policyId === row.policyId,
              );
              if (originalPolicy) {
                handlePolicyClick(originalPolicy);
              }
            }}
          />
        </div>
      </div>

      <PolicyRequestModal
        isOpen={isRequestModalOpen}
        onClose={() => setIsRequestModalOpen(false)}
        onSubmit={handlePolicyRequest}
        loading={policiesLoading}
      />

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={handleStatusUpdate}
        onGovernanceReview={handleGovernanceReview}
        onGrcReview={handleGrcReview}
        onSubmitForReview={handleSubmitForReview}
        onApprovePolicy={handleApprovePolicy}
        onRejectPolicy={handleRejectPolicy}
        onRequestRetirement={handleRequestRetirement}
        onRetirementGovernanceReview={handleRetirementGovernanceReview}
        onApproveRetirement={handleApproveRetirement}
        onRejectRetirement={handleRejectRetirement}
        onPolicyUpdate={handlePolicyUpdate}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default Manage;
