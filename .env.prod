# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
# Direct connection string (bypasses SRV DNS lookup)
#MONGODB_URI=mongodb://pmettempas:<EMAIL>:27017,ac-wvlzglh-shard-00-01.ftzd11w.mongodb.net:27017,ac-wvlzglh-shard-00-02.ftzd11w.mongodb.net:27017/grc-web-app?ssl=true&authSource=admin&replicaSet=atlas-fpdzss-shard-0&retryWrites=true&w=majority

# Database Configuration
MONGODB_URI=mongodb+srv://anurag98cs:<EMAIL>/grc-web-app?retryWrites=true&w=majority&appName=Cluster0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d

# Frontend Configuration
#FRONTEND_URL=https://grc-v4.autoresilience.com
FRONTEND_URL=http://localhost:3000  

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
