'use client';

import { useState, useEffect } from 'react';
import { DataTable } from './datatable';
import { columns, RetirementPrograms } from './column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyRetirementModal } from '@/components/PolicyRetirementModal';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';
import { Archive, FileX, Clock, BarChart3 } from 'lucide-react';

const Retirement = () => {
  const [isRetirementModalOpen, setIsRetirementModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch policies with retirement-related statuses
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    requestRetirement,
    retirementGovernanceReview,
    approveRetirement,
    rejectRetirement,
  } = usePolicies({
    autoFetch: false,
    management: true,
    // Filter for published policies and retirement-related statuses
    status: undefined, // We'll filter in the component
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Update selected policy when policies data changes (for real-time updates)
  useEffect(() => {
    if (selectedPolicy && policies.length > 0) {
      const updatedPolicy = policies.find((p) => p._id === selectedPolicy._id);
      if (updatedPolicy) {
        setSelectedPolicy(updatedPolicy);
      }
    }
  }, [policies, selectedPolicy]);

  // Filter policies for retirement workflow (Retirement statuses only, excluding Published)
  const retirementPolicies = policies.filter((policy) =>
    ['Retirement Requested', 'Retirement Pending Approval', 'Retired'].includes(
      policy.status,
    ),
  );

  // Handle policy retirement request submission
  const handleRetirementRequest = async (retirementData: {
    policyId: string;
    justification: string;
    effectiveDate?: string;
    comments?: string;
  }) => {
    try {
      const success = await requestRetirement(
        retirementData.policyId,
        retirementData.justification,
        retirementData.effectiveDate,
        retirementData.comments,
      );

      if (success) {
        toast({
          title: 'Success',
          description: 'Policy retirement request submitted successfully',
        });
        // Refresh policies after retirement request
        refetchPolicies();
        setIsRetirementModalOpen(false);
      } else {
        // Handle case where requestRetirement returns false
        toast({
          title: 'Error',
          description: 'Failed to submit retirement request. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error requesting retirement:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to submit retirement request';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle policy row click to open details modal
  const handlePolicyClick = (policy: Policy) => {
    setSelectedPolicy(policy);
    setIsDetailsModalOpen(true);
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle policy update (refresh policy data and update selected policy)
  const handlePolicyUpdate = () => {
    refetchPolicies();
  };

  // Handle retirement governance review from details modal
  const handleRetirementGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await retirementGovernanceReview(
      policyId,
      decision,
      comments,
    );
    refetchPolicies();
    return result;
  };

  // Handle approve retirement from details modal
  const handleApproveRetirement = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approveRetirement(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle reject retirement from details modal
  const handleRejectRetirement = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectRetirement(policyId, comments);
    refetchPolicies();
    return result;
  };

  // Handle request retirement from details modal
  const handleRequestRetirement = async (
    policyId: string,
    justification: string,
    effectiveDate?: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await requestRetirement(
      policyId,
      justification,
      effectiveDate,
      comments,
    );
    refetchPolicies();
    return result;
  };

  // Transform API data to match table structure
  const transformedPolicies = retirementPolicies.map((policy, index) => ({
    id: index,
    policyId: policy.policyId,
    documentCode: policy.documentCode || '',
    name: policy.name,
    documentName: policy.documentName || policy.name,
    documentType: policy.documentType || 'Policy',
    version: policy.version || '1.0',
    policyType: policy.policyType || 'Corporate',
    categories: policy.categories.join(', '),
    department: policy.department,
    subDepartment: policy.subDepartment || '',
    policyOwner: policy.policyOwner.name,
    priorityScore: policy.priorityScore || 5,
    status: policy.status,
    detailedStatus: policy.detailedStatus || '',
    classification: policy.classification || 'Internal',
    publishedDate: policy.publishedDate || '',
    retirementRequestedDate: policy.retirementRequestedDate || '',
    retiredDate: policy.retiredDate || '',
    retirementJustification: policy.retirementRequest?.justification || '',
    retirementEffectiveDate: policy.retirementRequest?.effectiveDate || '',
    retirementRequestedBy: policy.retirementRequest?.requestedBy?.name || '',
  }));

  // Retirement workflow status cards (excluding Published status)
  const retirementStatusCards = [
    {
      title: 'Retirement Requested',
      number: retirementPolicies
        .filter((p) => p.status === 'Retirement Requested')
        .length.toString(),
      icon: FileX,
      borderColor: 'border-l-orange-500',
      numberColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Policies with pending retirement requests',
    },
    {
      title: 'Retirement Pending Approval',
      number: retirementPolicies
        .filter((p) => p.status === 'Retirement Pending Approval')
        .length.toString(),
      icon: Clock,
      borderColor: 'border-l-yellow-500',
      numberColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: 'Retirement requests awaiting final approval',
    },
    {
      title: 'Retired',
      number: retirementPolicies
        .filter((p) => p.status === 'Retired')
        .length.toString(),
      icon: Archive,
      borderColor: 'border-l-gray-500',
      numberColor: 'text-gray-600',
      bgColor: 'bg-gray-50',
      description: 'Successfully retired policies',
    },
  ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading policy retirement data..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="w-full bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Policies"
          message="We couldn't load the policy retirement data. Please try again or contact support if the problem persists."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="w-full bg-gray-100 py-2">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">
              Policy Retirement
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage policy retirement requests and track retirement workflow
            </p>
          </div>
          {/* Only show Request Retirement button for policy owners, admins, and super admins */}
          {user && ['Creator', 'Admin', 'Super Admin'].includes(user.role) && (
            <Button
              onClick={() => setIsRetirementModalOpen(true)}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Request Policy Retirement
            </Button>
          )}
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-red-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-orange-700">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-6 w-6" />
                  <span>Policy Retirement Overview</span>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-3xl font-bold text-orange-700">
                    {policiesLoading ? '...' : retirementPolicies.length}
                  </div>
                  <div className="text-sm text-gray-500">
                    retirement-related policies
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Policies currently in the retirement workflow
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Retirement Status Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Retirement Workflow Overview
          </h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {retirementStatusCards.map((card, index) => (
              <Card
                key={index}
                className={`${card.borderColor} cursor-pointer border-l-4 transition-shadow hover:shadow-md ${card.bgColor}`}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                    <div className="flex items-center space-x-2">
                      <card.icon className="h-4 w-4" />
                      <span className="truncate">{card.title}</span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-col space-y-2">
                    <p className={`text-2xl font-bold ${card.numberColor}`}>
                      {policiesLoading ? '...' : card.number}
                    </p>
                    <p className="text-xs leading-tight text-gray-500">
                      {card.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedPolicies}
            loading={policiesLoading}
            onRowClick={(row: RetirementPrograms) => {
              // Find the original policy from the transformed data
              const originalPolicy = retirementPolicies.find(
                (p) => p._id === row.policyId || p.policyId === row.policyId,
              );
              if (originalPolicy) {
                handlePolicyClick(originalPolicy);
              }
            }}
          />
        </div>
      </div>

      <PolicyRetirementModal
        isOpen={isRetirementModalOpen}
        onClose={() => setIsRetirementModalOpen(false)}
        onSubmit={handleRetirementRequest}
        loading={policiesLoading}
        publishedPolicies={policies.filter((p) => p.status === 'Published')}
      />

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={() => Promise.resolve(false)} // Not used in retirement workflow
        onGovernanceReview={() => Promise.resolve(false)} // Not used in retirement workflow
        onGrcReview={() => Promise.resolve(false)} // Not used in retirement workflow
        onRequestRetirement={handleRequestRetirement}
        onRetirementGovernanceReview={handleRetirementGovernanceReview}
        onApproveRetirement={handleApproveRetirement}
        onRejectRetirement={handleRejectRetirement}
        onPolicyUpdate={handlePolicyUpdate}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default Retirement;
