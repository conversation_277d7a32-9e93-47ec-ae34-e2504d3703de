'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

// Define the window interface for OnlyOffice
declare global {
  interface Window {
    DocsAPI: {
      DocEditor: new (
        id: string,
        config: any,
      ) => {
        destroyEditor: () => void;
      };
    };
  }
}

export default function EditorPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [config, setConfig] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [policyName, setPolicyName] = useState<string>('');
  const policyId = params.policyId as string;

  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/onlyoffice/config/${policyId}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
            },
          },
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || 'Failed to load editor configuration',
          );
        }

        const data = await response.json();
        setConfig(data.data);
        setPolicyName(data.data.document.title || 'Document');
      } catch (err) {
        console.error('Error loading editor config:', err);
        setError(err instanceof Error ? err.message : 'Failed to load editor');
        toast({
          title: 'Error',
          description:
            err instanceof Error ? err.message : 'Failed to load editor',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, [policyId, toast]);

  useEffect(() => {
    // Load OnlyOffice API script
    const script = document.createElement('script');
    const onlyOfficeUrl =
      process.env.NEXT_PUBLIC_ONLYOFFICE_URL || 'http://localhost:8080';
    script.src = `${onlyOfficeUrl}/web-apps/apps/api/documents/api.js`;
    script.async = true;
    script.onload = () => {
      console.log('OnlyOffice API script loaded successfully');
    };

    script.onerror = (error) => {
      console.error('Failed to load OnlyOffice API script:', error);
      setError('Failed to load OnlyOffice API script');
    };
    document.body.appendChild(script);

    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, []);

  useEffect(() => {
    // Initialize editor when config is loaded and API is available
    if (config && window.DocsAPI) {
      const docEditor = new window.DocsAPI.DocEditor(
        'onlyoffice-editor',
        config,
      );

      return () => {
        if (docEditor && typeof docEditor.destroyEditor === 'function') {
          docEditor.destroyEditor();
        }
      };
    }
  }, [config]);

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
        <p className="ml-2">Loading editor...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
          <p className="font-bold">Error loading editor</p>
          <p>{error}</p>
        </div>
        <Button className="mt-4" onClick={handleBack}>
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col">
      <header className="flex items-center justify-between border-b bg-white p-4">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-2">
            <ArrowLeft className="mr-1 h-4 w-4" />
            Back to Policy
          </Button>
          <h1 className="text-xl font-semibold">{policyName}</h1>
        </div>
      </header>
      <div id="onlyoffice-editor" className="flex-1"></div>
    </div>
  );
}
