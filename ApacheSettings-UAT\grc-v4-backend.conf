<VirtualHost *:443>
    ServerName grc-temp.autoresilience.com
    ServerAlias autoresilience.com

    SSLEngine on
    SSLCertificateFile /etc/apache2/certificates/certificate.crt
    SSLCertificateKeyFile /etc/apache2/certificates/private.key
    SSLCACertificateFile /etc/apache2/certificates/ca_bundle.crt

    ProxyPreserveHost On
    ProxyRequests Off
    AllowEncodedSlashes NoDecode

    <Proxy *>
        Require all granted
    </Proxy>

    # --- WebSocket Proxy for App (Socket.IO) ---
    # Map frontend /app/socket.io to backend /socket.io
    ProxyPass "/app/socket.io/" "ws://127.0.0.1:5000/socket.io/"
    ProxyPassReverse "/app/socket.io/" "ws://127.0.0.1:5000/socket.io/"

    # Fallback for HTTP long polling
    ProxyPass "/app/socket.io/" "http://127.0.0.1:5000/socket.io/"
    ProxyPassReverse "/app/socket.io/" "http://127.0.0.1:5000/socket.io/"

    RequestHeader set Upgrade "websocket"
    RequestHeader set Connection "upgrade"

    # --- API Backend ---
    ProxyPass /app/ http://127.0.0.1:5000/
    ProxyPassReverse /app/ http://127.0.0.1:5000/

    # --- OnlyOffice ---
    ProxyPass "/onlyoffice/" "http://127.0.0.1:8080/"
    ProxyPassReverse "/onlyoffice/" "http://127.0.0.1:8080/"

    ProxyPass "/onlyoffice/" "ws://127.0.0.1:8080/"
    ProxyPassReverse "/onlyoffice/" "ws://127.0.0.1:8080/"

    # --- Frontend ---
    ProxyPass / http://127.0.0.1:3000/
    ProxyPassReverse / http://127.0.0.1:3000/

    # --- Performance Settings ---
    ProxyTimeout 600
    Timeout 600
    ProxyBadHeader Ignore
    SetEnv proxy-nokeepalive 1
    SetEnv proxy-sendchunked 1
    ProxyIOBufferSize 65536

    # Security Headers
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self' https: data: blob:; connect-src 'self' https: wss:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:;"
</VirtualHost>

