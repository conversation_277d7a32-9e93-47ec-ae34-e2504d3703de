'use client';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Dialog, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  Shield,
  Clock,
} from 'lucide-react';

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  subDepartment?: string;
  position?: string;
  phoneNumber?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  reportingManager?: {
    _id: string;
    name: string;
  };
}

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

export const UserProfileModal: React.FC<UserProfileModalProps> = ({
  isOpen,
  onClose,
  user,
}) => {
  if (!user) return null;

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'Super Admin':
        return 'bg-red-500';
      case 'Admin':
        return 'bg-orange-500';
      case 'Approver':
        return 'bg-purple-500';
      case 'Reviewer':
        return 'bg-blue-500';
      case 'Creator':
        return 'bg-green-500';
      case 'Publisher':
        return 'bg-indigo-500';
      case 'Viewer':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusBadgeColor = (isActive: boolean) => {
    return isActive ? 'bg-green-500' : 'bg-red-500';
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'Creator':
        return 'Document owners from each department - responsible for creating and maintaining policy documents';
      case 'Reviewer':
        return 'Directors, Chiefs, Chief GRC - review policy documents for accuracy and compliance';
      case 'Approver':
        return 'Director, Chief, CEO, Board of Directors - approve policies based on document type and classification';
      case 'Publisher':
        return 'Governance Department - publish approved policies to the repository and manage distribution';
      case 'Viewer':
        return 'All Golf Saudi employees - can view published policies and documents';
      case 'Admin':
        return 'System administrator with elevated privileges for user and system management';
      case 'Super Admin':
        return 'Full system access including user management, system configuration, and all policy operations';
      default:
        return 'Standard user access';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPrimitive.Portal>
        {/* Transparent overlay */}
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black opacity-40" />
        {/* Custom content without default overlay */}
        <DialogPrimitive.Content
          className={cn(
            'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed left-[50%] top-[50%] z-50 grid max-h-[90vh] w-full translate-x-[-50%] translate-y-[-50%] gap-4 overflow-y-auto border bg-background p-6 shadow-lg duration-200 sm:max-w-[700px] sm:rounded-lg',
          )}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>User Profile</span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Basic Information Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Basic Information</span>
                  <div className="flex space-x-2">
                    <Badge
                      className={`text-white ${getRoleBadgeColor(user.role)}`}
                    >
                      {user.role}
                    </Badge>
                    <Badge
                      className={`text-white ${getStatusBadgeColor(user.isActive)}`}
                    >
                      {user.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Full Name
                      </p>
                      <p className="font-medium">{user.name}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Email Address
                      </p>
                      <p className="font-medium">{user.email}</p>
                    </div>
                  </div>

                  {user.phoneNumber && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">
                          Phone Number
                        </p>
                        <p className="font-medium">{user.phoneNumber}</p>
                      </div>
                    </div>
                  )}

                  {user.position && (
                    <div className="flex items-center space-x-3">
                      <Building className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">
                          Position
                        </p>
                        <p className="font-medium">{user.position}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Department Information Card */}
            <Card>
              <CardHeader>
                <CardTitle>Department Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Department
                    </p>
                    <p className="font-medium">{user.department}</p>
                  </div>

                  {user.subDepartment && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Sub-Department
                      </p>
                      <p className="font-medium">{user.subDepartment}</p>
                    </div>
                  )}

                  {user.reportingManager && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Reporting Manager
                      </p>
                      <p className="font-medium">
                        {user.reportingManager.name}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Role & Permissions Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Role & Permissions</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="mb-2 text-sm font-medium text-gray-500">
                    Role Description
                  </p>
                  <p className="text-sm leading-relaxed text-gray-700">
                    {getRoleDescription(user.role)}
                  </p>
                </div>

                <Separator />

                <div>
                  <p className="mb-2 text-sm font-medium text-gray-500">
                    Workflow Responsibilities
                  </p>
                  <div className="space-y-2">
                    {user.role === 'Creator' && (
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span className="text-sm">
                          Create and draft policy documents
                        </span>
                      </div>
                    )}
                    {user.role === 'Reviewer' && (
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                        <span className="text-sm">
                          Review policy documents for accuracy and compliance
                        </span>
                      </div>
                    )}
                    {user.role === 'Approver' && (
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                        <span className="text-sm">
                          Approve policies based on document classification
                        </span>
                      </div>
                    )}
                    {user.role === 'Publisher' && (
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
                        <span className="text-sm">
                          Publish approved policies to repository
                        </span>
                      </div>
                    )}
                    {user.role === 'Viewer' && (
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                        <span className="text-sm">
                          View published policies and documents
                        </span>
                      </div>
                    )}
                    {(user.role === 'Admin' || user.role === 'Super Admin') && (
                      <>
                        <div className="flex items-center space-x-2">
                          <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                          <span className="text-sm">
                            Manage users and system configuration
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                          <span className="text-sm">
                            Override workflow stages and assignments
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Activity Information Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>Activity Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Account Created
                      </p>
                      <p className="font-medium">
                        {formatDate(user.createdAt)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Last Login
                      </p>
                      <p className="font-medium">
                        {user.lastLogin
                          ? formatDate(user.lastLogin)
                          : 'Never logged in'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Close button */}
          <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
  );
};
