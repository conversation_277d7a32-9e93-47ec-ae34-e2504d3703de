'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyRetirementModal } from '@/components/PolicyRetirementModal';
import { PolicyExceptionModal } from '@/components/PolicyExceptionModal';
import { PolicyRequestModal } from '@/components/PolicyRequestModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import {
  Plus,
  Archive,
  AlertTriangle,
  Send,
  BarChart3,
  ArrowRight,
} from 'lucide-react';

const Requests = () => {
  const [isInitiationModalOpen, setIsInitiationModalOpen] = useState(false);
  const [isRetirementModalOpen, setIsRetirementModalOpen] = useState(false);
  const [isExceptionModalOpen, setIsExceptionModalOpen] = useState(false);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch all policies for request management
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    requestPolicyInitiation,
    requestRetirement,
    requestException,
  } = usePolicies({
    autoFetch: false,
    management: true,
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Handle policy initiation request submission
  const handleInitiationRequest = async (policyData: {
    name: string;
    description?: string;
    documentName?: string;
    documentCode?: string;
    documentType?: string;
    version?: string;
    policyCategory: 'Corporate Policies' | 'Operational Policies';
    policyType?: string;
    categories: string[];
    department: string;
    subDepartment?: string;
    priorityScore?: number;
    classification?: string;
    priority?: string;
    dueDate?: string;
    startDate?: string;
    nextReviewDate?: string;
  }) => {
    try {
      const result = await requestPolicyInitiation(policyData);

      if (result) {
        toast({
          title: 'Success',
          description: 'Policy initiation request submitted successfully',
        });
        refetchPolicies();
        setIsInitiationModalOpen(false);
      } else {
        toast({
          title: 'Error',
          description:
            'Failed to submit policy initiation request. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error requesting policy initiation:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to submit policy initiation request';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle policy retirement request submission
  const handleRetirementRequest = async (retirementData: {
    policyId: string;
    justification: string;
    effectiveDate?: string;
    comments?: string;
  }) => {
    try {
      const success = await requestRetirement(
        retirementData.policyId,
        retirementData.justification,
        retirementData.effectiveDate,
        retirementData.comments,
      );

      if (success) {
        toast({
          title: 'Success',
          description: 'Policy retirement request submitted successfully',
        });
        refetchPolicies();
        setIsRetirementModalOpen(false);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to submit retirement request. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error requesting retirement:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to submit retirement request';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle exception submission
  const handleExceptionSubmit = async (data: {
    policyId: string;
    justification: string;
    specificSection: string;
    exceptionType: 'Material Exception' | 'Immaterial Exception';
    effectiveDate?: string;
    expiryDate?: string;
    comments?: string;
  }) => {
    try {
      const success = await requestException(
        data.policyId,
        data.justification,
        data.specificSection,
        data.exceptionType,
        data.effectiveDate,
        data.expiryDate,
        data.comments,
      );

      if (success) {
        toast({
          title: 'Success',
          description: 'Exception request submitted successfully',
        });
        setIsExceptionModalOpen(false);
        refetchPolicies();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to submit exception request',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An error occurred while submitting the exception request',
        variant: 'destructive',
      });
    }
  };

  // Calculate request statistics
  const requestStats = {
    initiation: policies.filter((p) => p.status === 'Request Initiated').length,
    retirement: policies.filter((p) =>
      ['Retirement Requested', 'Retirement Pending Approval'].includes(
        p.status,
      ),
    ).length,
    exception: policies.filter(
      (p) =>
        p.exceptionStatus &&
        ['Requested', 'Pending Approval'].includes(p.exceptionStatus),
    ).length,
    total: policies.filter(
      (p) =>
        p.status === 'Request Initiated' ||
        ['Retirement Requested', 'Retirement Pending Approval'].includes(
          p.status,
        ) ||
        (p.exceptionStatus &&
          ['Requested', 'Pending Approval'].includes(p.exceptionStatus)),
    ).length,
  };

  // Request type cards
  const requestTypeCards = [
    {
      title: 'Policy Initiation Requests',
      count: requestStats.initiation,
      icon: Plus,
      color: 'blue',
      description: 'New policy creation requests',
      actionText: 'Request New Policy',
      action: () => setIsInitiationModalOpen(true),
      canRequest: ['Creator', 'Admin', 'Super Admin'].includes(
        user?.role || '',
      ),
    },
    {
      title: 'Policy Retirement Requests',
      count: requestStats.retirement,
      icon: Archive,
      color: 'orange',
      description: 'Policy retirement requests',
      actionText: 'Request Retirement',
      action: () => setIsRetirementModalOpen(true),
      canRequest: ['Creator', 'Admin', 'Super Admin'].includes(
        user?.role || '',
      ),
    },
    {
      title: 'Policy Exception Requests',
      count: requestStats.exception,
      icon: AlertTriangle,
      color: 'purple',
      description: 'Policy exception requests',
      actionText: 'Request Exception',
      action: () => setIsExceptionModalOpen(true),
      canRequest: ['Creator', 'Admin', 'Super Admin'].includes(
        user?.role || '',
      ),
    },
  ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading requests data..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="w-full bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Requests"
          message="We couldn't load the requests data. Please try again or contact support if the problem persists."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="w-full bg-gray-100 py-2">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">
              Policy Requests
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage all types of policy requests and submit new requests
            </p>
          </div>
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-blue-700">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="h-6 w-6" />
                  <span>Requests Overview</span>
                </div>
                <div className="flex flex-col items-end">
                  <div className="text-3xl font-bold text-blue-700">
                    {policiesLoading ? '...' : requestStats.total}
                  </div>
                  <div className="text-sm text-gray-500">active requests</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                All policy-related requests across initiation, retirement, and
                exception workflows
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Request Type Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Request Types
          </h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {requestTypeCards.map((card, index) => (
              <Card
                key={index}
                className={`border-l-4 transition-shadow hover:shadow-md ${
                  card.color === 'blue'
                    ? 'border-l-blue-500 bg-blue-50'
                    : card.color === 'orange'
                      ? 'border-l-orange-500 bg-orange-50'
                      : 'border-l-purple-500 bg-purple-50'
                }`}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-sm font-semibold text-gray-700">
                    <div className="flex items-center space-x-2">
                      <card.icon
                        className={`h-5 w-5 ${
                          card.color === 'blue'
                            ? 'text-blue-600'
                            : card.color === 'orange'
                              ? 'text-orange-600'
                              : 'text-purple-600'
                        }`}
                      />
                      <span className="truncate">{card.title}</span>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center justify-between">
                      <p
                        className={`text-2xl font-bold ${
                          card.color === 'blue'
                            ? 'text-blue-600'
                            : card.color === 'orange'
                              ? 'text-orange-600'
                              : 'text-purple-600'
                        }`}
                      >
                        {policiesLoading ? '...' : card.count}
                      </p>
                      <Send
                        className={`h-4 w-4 ${
                          card.color === 'blue'
                            ? 'text-blue-400'
                            : card.color === 'orange'
                              ? 'text-orange-400'
                              : 'text-purple-400'
                        }`}
                      />
                    </div>
                    <p className="text-xs leading-tight text-gray-600">
                      {card.description}
                    </p>
                    {card.canRequest && (
                      <Button
                        onClick={card.action}
                        className={`w-full text-sm ${
                          card.color === 'blue'
                            ? 'bg-blue-600 hover:bg-blue-700'
                            : card.color === 'orange'
                              ? 'bg-orange-600 hover:bg-orange-700'
                              : 'bg-purple-600 hover:bg-purple-700'
                        }`}
                      >
                        {card.actionText}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      <PolicyRequestModal
        isOpen={isInitiationModalOpen}
        onClose={() => setIsInitiationModalOpen(false)}
        onSubmit={handleInitiationRequest}
        loading={policiesLoading}
      />

      <PolicyRetirementModal
        isOpen={isRetirementModalOpen}
        onClose={() => setIsRetirementModalOpen(false)}
        onSubmit={handleRetirementRequest}
        loading={policiesLoading}
        publishedPolicies={policies.filter((p) => p.status === 'Published')}
      />

      <PolicyExceptionModal
        isOpen={isExceptionModalOpen}
        onClose={() => setIsExceptionModalOpen(false)}
        policies={policies.filter((p) => p.status === 'Published')}
        onSubmit={handleExceptionSubmit}
        isSubmitting={policiesLoading}
      />
    </>
  );
};

export default Requests;
