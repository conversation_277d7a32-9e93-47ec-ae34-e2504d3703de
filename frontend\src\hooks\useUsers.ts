'use client';

import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

const API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api`;

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  subDepartment?: string;
  position?: string;
  phoneNumber?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  reportingManager?: {
    _id: string;
    name: string;
  };
}

interface UserFilters {
  page?: number;
  limit?: number;
  role?: string;
  department?: string;
  isActive?: boolean;
  search?: string;
}

interface UserResponse {
  success: boolean;
  data: {
    users: User[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  };
}

interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role: string;
  department: string;
  subDepartment?: string;
  position?: string;
  phoneNumber?: string;
}

interface UpdateUserData {
  name?: string;
  email?: string;
  password?: string;
  role?: string;
  department?: string;
  subDepartment?: string;
  position?: string;
  phoneNumber?: string;
}

export const useUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
  });
  const { toast } = useToast();

  const getAuthHeaders = () => {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  };

  const handleApiError = (error: any) => {
    console.error('API Error:', error);
    const message =
      error.response?.data?.message || error.message || 'An error occurred';
    setError(message);
    toast({
      title: 'Error',
      description: message,
      variant: 'destructive',
    });
    return message;
  };

  // Fetch users with filters
  const fetchUsers = useCallback(
    async (filters: UserFilters = {}) => {
      setLoading(true);
      setError(null);

      try {
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(key, value.toString());
          }
        });

        const response = await fetch(`${API_BASE_URL}/users?${queryParams}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: UserResponse = await response.json();

        if (data.success) {
          setUsers(data.data.users);
          setPagination(data.data.pagination);
        } else {
          throw new Error('Failed to fetch users');
        }
      } catch (error) {
        handleApiError(error);
      } finally {
        setLoading(false);
      }
    },
    [toast],
  );

  // Create new user
  const createUser = useCallback(
    async (userData: CreateUserData): Promise<User | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`${API_BASE_URL}/users`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(userData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || `HTTP error! status: ${response.status}`,
          );
        }

        const data = await response.json();

        if (data.success) {
          toast({
            title: 'Success',
            description: 'User created successfully',
          });

          // Refresh users list
          await fetchUsers();
          return data.data;
        } else {
          throw new Error(data.message || 'Failed to create user');
        }
      } catch (error) {
        handleApiError(error);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [toast, fetchUsers],
  );

  // Update user
  const updateUser = useCallback(
    async (userId: string, userData: UpdateUserData): Promise<User | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(userData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || `HTTP error! status: ${response.status}`,
          );
        }

        const data = await response.json();

        if (data.success) {
          toast({
            title: 'Success',
            description: 'User updated successfully',
          });

          // Update local state
          setUsers((prev) =>
            prev.map((user) =>
              user._id === userId ? { ...user, ...data.data } : user,
            ),
          );

          return data.data;
        } else {
          throw new Error(data.message || 'Failed to update user');
        }
      } catch (error) {
        handleApiError(error);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [toast],
  );

  // Toggle user status (activate/deactivate)
  const toggleUserStatus = useCallback(
    async (userId: string): Promise<boolean> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
          method: 'DELETE', // This is actually a soft delete (deactivate)
          headers: getAuthHeaders(),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.message || `HTTP error! status: ${response.status}`,
          );
        }

        const data = await response.json();

        if (data.success) {
          toast({
            title: 'Success',
            description: data.message || 'User status updated successfully',
          });

          // Update local state
          setUsers((prev) =>
            prev.map((user) =>
              user._id === userId
                ? { ...user, isActive: !user.isActive }
                : user,
            ),
          );

          return true;
        } else {
          throw new Error(data.message || 'Failed to update user status');
        }
      } catch (error) {
        handleApiError(error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [toast],
  );

  // Get user by ID
  const getUserById = useCallback(
    async (userId: string): Promise<User | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          return data.data;
        } else {
          throw new Error('Failed to fetch user');
        }
      } catch (error) {
        handleApiError(error);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [toast],
  );

  // Get users by department (for reporting manager selection)
  const getUsersByDepartment = useCallback(
    async (department: string): Promise<User[]> => {
      try {
        const response = await fetch(
          `${API_BASE_URL}/users?department=${encodeURIComponent(department)}&isActive=true`,
          {
            method: 'GET',
            headers: getAuthHeaders(),
          },
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: UserResponse = await response.json();

        if (data.success) {
          return data.data.users;
        } else {
          throw new Error('Failed to fetch users by department');
        }
      } catch (error) {
        console.error('Error fetching users by department:', error);
        return [];
      }
    },
    [],
  );

  // Get users by role
  const getUsersByRole = useCallback(async (role: string): Promise<User[]> => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/users?role=${encodeURIComponent(role)}&isActive=true`,
        {
          method: 'GET',
          headers: getAuthHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: UserResponse = await response.json();

      if (data.success) {
        return data.data.users;
      } else {
        throw new Error('Failed to fetch users by role');
      }
    } catch (error) {
      console.error('Error fetching users by role:', error);
      return [];
    }
  }, []);

  return {
    users,
    loading,
    error,
    pagination,
    fetchUsers,
    createUser,
    updateUser,
    toggleUserStatus,
    getUserById,
    getUsersByDepartment,
    getUsersByRole,
  };
};
