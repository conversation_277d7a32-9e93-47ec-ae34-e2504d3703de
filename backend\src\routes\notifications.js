const express = require('express');
const router = express.Router();
const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  archiveNotification,
  getPreferences,
  updatePreferences,
  createNotification,
} = require('../controllers/notificationController');

const { protect, authorize } = require('../middleware/auth');
const { validateNotification } = require('../middleware/validation');

// All routes require authentication
router.use(protect);

// User notification routes
router.route('/')
  .get(getNotifications)
  .post(authorize('Admin', 'Super Admin'), validateNotification, createNotification);

router.get('/unread-count', getUnreadCount);
router.put('/read-all', markAllAsRead);

// Notification preferences
router.route('/preferences')
  .get(getPreferences)
  .put(updatePreferences);

// Individual notification routes
router.route('/:id')
  .delete(deleteNotification);

router.put('/:id/read', markAsRead);
router.put('/:id/archive', archiveNotification);

module.exports = router;
