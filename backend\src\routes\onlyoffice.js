const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getConfig,
  getDocumentContent,
  handleCallback,
  createDocument
} = require('../controllers/onlyofficeController');

// Get OnlyOffice configuration for a document
router.get('/config/:policyId', protect, getConfig);

// Get document content for OnlyOffice
router.get('/documents/:policyId/content', getDocumentContent);

// Handle callback from OnlyOffice
router.post('/callback/:policyId', handleCallback);

// Create new document
router.post('/documents/:policyId/create', protect, createDocument);

module.exports = router;
