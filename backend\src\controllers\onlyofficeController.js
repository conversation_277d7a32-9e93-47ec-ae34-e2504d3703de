const jwt = require("jsonwebtoken");
const path = require("path");
const fs = require("fs-extra");
const { v4: uuidv4 } = require("uuid");
const Policy = require("../models/Policy");
const {
  createDocumentFromTemplate,
  getMimeType,
} = require("../utils/documentTemplates");

// Get OnlyOffice configuration for a document
const getConfig = async (req, res) => {
  try {
    const { policyId } = req.params;

    // Get policy with attachment
    const policy = await Policy.findById(policyId);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: "Policy not found",
      });
    }

    // Check if policy has an attachment
    if (!policy.attachments || policy.attachments.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No document attached to this policy",
      });
    }

    const attachment = policy.attachments[0];
    const filename = attachment.filename;
    const fileExt = path.extname(attachment.originalName).substring(1);

    // Generate consistent document key for tracking changes (must be simple string without special characters)
    // Use policy ID and attachment filename hash for consistency across sessions
    const documentKey = `${policy._id}_${attachment.filename.replace(/[^a-zA-Z0-9]/g, '_')}`;

    // Determine document type
    let documentType = "word";
    if (["xlsx", "xls"].includes(fileExt)) {
      documentType = "cell";
    } else if (["pptx", "ppt"].includes(fileExt)) {
      documentType = "slide";
    }

    // Base URL for API endpoints - OnlyOffice needs to call back to the backend service
    // For UAT/production: OnlyOffice container calls backend container directly
    // For development: OnlyOffice container calls backend container directly
    const backendUrl = process.env.NODE_ENV === "production"
      ? "http://backend:5000"  // Internal container communication in UAT
      : "http://backend:5000"; // Internal container communication in dev
    
    console.log('OnlyOffice config - Backend URL for callbacks:', backendUrl);
    console.log('OnlyOffice config - Document key:', documentKey);

    // OnlyOffice configuration with aggressive performance optimizations
    const config = {
      document: {
        fileType: fileExt,
        key: documentKey,
        title: attachment.originalName,
        url: `${backendUrl}/api/onlyoffice/documents/${policyId}/content`,
        permissions: {
          edit: policy.status === "Draft",
          download: true,
          review: true,
          print: true,
          comment: true,
        },
        // Add file info for better caching
        info: {
          owner: req.user.name,
          uploaded: attachment.uploadedAt || new Date(),
          favorite: false
        }
      },
      documentType,
      editorConfig: {
        callbackUrl: `${backendUrl}/api/onlyoffice/callback/${policyId}`,
        lang: "en",
        mode: policy.status === "Draft" ? "edit" : "view",
        user: {
          id: req.user._id,
          name: req.user.name,
        },
        customization: {
          autosave: true,
          forcesave: false, // Disable forcesave for better performance
          chat: false,
          comments: true,
          compactToolbar: true, // Use compact toolbar for faster loading
          feedback: false,
          help: false, // Disable help for faster loading
          // Performance optimizations
          hideRightMenu: true, // Hide right menu for faster loading
          hideRulers: true, // Hide rulers for faster loading
          unit: "cm",
          zoom: 100,
          // Additional performance settings
          spellcheck: false, // Disable spellcheck for faster loading
          trackChanges: false, // Disable track changes for faster loading
          showReviewChanges: false
        },
        // Performance and caching optimizations
        coEditing: {
          mode: "fast",
          change: false // Disable real-time changes for better performance
        },
        // Reduce network requests
        region: "en-US",
        // Additional performance settings
        recent: [],
        templates: [],
        // Disable plugins for faster loading
        plugins: {
          autostart: [],
          pluginsData: []
        }
      },
      height: "100%",
      width: "100%",
      // Performance settings
      type: "desktop",
      // Remove event handlers to reduce overhead
      events: {}
    };

    // Add JWT token if enabled
    if (process.env.ONLYOFFICE_JWT_SECRET) {
      config.token = jwt.sign(config, process.env.ONLYOFFICE_JWT_SECRET);
    }

    res.json({
      success: true,
      data: config,
    });
  } catch (error) {
    console.error("Error generating OnlyOffice config:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate editor configuration",
    });
  }
};

// Get document content for OnlyOffice
const getDocumentContent = async (req, res) => {
  const startTime = Date.now();
  try {
    const { policyId } = req.params;
    console.log(`[PERFORMANCE] Document content request started for policy: ${policyId}`);

    // Get policy with attachment
    const dbStartTime = Date.now();
    const policy = await Policy.findById(policyId);
    console.log(`[PERFORMANCE] Database query took: ${Date.now() - dbStartTime}ms`);

    if (!policy || !policy.attachments || policy.attachments.length === 0) {
      console.log(`[PERFORMANCE] Document not found for policy: ${policyId}`);
      return res.status(404).json({
        success: false,
        message: "Document not found",
      });
    }

    const attachment = policy.attachments[0];
    const filePath = path.join(
      __dirname,
      "../../uploads/policies",
      attachment.filename
    );

    console.log(`[PERFORMANCE] File path resolved: ${filePath}`);

    const fileCheckStart = Date.now();
    if (!fs.existsSync(filePath)) {
      console.log(`[PERFORMANCE] File not found on disk: ${filePath}`);
      return res.status(404).json({
        success: false,
        message: "Document file not found",
      });
    }
    console.log(`[PERFORMANCE] File existence check took: ${Date.now() - fileCheckStart}ms`);

    // Get file stats for performance optimization
    const statsStartTime = Date.now();
    const stats = await fs.stat(filePath);
    console.log(`[PERFORMANCE] File stats took: ${Date.now() - statsStartTime}ms, file size: ${stats.size} bytes`);
    
    // Set appropriate headers with caching and performance optimizations
    res.setHeader("Content-Type", attachment.mimeType);
    res.setHeader("Content-Length", stats.size);
    res.setHeader(
      "Content-Disposition",
      `inline; filename="${attachment.originalName}"`
    );
    
    // Add aggressive caching headers for better performance
    res.setHeader("Cache-Control", "public, max-age=86400, immutable"); // Cache for 24 hours
    res.setHeader("ETag", `"${stats.mtime.getTime()}-${stats.size}"`);
    res.setHeader("Last-Modified", stats.mtime.toUTCString());
    
    // Handle conditional requests for better performance
    const ifModifiedSince = req.headers['if-modified-since'];
    const ifNoneMatch = req.headers['if-none-match'];
    
    if (ifModifiedSince && new Date(ifModifiedSince) >= stats.mtime) {
      console.log(`[PERFORMANCE] Returning 304 Not Modified (if-modified-since), total time: ${Date.now() - startTime}ms`);
      return res.status(304).end();
    }
    
    if (ifNoneMatch && ifNoneMatch === `"${stats.mtime.getTime()}-${stats.size}"`) {
      console.log(`[PERFORMANCE] Returning 304 Not Modified (etag), total time: ${Date.now() - startTime}ms`);
      return res.status(304).end();
    }

    console.log(`[PERFORMANCE] Starting file stream for ${attachment.filename}`);
    
    // Stream the file with optimized buffer size
    const fileStream = fs.createReadStream(filePath, {
      highWaterMark: 128 * 1024 // Increased to 128KB buffer for better performance
    });
    
    fileStream.on('error', (error) => {
      console.error('[PERFORMANCE] Error streaming document:', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Failed to stream document content",
        });
      }
    });
    
    fileStream.on('end', () => {
      console.log(`[PERFORMANCE] Document content delivery completed in: ${Date.now() - startTime}ms`);
    });
    
    fileStream.pipe(res);
  } catch (error) {
    console.error(`[PERFORMANCE] Error getting document content (${Date.now() - startTime}ms):`, error);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve document content",
    });
  }
};

// Handle callback from OnlyOffice
const handleCallback = async (req, res) => {
  try {
    const { policyId } = req.params;
    const { status, url } = req.body;

    console.log("OnlyOffice callback received:", {
      policyId,
      status,
      url,
      body: req.body
    });

    // Get policy
    const policy = await Policy.findById(policyId);

    if (!policy || !policy.attachments || policy.attachments.length === 0) {
      console.error("Policy or attachment not found for callback:", policyId);
      return res.json({ error: 0 }); // OnlyOffice expects error: 0 for success
    }

    // Handle document saving (status 2)
    if (status === 2 && url) {
      try {
        const axios = require("axios");
        const attachment = policy.attachments[0];
        const filePath = path.join(
          __dirname,
          "../../uploads/policies",
          attachment.filename
        );
        
        // Convert external URL to internal container URL for different environments
        let internalUrl = url;
        
        console.log('OnlyOffice callback - Original URL:', url);
        
        // For UAT/production environment - use internal container communication
        if (process.env.NODE_ENV === 'production') {
          // In UAT, OnlyOffice container is named 'onlyoffice' and runs on port 80 internally
          // Replace any external domain with internal container name
          const urlObj = new URL(url);
          internalUrl = `http://onlyoffice:80${urlObj.pathname}${urlObj.search || ''}`;
        } else {
          // For development environment
          if (url.includes('localhost:8080')) {
            internalUrl = url.replace('localhost:8080', 'onlyoffice:80');
          } else {
            // Fallback: use container name for any external URL
            const urlObj = new URL(url);
            internalUrl = `http://onlyoffice:80${urlObj.pathname}${urlObj.search || ''}`;
          }
        }
        
        console.log('OnlyOffice callback URL conversion:', { original: url, internal: internalUrl });
        
        // Download updated document
        console.log('Downloading updated document from OnlyOffice...');
        const response = await axios.get(internalUrl, {
          responseType: "stream",
          timeout: 30000,
          headers: {
            'User-Agent': 'GRC-Backend/1.0'
          }
        });
        
        console.log('Document download response status:', response.status);
        const writeStream = fs.createWriteStream(filePath);
        response.data.pipe(writeStream);

        await new Promise((resolve, reject) => {
          writeStream.on("finish", resolve);
          writeStream.on("error", reject);
        });

        // Update attachment metadata
        const stats = await fs.stat(filePath);
        attachment.size = stats.size;
        attachment.uploadedAt = new Date();

        // Increment version if versioning is enabled
        if (!policy.version) policy.version = "1.0";
        if (!policy.versionNumber) policy.versionNumber = 1.0;

        policy.versionNumber =
          Math.round((policy.versionNumber + 0.1) * 10) / 10;
        policy.version = policy.versionNumber.toFixed(1);

        // Add to version history if it doesn't exist
        if (!policy.versionHistory) policy.versionHistory = [];

        policy.versionHistory.push({
          version: policy.version,
          modifiedBy: {
            id: req.body.users?.[0]?.id || "system",
            name: req.body.users?.[0]?.name || "System",
          },
          modifiedAt: new Date(),
          size: stats.size,
        });

        await policy.save();

        console.log("Document saved successfully:", {
          policyId,
          version: policy.version,
          size: stats.size,
          filename: attachment.filename
        });
      } catch (saveError) {
        console.error("Error saving document:", {
          error: saveError.message,
          stack: saveError.stack,
          policyId,
          url,
          internalUrl
        });
        return res.json({ error: 1 });
      }
    }

    // OnlyOffice expects a JSON response with error: 0 for success
    res.json({ error: 0 });
  } catch (error) {
    console.error("Callback error:", error);
    res.json({ error: 1 });
  }
};

// Create new document
const createDocument = async (req, res) => {
  try {
    const { policyId } = req.params;
    const { title, type = "docx" } = req.body;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: "Document title is required",
      });
    }

    // Get policy
    const policy = await Policy.findById(policyId);

    if (!policy) {
      return res.status(404).json({
        success: false,
        message: "Policy not found",
      });
    }

    // Check if policy can be edited
    if (policy.status !== "Draft" && policy.status !== "Request Initiated") {
      return res.status(400).json({
        success: false,
        message: "Policy must be in Draft status to create a document",
      });
    }

    // Generate unique filename
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const filename = `policy-${uniqueSuffix}.${type}`;
    const uploadDir = path.join(__dirname, "../../uploads/policies");
    const filePath = path.join(uploadDir, filename);

    // Ensure upload directory exists
    await fs.ensureDir(uploadDir);

    // Create document from template
    await createDocumentFromTemplate(filePath, type, title);

    // Get file stats
    const stats = await fs.stat(filePath);

    // Create attachment object
    const attachment = {
      filename,
      originalName: `${title}.${type}`,
      url: `/uploads/policies/${filename}`,
      size: stats.size,
      mimeType: getMimeType(type),
      uploadedAt: new Date(),
    };

    // Replace existing attachment (only one policy document allowed)
    policy.attachments = [attachment];

    // Initialize version if not exists
    if (!policy.version) policy.version = "1.0";
    if (!policy.versionNumber) policy.versionNumber = 1.0;

    // Initialize version history if not exists
    if (!policy.versionHistory) policy.versionHistory = [];

    policy.versionHistory.push({
      version: policy.version,
      modifiedBy: {
        id: req.user._id,
        name: req.user.name,
      },
      modifiedAt: new Date(),
      size: stats.size,
      action: "created",
    });

    await policy.save();

    res.status(201).json({
      success: true,
      message: "Document created successfully",
      data: {
        policyId: policy._id,
        attachment,
      },
    });
  } catch (error) {
    console.error("Error creating document:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create document",
    });
  }
};

module.exports = {
  getConfig,
  getDocumentContent,
  handleCallback,
  createDocument,
};
