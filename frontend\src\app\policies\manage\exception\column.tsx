'use client';

import * as React from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export type ExceptionPrograms = {
  id: number;
  policyId: string;
  documentCode: string;
  name: string;
  documentName: string;
  documentType: string;
  version: string;
  policyType: string;
  categories: string;
  department: string;
  subDepartment: string;
  policyOwner: string;
  priorityScore: number;
  status: string;
  detailedStatus: string;
  exceptionStatus: string;
  classification: string;
  publishedDate: string;
  exceptionRequestedDate: string;
  exceptionApprovedDate: string;
  exceptionJustification: string;
  exceptionSpecificSection: string;
  exceptionType: string;
  exceptionEffectiveDate: string;
  exceptionExpiryDate: string;
  exceptionRequestedBy: string;
};

const columnHelper = createColumnHelper<ExceptionPrograms>();

export const columns = [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }),
  // 1. Policy ID - Unique identifier
  columnHelper.accessor('policyId', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy ID
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2 text-sm font-medium text-customBlueSecondary">
          <span>{row.getValue('policyId')}</span>
        </div>
      );
    },
    enableHiding: false,
  }),
  // 2. Policy Name - Main identifier
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy Name
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[200px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('name')}
      >
        {row.getValue('name')}
      </div>
    ),
    enableHiding: false,
  }),

  // 3. Document Type - Critical for categorization
  columnHelper.accessor('documentType', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Type</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('documentType') || 'Policy'}
      </div>
    ),
    enableHiding: false,
  }),
  // 4. Department - Ownership and responsibility
  columnHelper.accessor('department', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Department
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('department')}
      </div>
    ),
    enableHiding: false,
  }),

  // 5. Policy Owner - Key stakeholder
  columnHelper.accessor('policyOwner', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Owner</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[120px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('policyOwner')}
      >
        {row.getValue('policyOwner')}
      </div>
    ),
    enableHiding: false,
  }),

  // 6. Exception Status - Current exception workflow stage
  columnHelper.accessor('exceptionStatus', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Exception Status
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const exceptionStatus = row.getValue('exceptionStatus') as string;
      const getExceptionStatusColor = (status: string) => {
        switch (status) {
          case 'Requested':
            return 'text-orange-700 bg-orange-100';
          case 'Under Review':
            return 'text-blue-700 bg-blue-100';
          case 'Pending Approval':
            return 'text-yellow-700 bg-yellow-100';
          case 'Approved':
            return 'text-green-700 bg-green-100';
          case 'Rejected':
            return 'text-red-700 bg-red-100';
          case 'Expired':
            return 'text-gray-700 bg-gray-100';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getExceptionStatusColor(exceptionStatus || 'N/A')}`}
        >
          {exceptionStatus || 'N/A'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 7. Exception Type - Material vs Immaterial
  columnHelper.accessor('exceptionType', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Exception Type
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const exceptionType = row.getValue('exceptionType') as string;
      const getExceptionTypeColor = (type: string) => {
        switch (type) {
          case 'Material Exception':
            return 'text-red-700 bg-red-100';
          case 'Immaterial Exception':
            return 'text-blue-700 bg-blue-100';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getExceptionTypeColor(exceptionType || 'Immaterial Exception')}`}
        >
          {exceptionType || 'N/A'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 8. Published Date - When policy was published
  columnHelper.accessor('publishedDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Published Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const publishedDate = row.getValue('publishedDate') as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {publishedDate ? new Date(publishedDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 9. Exception Requested Date - When exception was requested
  columnHelper.accessor('exceptionRequestedDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Exception Requested
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const exceptionRequestedDate = row.getValue(
        'exceptionRequestedDate',
      ) as string;
      return (
        <div className="text-sm font-medium text-customBlueSecondary">
          {exceptionRequestedDate
            ? new Date(exceptionRequestedDate).toLocaleDateString()
            : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 10. Exception Requested By - Who requested exception
  columnHelper.accessor('exceptionRequestedBy', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Requested By
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[120px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('exceptionRequestedBy')}
      >
        {row.getValue('exceptionRequestedBy') || 'N/A'}
      </div>
    ),
    enableHiding: true,
  }),

  // 11. Exception Effective Date - When exception becomes effective
  columnHelper.accessor('exceptionEffectiveDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Effective Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const effectiveDate = row.getValue('exceptionEffectiveDate') as string;
      const isUpcoming = effectiveDate && new Date(effectiveDate) > new Date();
      return (
        <div
          className={`text-sm font-medium ${isUpcoming ? 'text-orange-600' : 'text-customBlueSecondary'}`}
        >
          {effectiveDate ? new Date(effectiveDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 12. Exception Expiry Date - When exception expires
  columnHelper.accessor('exceptionExpiryDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Expiry Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const expiryDate = row.getValue('exceptionExpiryDate') as string;
      const isExpired = expiryDate && new Date(expiryDate) < new Date();
      return (
        <div
          className={`text-sm font-medium ${isExpired ? 'text-red-600' : 'text-customBlueSecondary'}`}
        >
          {expiryDate ? new Date(expiryDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 13. Classification - Security level
  columnHelper.accessor('classification', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Classification
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const classification = row.getValue('classification') as string;
      const getClassificationColor = (classification: string) => {
        switch (classification) {
          case 'Top Secret':
            return 'text-red-700 bg-red-100';
          case 'Restricted':
            return 'text-red-600 bg-red-50';
          case 'Confidential':
            return 'text-orange-600 bg-orange-50';
          case 'Internal':
            return 'text-blue-600 bg-blue-50';
          case 'Public':
            return 'text-green-600 bg-green-50';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getClassificationColor(classification || 'Internal')}`}
        >
          {classification || 'Internal'}
        </div>
      );
    },
    enableHiding: true,
  }),

  // 14. Version - Document version tracking
  columnHelper.accessor('version', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Version
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('version') || '1.0'}
      </div>
    ),
    enableHiding: true,
  }),
];
