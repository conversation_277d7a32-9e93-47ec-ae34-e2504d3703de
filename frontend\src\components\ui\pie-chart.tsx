'use client';

import * as React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { ChartContainer } from './chart';
import { cn } from '@/lib/utils';

// Monochromatic blue color palette from user's specification
const MONOCHROME_BLUE_COLORS = [
  '#3b82f6', // blue-500 (primary)
  '#60a5fa', // blue-400 (lighter)
  '#2563eb', // blue-600 (darker)
  '#1d4ed8', // blue-700 (darkest)
  '#93c5fd', // blue-300 (lightest)
  '#1e40af', // blue-800 (very dark)
  '#dbeafe', // blue-200 (very light)
  '#1e3a8a', // blue-900 (very dark)
  '#bfdbfe', // blue-200 (alternate light)
  '#3730a3', // indigo-800 (very dark alternate)
  '#312e81', // indigo-900 (darkest alternate)
];

// Alternative monochrome palettes
const MONOCHROME_PURPLE_COLORS = [
  '#8b5cf6', // violet-500
  '#a78bfa', // violet-400
  '#7c3aed', // violet-600
  '#6d28d9', // violet-700
  '#c4b5fd', // violet-300
  '#5b21b6', // violet-800
  '#ddd6fe', // violet-200
  '#4c1d95', // violet-900
];

const MONOCHROME_GREEN_COLORS = [
  '#10b981', // emerald-500
  '#34d399', // emerald-400
  '#059669', // emerald-600
  '#047857', // emerald-700
  '#6ee7b7', // emerald-300
  '#065f46', // emerald-800
  '#a7f3d0', // emerald-200
  '#064e3b', // emerald-900
];

interface PieChartData {
  name: string;
  value: number;
  fill?: string;
}

interface CustomPieChartProps {
  data: PieChartData[];
  className?: string;
  innerRadius?: number;
  outerRadius?: number;
  showTooltip?: boolean;
  showLegend?: boolean;
  showLabels?: boolean;
  showPercentage?: boolean;
  colors?: string[];
  colorScheme?: 'blue' | 'purple' | 'green';
  title?: string;
  description?: string;
}

const CustomLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
  name,
  showPercentage = true,
}: any) => {
  if (percent < 0.08) return null; // Don't show labels for slices < 8%

  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.7;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  // Truncate long names
  const displayName = name.length > 12 ? name.substring(0, 10) + '...' : name;

  return (
    <g>
      <text
        x={x}
        y={showPercentage ? y - 4 : y}
        fill="white"
        textAnchor="middle"
        dominantBaseline="central"
        className="text-xs font-semibold"
        style={{ textShadow: '0 1px 3px rgba(0,0,0,0.9)' }}
      >
        {displayName}
      </text>
      {showPercentage && (
        <text
          x={x}
          y={y + 10}
          fill="white"
          textAnchor="middle"
          dominantBaseline="central"
          className="text-xs font-medium opacity-90"
          style={{ textShadow: '0 1px 3px rgba(0,0,0,0.9)' }}
        >
          {`${(percent * 100).toFixed(0)}%`}
        </text>
      )}
    </g>
  );
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="rounded-lg border bg-background p-3 shadow-lg">
        <p className="mb-1 font-semibold text-foreground">{data.name}</p>
        <div className="flex items-center gap-2">
          <div
            className="h-3 w-3 rounded-full"
            style={{ backgroundColor: data.color }}
          />
          <span className="text-sm text-muted-foreground">
            <span className="font-mono font-medium text-foreground">
              {data.value}
            </span>{' '}
            ({((data.value / payload[0].payload.total) * 100).toFixed(1)}%)
          </span>
        </div>
      </div>
    );
  }
  return null;
};

const CustomLegend = ({ payload }: any) => {
  if (!payload?.length) return null;

  return (
    <div className="mt-6 flex flex-wrap justify-center gap-4">
      {payload.map((entry: any, index: number) => (
        <div
          key={`legend-item-${index}`}
          className="flex items-center gap-2 text-sm"
        >
          <div
            className="h-3 w-3 rounded-full border border-border/20"
            style={{ backgroundColor: entry.color }}
          />
          <span className="font-medium text-foreground">{entry.value}</span>
          <span className="font-mono text-muted-foreground">
            ({entry.payload?.value || 0})
          </span>
        </div>
      ))}
    </div>
  );
};

export function CustomPieChart({
  data,
  className,
  innerRadius = 0,
  outerRadius = 80,
  showTooltip = true,
  showLegend = false, // Default to false for shadcn style
  showLabels = true,
  showPercentage = true,
  colors,
  colorScheme = 'blue',
  title,
  description,
}: CustomPieChartProps) {
  // Select color palette based on scheme
  const selectedColors = React.useMemo(() => {
    if (colors) return colors;

    switch (colorScheme) {
      case 'purple':
        return MONOCHROME_PURPLE_COLORS;
      case 'green':
        return MONOCHROME_GREEN_COLORS;
      case 'blue':
      default:
        return MONOCHROME_BLUE_COLORS;
    }
  }, [colors, colorScheme]);

  // Calculate total for percentage calculations
  const total = React.useMemo(
    () => data.reduce((sum, item) => sum + item.value, 0),
    [data],
  );

  // Enhance data with total for tooltip calculations
  const enhancedData = React.useMemo(
    () => data.map((item) => ({ ...item, total })),
    [data, total],
  );

  // Show empty state if no data
  if (!data || data.length === 0) {
    return (
      <div
        className={cn(
          'flex h-[300px] w-full items-center justify-center',
          className,
        )}
      >
        <div className="text-center">
          <p className="text-muted-foreground">No data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)}>
      {(title || description) && (
        <div className="mb-4">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}

      <ChartContainer className="mx-auto aspect-square max-h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={enhancedData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={
                showLabels
                  ? (props) => CustomLabel({ ...props, showPercentage })
                  : false
              }
              outerRadius={outerRadius}
              innerRadius={innerRadius}
              fill="#8884d8"
              dataKey="value"
              stroke="hsl(var(--background))"
              strokeWidth={1}
            >
              {enhancedData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    entry.fill || selectedColors[index % selectedColors.length]
                  }
                  className="transition-opacity duration-200 hover:opacity-90"
                />
              ))}
            </Pie>
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend content={<CustomLegend />} />}
          </PieChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  );
}

export default CustomPieChart;
