'use client';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { usePolicies } from '@/hooks/usePolicies';
import { useMyActions } from '@/hooks/useMyActions';
import { useEffect, useState } from 'react';
import {
  ChevronUp,
  ChevronDown,
  User,
  Home,
  LayoutDashboard,
  FolderKanban,
  Database,
  LogOut,
  Target,
  Plus,
  Archive,
  AlertTriangle,
  Send,
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuBadge,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarFooter,
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';

// Main menu items
const main = [
  {
    title: 'Home',
    url: '/all-apps',
    icon: Home,
  },
];

// Policy menu items
const policyItems = [
  {
    title: 'Dashboard',
    url: '/policies/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'My Actions',
    url: '/policies/my-actions',
    icon: Target,
  },
  {
    title: 'Policy Hub',
    url: '/policies/policyhub',
    icon: FolderKanban,
  },
  {
    title: 'Requests',
    url: '/policies/requests',
    icon: Send,
  },
  {
    title: 'Exception',
    url: '/policies/exception',
    icon: Send,
  },
  {
    title: 'User Management',
    url: '/policies/user-management',
    icon: User,
  },
];

// Manage submenu items
const manageSubItems = [
  {
    title: 'Policy Creation Workflow',
    url: '/policies/manage/creation',
    icon: Plus,
  },
  {
    title: 'Policy Retirement Workflow',
    url: '/policies/manage/retirement',
    icon: Archive,
  },
  {
    title: 'Policy Exception Workflow',
    url: '/policies/manage/exception',
    icon: AlertTriangle,
  },
  {
    title: 'Policy Review Workflow',
    url: '/policies/manage/review',
    icon: AlertTriangle,
  },
];

export function AppSidebar() {
  // Get current path from Next.js
  const pathname = usePathname();
  const { user, logout, isAuthenticated } = useAuth();

  // State for manage submenu
  const [isManageOpen, setIsManageOpen] = useState(false);

  // Fetch policies for urgent actions count
  const { policies, refetch: refetchPolicies } = usePolicies({
    autoFetch: false,
    myActions: true,
  });

  // Get urgent actions count
  const { hasUrgentActions, actionCategories } = useMyActions(policies, user);

  // Fetch policies when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      refetchPolicies();
    }
  }, [isAuthenticated, user, refetchPolicies]);

  // Check if manage section should be open based on current path
  useEffect(() => {
    if (pathname.startsWith('/policies/manage')) {
      setIsManageOpen(true);
    }
  }, [pathname]);

  // Helper function to check if link is active
  const isActiveLink = (url: string) => {
    return pathname.startsWith(url);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <Sidebar>
      {/* CONTENT */}
      <SidebarContent className="bg-white">
        {/* GROUP 1 */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {main.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`hover:bg-blue-50 ${
                      isActiveLink(item.url) ? 'bg-blue-100 font-bold' : ''
                    }`}
                  >
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}

              {/* Example of single item with badge */}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* POLICY */}
        <SidebarGroup>
          <SidebarGroupLabel className="px-2 text-xs font-medium uppercase text-sidebar-foreground/70">
            Policy
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {policyItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`hover:bg-blue-50 ${
                      isActiveLink(item.url) ? 'bg-blue-100 font-bold' : ''
                    }`}
                  >
                    <Link href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                      {/* Show urgent actions badge for My Actions */}
                      {item.title === 'My Actions' && hasUrgentActions && (
                        <SidebarMenuBadge className="bg-red-500 text-white">
                          {actionCategories.urgent.length}
                        </SidebarMenuBadge>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}

              {/* Manage Section with Submenu */}
              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={() => setIsManageOpen(!isManageOpen)}
                  className={`hover:bg-blue-50 ${
                    pathname.startsWith('/policies/manage')
                      ? 'bg-blue-100 font-bold'
                      : ''
                  }`}
                >
                  <Database />
                  <span>Manage</span>
                  {isManageOpen ? (
                    <ChevronDown className="ml-auto h-4 w-4" />
                  ) : (
                    <ChevronUp className="ml-auto h-4 w-4" />
                  )}
                </SidebarMenuButton>

                {isManageOpen && (
                  <SidebarMenuSub>
                    {manageSubItems.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.title}>
                        <SidebarMenuButton
                          asChild
                          className={`hover:bg-blue-50 ${
                            isActiveLink(subItem.url)
                              ? 'bg-blue-100 font-bold'
                              : ''
                          }`}
                        >
                          <Link href={subItem.url}>
                            <subItem.icon />
                            <span>{subItem.title}</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                )}
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      {/* FOOTER */}
      <SidebarFooter className="bg-white">
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton className="flex items-center gap-2 p-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex flex-col items-start text-left">
                    <span className="text-sm font-medium">
                      {user?.name || 'User'}
                    </span>
                    <span className="text-xs text-gray-500">
                      {user?.role || 'Role'}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto h-4 w-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="top"
                className="w-[--radix-popper-anchor-width]"
              >
                <div className="px-2 py-1.5 text-sm text-gray-700">
                  <div className="font-medium">{user?.name || 'User'}</div>
                  <div className="text-xs text-gray-500">
                    {user?.email || '<EMAIL>'}
                  </div>
                  <div className="text-xs text-gray-400">
                    {user?.department || 'Department'}
                  </div>
                </div>
                <DropdownMenuItem
                  className="flex items-center gap-2 text-red-600 hover:bg-red-50 hover:text-red-700"
                  onClick={handleLogout}
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
