'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useRef,
} from 'react';
import { useAuth } from './AuthContext';
import { io, Socket } from 'socket.io-client';

// Notification interface
interface Notification {
  _id: string;
  title: string;
  message: string;
  type: 'policy_workflow' | 'user_management' | 'system' | 'reminder';
  category:
    | 'approval_required'
    | 'status_change'
    | 'assignment'
    | 'due_date'
    | 'system_update';
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  data?: {
    policyId?: string;
    policyName?: string;
    actionRequired?: boolean;
    dueDate?: string;
  };
  sender?: {
    name: string;
    email: string;
  };
}

// Context interface
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  loading: boolean;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  fetchNotifications: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

// Custom hook to use notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      'useNotifications must be used within a NotificationProvider',
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const { user, isAuthenticated } = useAuth();
  const socketRef = useRef<Socket | null>(null);

  // Calculate unread count
  const unreadCount = notifications.filter((n) => !n.isRead).length;

  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    if (!isAuthenticated || !user) return;

    try {
      setLoading(true);
      const { notificationApi } = await import('@/lib/api');
      const response = await notificationApi.getNotifications({ limit: 50 });

      if (response.success && response.data) {
        setNotifications(response.data.notifications || []);
      } else {
        console.error('Failed to fetch notifications:', response.error);
        // Fallback to mock data for development
        setNotifications(getMockNotifications());
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      // Fallback to mock data for development
      setNotifications(getMockNotifications());
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  // Mark notification as read
  const markAsRead = useCallback(async (id: string) => {
    try {
      const { notificationApi } = await import('@/lib/api');
      const response = await notificationApi.markAsRead(id);

      if (response.success) {
        setNotifications((prev) =>
          prev.map((n) => (n._id === id ? { ...n, isRead: true } : n)),
        );
      } else {
        console.error('Failed to mark notification as read:', response.error);
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      // Optimistically update UI
      setNotifications((prev) =>
        prev.map((n) => (n._id === id ? { ...n, isRead: true } : n)),
      );
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const { notificationApi } = await import('@/lib/api');
      const response = await notificationApi.markAllAsRead();

      if (response.success) {
        setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
      } else {
        console.error(
          'Failed to mark all notifications as read:',
          response.error,
        );
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      // Optimistically update UI
      setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
    }
  }, []);

  // Delete notification
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const { notificationApi } = await import('@/lib/api');
      const response = await notificationApi.deleteNotification(id);

      if (response.success) {
        setNotifications((prev) => prev.filter((n) => n._id !== id));
      } else {
        console.error('Failed to delete notification:', response.error);
      }
    } catch (error) {
      console.error('Failed to delete notification:', error);
      // Optimistically update UI
      setNotifications((prev) => prev.filter((n) => n._id !== id));
    }
  }, []);

  // Initialize WebSocket connection
  const initializeSocket = useCallback(() => {
    if (!isAuthenticated || !user || socketRef.current) return;

    const token = localStorage.getItem('token');
    if (!token) return;

    const socketUrl =
      process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000';

    socketRef.current = io(socketUrl, {
      auth: { token },
      transports: ['websocket', 'polling'],
      // CRITICAL: Set the correct path that matches Apache routing
      path: '/app/socket.io/',
      // Additional resilience settings
      forceNew: true,
      timeout: 20000,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      reconnectionDelayMax: 5000,
    });

    socketRef.current.on('connect', () => {
      console.log('Connected to notification service');
      setIsConnected(true);
    });

    socketRef.current.on('disconnect', () => {
      console.log('Disconnected from notification service');
      setIsConnected(false);
    });

    socketRef.current.on('new_notification', (notification: Notification) => {
      console.log('New notification received:', notification);
      setNotifications((prev) => [notification, ...prev]);

      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
        });
      }
    });

    socketRef.current.on(
      'unread_count_update',
      (data: { unreadCount: number }) => {
        console.log('Unread count updated:', data.unreadCount);
        // The unread count is calculated from the notifications array
      },
    );

    socketRef.current.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setIsConnected(false);
    });
  }, [isAuthenticated, user]);

  // Cleanup socket connection
  const cleanupSocket = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  }, []);

  // Request browser notification permission
  useEffect(() => {
    if (isAuthenticated && user && 'Notification' in window) {
      if (Notification.permission === 'default') {
        Notification.requestPermission().then((permission) => {
          console.log('Notification permission:', permission);
        });
      }
    }
  }, [isAuthenticated, user]);

  // Initialize notifications when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchNotifications();
      initializeSocket();
    } else {
      setNotifications([]);
      cleanupSocket();
      setLoading(false);
    }

    // Cleanup on unmount
    return () => {
      cleanupSocket();
    };
  }, [
    isAuthenticated,
    user,
    fetchNotifications,
    initializeSocket,
    cleanupSocket,
  ]);

  // Mock data for development
  const getMockNotifications = (): Notification[] => [
    {
      _id: '1',
      title: 'Policy Approval Required',
      message: 'HR Data Privacy Policy requires your approval',
      type: 'policy_workflow',
      category: 'approval_required',
      isRead: false,
      priority: 'high',
      createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      data: {
        policyId: 'pol_123',
        policyName: 'HR Data Privacy Policy',
        actionRequired: true,
      },
      sender: {
        name: 'John Doe',
        email: '<EMAIL>',
      },
    },
    {
      _id: '2',
      title: 'Policy Status Update',
      message: 'IT Security Policy has been published',
      type: 'policy_workflow',
      category: 'status_change',
      isRead: false,
      priority: 'medium',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      data: {
        policyId: 'pol_456',
        policyName: 'IT Security Policy',
        actionRequired: false,
      },
    },
    {
      _id: '3',
      title: 'Review Due Soon',
      message: 'Finance Expense Policy review due in 3 days',
      type: 'reminder',
      category: 'due_date',
      isRead: true,
      priority: 'medium',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      data: {
        policyId: 'pol_789',
        policyName: 'Finance Expense Policy',
        dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3).toISOString(),
      },
    },
  ];

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    loading,
    markAsRead,
    markAllAsRead,
    fetchNotifications,
    deleteNotification,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
