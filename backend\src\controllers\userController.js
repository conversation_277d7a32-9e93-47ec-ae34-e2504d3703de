const User = require("../models/User");

// @desc    Get all users
// @route   GET /api/users
// @access  Private (Admin only)
const getAllUsers = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      role,
      department,
      isActive,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
      position,
      subDepartment,
    } = req.query;

    // Build dynamic query object
    const query = {};

    // Exact match filters
    if (role && role !== "all") query.role = role;
    if (department && department !== "all") query.department = department;
    if (position && position !== "all") query.position = position;
    if (subDepartment && subDepartment !== "all")
      query.subDepartment = subDepartment;

    // Boolean filter for active status
    if (isActive !== undefined && isActive !== "all") {
      query.isActive = isActive === "true";
    }

    // Text search with multiple fields
    if (search && search.trim()) {
      const searchRegex = { $regex: search.trim(), $options: "i" };
      query.$or = [
        { name: searchRegex },
        { email: searchRegex },
        { position: searchRegex },
        { subDepartment: searchRegex },
        { phoneNumber: searchRegex },
      ];
    }

    // Pagination setup
    const pageNum = Math.max(1, parseInt(page, 10));
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10))); // Limit max results to 100
    const skip = (pageNum - 1) * limitNum;

    // Dynamic sorting
    const sortOptions = {};
    const validSortFields = [
      "name",
      "email",
      "role",
      "department",
      "createdAt",
      "lastLogin",
      "isActive",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    const sortDirection = sortOrder === "asc" ? 1 : -1;
    sortOptions[sortField] = sortDirection;

    // Execute query with population for related fields
    const users = await User.find(query)
      .select("-password")
      .populate("reportingManager", "name email")
      .sort(sortOptions)
      .skip(skip)
      .limit(limitNum)
      .lean(); // Use lean() for better performance

    // Get total count for pagination
    const total = await User.countDocuments(query);

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    res.status(200).json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage,
          hasPrevPage,
        },
        filters: {
          role,
          department,
          isActive,
          search,
          sortBy: sortField,
          sortOrder,
        },
      },
    });
  } catch (error) {
    console.error("Error in getAllUsers:", error);
    next(error);
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private (Admin only)
const getUserById = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id).select("-password");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new user
// @route   POST /api/users
// @access  Private (Admin only)
const createUser = async (req, res, next) => {
  try {
    const user = await User.create(req.body);
    user.password = undefined;

    res.status(201).json({
      success: true,
      data: user,
      message: "User created successfully",
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private (Admin only)
const updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    }).select("-password");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.status(200).json({
      success: true,
      data: user,
      message: "User updated successfully",
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private (Admin only)
const deleteUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Soft delete by setting isActive to false
    user.isActive = false;
    await user.save();

    res.status(200).json({
      success: true,
      message: "User deactivated successfully",
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
};
