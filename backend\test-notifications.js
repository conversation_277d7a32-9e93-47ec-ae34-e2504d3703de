const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grc-web-app');

const Notification = require('./src/models/Notification');
const User = require('./src/models/User');

async function createTestNotifications() {
  try {
    console.log('🔔 Creating test notifications...');

    // Find a test user
    const user = await User.findOne({ email: '<EMAIL>' });
    if (!user) {
      console.log('❌ No test user found. Please create a user first.');
      return;
    }

    console.log(`📧 Found user: ${user.name} (${user.email})`);

    // Create test notifications
    const testNotifications = [
      {
        recipient: {
          id: user._id,
          email: user.email,
          role: user.role,
        },
        type: 'policy_workflow',
        category: 'approval_required',
        title: 'Policy Approval Required',
        message: 'HR Data Privacy Policy requires your approval',
        priority: 'high',
        data: {
          policyName: 'HR Data Privacy Policy',
          actionRequired: true,
          actionUrl: '/policies/manage/test-policy-1',
        },
      },
      {
        recipient: {
          id: user._id,
          email: user.email,
          role: user.role,
        },
        type: 'policy_workflow',
        category: 'status_change',
        title: 'Policy Status Update',
        message: 'IT Security Policy has been published',
        priority: 'medium',
        data: {
          policyName: 'IT Security Policy',
          actionRequired: false,
          actionUrl: '/policies/view/test-policy-2',
        },
      },
      {
        recipient: {
          id: user._id,
          email: user.email,
          role: user.role,
        },
        type: 'reminder',
        category: 'due_date',
        title: 'Review Due Soon',
        message: 'Finance Expense Policy review due in 3 days',
        priority: 'medium',
        data: {
          policyName: 'Finance Expense Policy',
          dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
          actionRequired: true,
          actionUrl: '/policies/manage/test-policy-3',
        },
      },
      {
        recipient: {
          id: user._id,
          email: user.email,
          role: user.role,
        },
        type: 'system',
        category: 'system_update',
        title: 'System Maintenance',
        message: 'Scheduled maintenance tonight from 2 AM to 4 AM',
        priority: 'low',
        data: {
          actionRequired: false,
        },
      },
    ];

    // Create notifications
    for (const notificationData of testNotifications) {
      const notification = await Notification.create(notificationData);
      console.log(`✅ Created notification: ${notification.title}`);
    }

    console.log('🎉 Test notifications created successfully!');

    // Get notification count
    const count = await Notification.countDocuments({ 'recipient.id': user._id });
    console.log(`📊 Total notifications for ${user.name}: ${count}`);

    // Get unread count
    const unreadCount = await Notification.countDocuments({ 
      'recipient.id': user._id, 
      isRead: false 
    });
    console.log(`🔔 Unread notifications: ${unreadCount}`);

  } catch (error) {
    console.error('❌ Error creating test notifications:', error);
  } finally {
    mongoose.connection.close();
  }
}

createTestNotifications();
