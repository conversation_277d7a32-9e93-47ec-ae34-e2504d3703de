'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

// Chart container component
const ChartContainer = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    config?: Record<string, { label: string; color?: string }>;
  }
>(({ className, config, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex aspect-square justify-center text-xs', className)}
    {...props}
  />
));
ChartContainer.displayName = 'ChartContainer';

// Chart tooltip component
const ChartTooltip = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('rounded-lg border bg-background p-2 shadow-md', className)}
    {...props}
  />
));
ChartTooltip.displayName = 'ChartTooltip';

// Chart tooltip content component
const ChartTooltipContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    active?: boolean;
    payload?: any[];
    label?: string;
    hideLabel?: boolean;
    hideIndicator?: boolean;
    indicator?: 'line' | 'dot' | 'dashed';
    nameKey?: string;
    labelKey?: string;
  }
>(
  (
    {
      active,
      payload,
      className,
      indicator = 'dot',
      hideLabel = false,
      hideIndicator = false,
      label,
      labelKey,
      nameKey,
      ...props
    },
    ref,
  ) => {
    if (!active || !payload?.length) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn(
          'grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl',
          className,
        )}
        {...props}
      >
        {!hideLabel && label && (
          <p className="font-medium text-foreground">{label}</p>
        )}
        <div className="grid gap-1.5">
          {payload.map((item, index) => {
            const key = `${labelKey || item.dataKey || item.name || 'value'}-${index}`;
            const itemConfig = item.payload;
            const indicatorColor = item.color || itemConfig?.color || '#8884d8';

            return (
              <div
                key={key}
                className="flex w-full items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground"
              >
                {!hideIndicator && (
                  <div
                    className="shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]"
                    style={
                      {
                        '--color-bg': indicatorColor,
                        '--color-border': indicatorColor,
                      } as React.CSSProperties
                    }
                  >
                    {indicator === 'dot' && (
                      <div className="h-2.5 w-2.5 rounded-full" />
                    )}
                    {indicator === 'line' && (
                      <div className="h-2.5 w-0.5 rounded-full" />
                    )}
                    {indicator === 'dashed' && (
                      <div className="h-2.5 w-0.5 rounded-full opacity-50" />
                    )}
                  </div>
                )}
                <div className="flex flex-1 justify-between leading-none">
                  <div className="grid gap-1.5">
                    <span className="text-muted-foreground">
                      {nameKey ? item.payload?.[nameKey] : item.name}
                    </span>
                  </div>
                  <span className="font-mono font-medium tabular-nums text-foreground">
                    {item.value}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  },
);
ChartTooltipContent.displayName = 'ChartTooltipContent';

// Chart legend component
const ChartLegend = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center justify-center gap-4', className)}
    {...props}
  />
));
ChartLegend.displayName = 'ChartLegend';

// Chart legend content component
const ChartLegendContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    payload?: any[];
    nameKey?: string;
    hideIcon?: boolean;
  }
>(({ className, payload, nameKey, hideIcon = false, ...props }, ref) => {
  if (!payload?.length) {
    return null;
  }

  return (
    <div
      ref={ref}
      className={cn(
        'flex flex-wrap items-center justify-center gap-4 text-sm',
        className,
      )}
      {...props}
    >
      {payload.map((item, index) => {
        const key = `${nameKey || item.dataKey || item.value}-${index}`;
        const itemConfig = item.payload;
        const indicatorColor = item.color || itemConfig?.color || '#8884d8';

        return (
          <div key={key} className="flex items-center gap-1.5">
            {!hideIcon && (
              <div
                className="h-2 w-2 shrink-0 rounded-[2px]"
                style={{
                  backgroundColor: indicatorColor,
                }}
              />
            )}
            <span className="text-muted-foreground">
              {nameKey ? item.payload?.[nameKey] : item.value}
            </span>
          </div>
        );
      })}
    </div>
  );
});
ChartLegendContent.displayName = 'ChartLegendContent';

export {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
};
