/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { useMemo } from 'react';

import { usePolicies, usePolicyStats } from '@/hooks/usePolicies';
import { Policy } from '@/lib/api';
import { FileText, Tag, Building2, AlertTriangle } from 'lucide-react';
import { CustomPieChart } from '@/components/ui/pie-chart';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';

// Shadcn-style Horizontal Bar Chart Component for Review Due Status
const ReviewDueBarChart = ({
  data,
}: {
  data: { name: string; value: number; color: string }[];
}) => {
  const maxValue = Math.max(...data.map((item) => item.value));

  return (
    <div className="space-y-3">
      {data.map((item) => {
        const percentage = maxValue > 0 ? (item.value / maxValue) * 100 : 0;
        return (
          <div key={item.name} className="flex items-center gap-3">
            <div className="relative flex-1">
              <div
                className="flex h-8 items-center rounded-md px-3 text-sm font-medium text-white transition-all duration-300 ease-in-out"
                style={{
                  width: `${Math.max(percentage, 15)}%`, // Minimum 15% width for label visibility
                  backgroundColor: item.color,
                }}
              >
                {item.name}
              </div>
            </div>
            <div className="min-w-[3rem] text-right text-sm font-bold text-gray-900">
              {item.value}
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Shadcn-style Vertical Bar Chart Component for Policy Age Distribution
const PolicyAgeBarChart = ({
  data,
}: {
  data: { name: string; value: number }[];
}) => {
  const maxValue = Math.max(...data.map((item) => item.value), 1); // Ensure maxValue is at least 1

  return (
    <div className="flex h-64 items-end justify-between gap-2 px-4">
      {data.map((item) => {
        // Calculate height with better scaling
        const percentage = maxValue > 0 ? (item.value / maxValue) * 80 : 0; // Use 80% of container height
        const height = item.value > 0 ? Math.max(percentage, 15) : 8; // Minimum 15% for values > 0, 8% for zero values

        return (
          <div
            key={item.name}
            className="flex max-w-[80px] flex-1 flex-col items-center"
          >
            <div className="mb-2 flex h-6 items-center text-sm font-bold text-gray-900">
              {item.value}
            </div>
            <div className="flex w-full justify-center">
              <div
                className={`w-12 rounded-t-md transition-all duration-300 ease-in-out ${
                  item.value > 0
                    ? 'bg-purple-500 hover:bg-purple-600'
                    : 'bg-gray-200'
                }`}
                style={{ height: `${height * 2.5}px` }} // Convert percentage to pixels for better control
              />
            </div>
            <div className="mt-2 text-center text-xs leading-tight text-gray-600">
              {item.name}
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Chart summary component
const ChartSummary = ({
  data,
  title,
  colorScheme = 'blue',
}: {
  data: { name: string; value: number }[];
  title: string;
  colorScheme?: 'blue' | 'purple' | 'green';
}) => {
  // Color mapping for indicators
  const colorMap = {
    blue: '#0ea5e9',
    purple: '#8b5cf6',
    green: '#10b981',
  };

  return (
    <div className="mt-4 space-y-3">
      <p className="text-sm text-muted-foreground">
        Showing current distribution across all {title.toLowerCase()}
      </p>

      {/* Compact legend */}
      <div className="flex flex-wrap gap-3 text-xs">
        {data.slice(0, 4).map((item) => (
          <div key={item.name} className="flex items-center gap-1.5">
            <div
              className="h-2 w-2 rounded-full"
              style={{ backgroundColor: colorMap[colorScheme] }}
            />
            <span className="font-medium text-foreground">{item.name}</span>
            <span className="text-muted-foreground">({item.value})</span>
          </div>
        ))}
        {data.length > 4 && (
          <span className="text-muted-foreground">+{data.length - 4} more</span>
        )}
      </div>
    </div>
  );
};

// Helper function to transform policies data for charts
const transformPoliciesForCharts = (policies: Policy[]) => {
  // Categories distribution for pie chart
  const categoryCounts: Record<string, number> = {};

  // Status distribution for overdue chart
  const statusCounts: Record<string, number> = {};

  // Classification distribution for bar chart
  const classificationByCategory: Record<string, Record<string, number>> = {};

  // Department distribution
  const departmentCounts: Record<string, number> = {};

  policies.forEach((policy) => {
    // Count categories
    policy.categories.forEach((category) => {
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    // Count status
    statusCounts[policy.status] = (statusCounts[policy.status] || 0) + 1;

    // Count departments
    departmentCounts[policy.department] =
      (departmentCounts[policy.department] || 0) + 1;

    // Count classification by category
    policy.categories.forEach((category) => {
      if (!classificationByCategory[category]) {
        classificationByCategory[category] = {};
      }
      const classification = policy.classification;
      classificationByCategory[category][classification] =
        (classificationByCategory[category][classification] || 0) + 1;
    });
  });

  return {
    categoryCounts,
    statusCounts,
    departmentCounts,
    classificationByCategory,
  };
};

const Dashboard = () => {
  // Fetch published and archived policies data
  const {
    policies: publishedPolicies,
    loading: publishedLoading,
    error: publishedError,
  } = usePolicies({
    status: 'Published',
    autoFetch: true,
  });

  const {
    policies: archivedPolicies,
    loading: archivedLoading,
    error: archivedError,
  } = usePolicies({
    status: 'Archived',
    autoFetch: true,
  });

  // Fetch policy statistics
  const { loading: statsLoading, error: statsError } = usePolicyStats();

  // Combine published and archived policies
  const allPolicies = useMemo(() => {
    return [...publishedPolicies, ...archivedPolicies];
  }, [publishedPolicies, archivedPolicies]);

  // Transform data for charts
  const chartData = useMemo(() => {
    if (!allPolicies.length) {
      return {
        categoryCounts: {},
        statusCounts: {},
        departmentCounts: {},
        classificationByCategory: {},
      };
    }
    return transformPoliciesForCharts(allPolicies);
  }, [allPolicies]);

  // Prepare data for pie charts
  const categoriesData = useMemo(() => {
    return Object.entries(chartData.categoryCounts).map(([name, value]) => ({
      name,
      value,
    }));
  }, [chartData.categoryCounts]);

  const statusData = useMemo(() => {
    return [
      {
        name: 'Published',
        value: publishedPolicies.length,
      },
      {
        name: 'Archived',
        value: archivedPolicies.length,
      },
    ];
  }, [publishedPolicies.length, archivedPolicies.length]);

  // Review Due Status Data - Monochrome Orange Scheme
  const reviewDueData = useMemo(() => {
    const now = new Date();
    const thirtyDays = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const sixtyDays = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000);
    const ninetyDays = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);

    const overdue = publishedPolicies.filter(
      (p) => p.nextReviewDate && new Date(p.nextReviewDate) < now,
    ).length;
    const dueIn30 = publishedPolicies.filter(
      (p) =>
        p.nextReviewDate &&
        new Date(p.nextReviewDate) >= now &&
        new Date(p.nextReviewDate) <= thirtyDays,
    ).length;
    const dueIn60 = publishedPolicies.filter(
      (p) =>
        p.nextReviewDate &&
        new Date(p.nextReviewDate) > thirtyDays &&
        new Date(p.nextReviewDate) <= sixtyDays,
    ).length;
    const dueIn90 = publishedPolicies.filter(
      (p) =>
        p.nextReviewDate &&
        new Date(p.nextReviewDate) > sixtyDays &&
        new Date(p.nextReviewDate) <= ninetyDays,
    ).length;
    const future = publishedPolicies.filter(
      (p) => p.nextReviewDate && new Date(p.nextReviewDate) > ninetyDays,
    ).length;

    // Monochrome orange color scheme (darkest to lightest based on urgency)
    return [
      { name: 'Overdue', value: overdue, color: '#c2410c' }, // orange-700 (darkest - most urgent)
      { name: 'In 30 days', value: dueIn30, color: '#ea580c' }, // orange-600
      { name: 'In 60 days', value: dueIn60, color: '#f97316' }, // orange-500 (primary)
      { name: 'In 90 days', value: dueIn90, color: '#fb923c' }, // orange-400
      { name: 'Future reviews', value: future, color: '#fdba74' }, // orange-300 (lightest)
    ];
  }, [publishedPolicies]);

  // Policy Age Distribution Data
  const policyAgeData = useMemo(() => {
    const now = new Date();
    const ageRanges = {
      '0-30 days': 0,
      '31-90 days': 0,
      '91-180 days': 0,
      '181-365 days': 0,
      '1-2 years': 0,
      '2+ years': 0,
    };

    publishedPolicies.forEach((policy) => {
      if (policy.publishedDate) {
        const publishedDate = new Date(policy.publishedDate);
        const daysDiff = Math.floor(
          (now.getTime() - publishedDate.getTime()) / (1000 * 60 * 60 * 24),
        );

        if (daysDiff <= 30) {
          ageRanges['0-30 days']++;
        } else if (daysDiff <= 90) {
          ageRanges['31-90 days']++;
        } else if (daysDiff <= 180) {
          ageRanges['91-180 days']++;
        } else if (daysDiff <= 365) {
          ageRanges['181-365 days']++;
        } else if (daysDiff <= 730) {
          ageRanges['1-2 years']++;
        } else {
          ageRanges['2+ years']++;
        }
      }
    });

    return Object.entries(ageRanges).map(([name, value]) => ({ name, value }));
  }, [publishedPolicies]);

  const departmentData = useMemo(() => {
    return Object.entries(chartData.departmentCounts).map(([name, value]) => ({
      name,
      value,
    }));
  }, [chartData.departmentCounts]);

  // Transform policies data for DataTable
  // const transformedPolicies = useMemo(() => {
  //   return publishedPolicies.map((policy, index) => ({
  //     id: index,
  //     policyId: policy._id,
  //     name: policy.name,
  //     department: policy.department,
  //     categories: policy.categories,
  //     version: policy.version || '1.0',
  //     classification: policy.classification || 'Internal',
  //     priorityScore: policy.priorityScore || 5,
  //     documentCode: policy.documentCode,
  //   }));
  // }, [publishedPolicies]);

  if (publishedLoading || archivedLoading || statsLoading) {
    return (
      <LoadingDisplay
        message="Loading dashboard data..."
        variant="default"
        size="md"
      />
    );
  }

  if (publishedError || archivedError || statsError) {
    return (
      <ErrorDisplay
        title="Unable to Load Dashboard"
        message="We encountered an issue while loading your dashboard data. Please try again or contact support if the problem persists."
        error={publishedError || archivedError || statsError || undefined}
        onRetry={() => window.location.reload()}
        onGoBack={() => window.history.back()}
        showTechnicalDetails={true}
      />
    );
  }

  return (
    <div className="min-h-screen">
      <div>
        {/* Header */}
        <div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="mb-2 text-xl font-bold text-gray-900">
                Published & Archived Policies Dashboard
              </h1>
              <p className="text-gray-600">
                Overview of published and archived policies, their distribution,
                and status across the organization
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span className="h-2 w-2 rounded-full bg-green-500"></span>
                <span>Live Data</span>
              </div>
            </div>
          </div>
        </div>
        {/* Statistics Cards */}
        <div className="my-4 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          <div className="rounded-xl border bg-white p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Published Policies
                </p>
                <p className="mt-2 text-3xl font-bold text-blue-600">
                  {publishedPolicies.length}
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">Currently active</p>
          </div>

          <div className="rounded-xl border bg-white p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Overdue Reviews
                </p>
                <p className="mt-2 text-3xl font-bold text-red-600">
                  {reviewDueData.find((item) => item.name === 'Overdue')
                    ?.value || 0}
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Require immediate attention
            </p>
          </div>

          <div className="rounded-xl border bg-white p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="mt-2 text-3xl font-bold text-green-600">
                  {Object.keys(chartData.categoryCounts).length}
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <Tag className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">Policy categories</p>
          </div>

          <div className="rounded-xl border bg-white p-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Departments</p>
                <p className="mt-2 text-3xl font-bold text-purple-600">
                  {Object.keys(chartData.departmentCounts).length}
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
                <Building2 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">Involved departments</p>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Categories Chart */}
          <div className="rounded-xl border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-card-foreground">
                  Policies by Category
                </h3>
                <p className="text-sm text-muted-foreground">
                  Distribution across categories
                </p>
              </div>
            </div>
            <div className="h-80">
              <CustomPieChart
                data={categoriesData}
                showTooltip={true}
                showLegend={false}
                showLabels={true}
                showPercentage={false}
                outerRadius={85}
                colorScheme="blue"
              />
            </div>
            <ChartSummary
              data={categoriesData}
              title="Categories"
              colorScheme="blue"
            />
          </div>

          {/* Departments Chart */}
          <div className="rounded-xl border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-card-foreground">
                  Policies by Department
                </h3>
                <p className="text-sm text-muted-foreground">
                  Distribution across departments
                </p>
              </div>
            </div>
            <div className="h-80">
              <CustomPieChart
                data={departmentData}
                showTooltip={true}
                showLegend={false}
                showLabels={true}
                showPercentage={false}
                outerRadius={85}
                colorScheme="purple"
              />
            </div>
            <ChartSummary
              data={departmentData}
              title="Departments"
              colorScheme="purple"
            />
          </div>

          {/* Status Chart */}
          <div className="rounded-xl border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-card-foreground">
                  Published vs Archived
                </h3>
                <p className="text-sm text-muted-foreground">
                  Distribution of active and archived policies
                </p>
              </div>
            </div>
            <div className="h-80">
              <CustomPieChart
                data={statusData}
                showTooltip={true}
                showLegend={false}
                showLabels={true}
                showPercentage={false}
                outerRadius={85}
                colorScheme="green"
              />
            </div>
            <ChartSummary
              data={statusData}
              title="Policy Status"
              colorScheme="green"
            />
          </div>
        </div>

        {/* New Charts Row - Review Due & Policy Age */}
        <div className="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Review Due Status Chart */}
          <div className="rounded-xl border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-card-foreground">
                  Review Due Status
                </h3>
                <p className="text-sm text-muted-foreground">
                  Review timeline with urgency levels
                </p>
              </div>
            </div>
            <div className="flex h-80 items-center">
              <div className="w-full">
                <ReviewDueBarChart data={reviewDueData} />
              </div>
            </div>
            <div className="mt-6 space-y-2">
              <p className="text-sm text-muted-foreground">
                Showing review status for all published policies
              </p>
            </div>
          </div>

          {/* Policy Age Distribution Chart */}
          <div className="rounded-xl border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-card-foreground">
                  Policy Age Distribution
                </h3>
                <p className="text-sm text-muted-foreground">
                  Age distribution of published policies
                </p>
              </div>
            </div>
            <div className="flex h-80 items-center">
              <div className="w-full">
                <PolicyAgeBarChart data={policyAgeData} />
              </div>
            </div>
            <div className="mt-6 space-y-2">
              <p className="text-sm text-muted-foreground">
                Showing policy age distribution for content freshness analysis
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
