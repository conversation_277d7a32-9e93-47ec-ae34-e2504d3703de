'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingDisplayProps {
  message?: string;
  className?: string;
  variant?: 'default' | 'minimal' | 'inline';
  size?: 'sm' | 'md' | 'lg';
}

export function LoadingDisplay({
  message = 'Loading...',
  className,
  variant = 'default',
  size = 'md',
}: LoadingDisplayProps) {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
  };

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center justify-center py-8', className)}>
        <div className="flex flex-col items-center space-y-4">
          <Loader2
            className={cn('animate-spin text-blue-600', sizeClasses[size])}
          />
          <div
            className={cn('font-medium text-gray-600', textSizeClasses[size])}
          >
            {message}
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={cn('flex items-center gap-3 p-4', className)}>
        <Loader2 className={cn('animate-spin text-blue-600', sizeClasses.sm)} />
        <span className={cn('text-gray-600', textSizeClasses.sm)}>
          {message}
        </span>
      </div>
    );
  }

  // Default full-screen variant
  return (
    <div
      className={cn(
        'flex min-h-full items-center justify-center bg-gray-50 p-6',
        className,
      )}
    >
      <div className="w-full max-w-md">
        {/* Loading Card */}
        <div className="rounded-2xl border border-gray-200 bg-white p-8 text-center shadow-lg">
          {/* Loading Spinner */}
          <div className="mx-auto mb-6">
            <Loader2
              className={cn(
                'mx-auto animate-spin text-blue-600',
                sizeClasses[size],
              )}
            />
          </div>

          {/* Loading Message */}
          <h2
            className={cn(
              'mb-2 font-semibold text-gray-900',
              textSizeClasses[size],
            )}
          >
            {message}
          </h2>

          {/* Sub-message */}
          <p className="text-sm text-gray-600">
            Please wait while we fetch your data...
          </p>

          {/* Progress indicator */}
          <div className="mt-6">
            <div className="h-1 w-full rounded-full bg-gray-200">
              <div
                className="h-1 animate-pulse rounded-full bg-blue-600"
                style={{ width: '60%' }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LoadingDisplay;
